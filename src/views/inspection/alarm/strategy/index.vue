<template>
  <EleSheet ref="sheetRef" v-bind="sheetProps" class="page-main page-main--full">
    <template #before>
    </template>

    <template #table:isEnabled:simple="{ row }">
      <el-switch v-model="row.isEnabled" active-value="1" inactive-value="0" :disabled="!$checkPermi(['alarm:strategy:updateStatus'])" @change="onSwitchChange($event, row, 'isEnabled')"></el-switch>
    </template>

    <template #after> </template>
  </EleSheet>
</template>

<script>
import {
  addAlarmStrategy,
  delAlarmStrategy,
  getAlarmStrategy,
  listAlarmStrategy,
  updateAlarmStrategy
} from '@/api/inspection/alarm/strategy.js'

import request from '@/utils/request.js'

export default {
  dicts: ['alerm_level'],
  data() {
    return {}
  },
  computed: {
    sheetProps() {
      const value = {
        title: '告警策略',

        lazy: false,

        api: {
          add: (params) => addAlarmStrategy({ ...params }),
          edit: (params) => updateAlarmStrategy({ ...params }),
          list: async(params) => listAlarmStrategy({ ...params }),
          info: getAlarmStrategy,
          remove: delAlarmStrategy,
          export: '/res/strategy/export',
          import: '',
          template: ''
        },

        infoProps: {
          title: true
        },

        tableProps: {
          height: '100%'
        },

        hiddenActions: {
          add: !this.$checkPermi(['alarm:strategy:add']),
          edit: !this.$checkPermi(['alarm:strategy:edit']),
          remove: !this.$checkPermi(['alarm:strategy:remove']),
          export: !this.$checkPermi(['alarm:strategy:export'])
        },

        model: {
          policyName: {
            label: '策略名称',
            table: {
              align: 'left',
              width: 150
            }
          },
          alertItemName: {
            label: '告警项名称',
            table: {
              width: 150
            }
          },
          metricType: {
            label: '监控指标',
            search: {
            },
            table: {
              width: 150
            }
          },
          thresholdValue: {
            label: '触发阈值',
            search: {
              hidden: true
            },
            table: {
              width: 150
            }
          },
          alertTarget: {
            label: '告警目标',
            search: {
              hidden: true
            }
          },
          alertLevel: {
            label: '告警级别',
            table: {
              width: 150
            },
            type: 'select',
            options: this.dict.type.alerm_level
          },
          createTime: {
            type: 'text',
            label: '创建时间',
            search: {
              hidden: true,
              type: 'date',
              parameter: (data) => {
                return {
                  startTime: data?.[0],
                  endTime: data?.[1]
                }
              }
            },
            form: {
              hidden: true
            },
            width: 200
          },
          isEnabled: {
            label: '是否启用',
            type: 'select',
            options: [
              {
                label: '启用',
                value: '1'
              },
              {
                label: '禁用',
                value: '0'
              }
            ],
            from: {
              hidden: true
            },
            table: {
              tableColumnProps: {
                fixed: 'right'
              }
            }
          },
          alertInfo: {
            label: '告警信息',
            search: {
              hidden: true
            },
            table: {
              hidden: true
            },
            group: 'detail'
          }
        }
      }

      return value
    }
  },
  methods: {
    async onSwitchChange(value, row, field) {
      const params = {
        id: row.id,
        [field]: value
      }

      const res = await this.sheetProps.api.edit(params)

      if (res.code === 200) {
        this.$message.success(res.msg)
      } else {
        this.$message.waring(res.msg)
      }
    }
  }
}
</script>

<style></style>
