<template>
  <EleSheet ref="sheetRef" v-bind="sheetProps" class="page-main">
    <template #search:resPoolName:after="{ model }">
      <el-form-item label="IP">
        <el-input v-model="model.ip" placeholder="请输入" />
      </el-form-item>
    </template>

    <template #search:resPoolName="{ model, itemProps }">
      <el-form-item v-bind="{ ...itemProps }">
        <ResPoolSelect v-model="model.resPoolName" placeholder="请输入" />
      </el-form-item>
    </template>

    <template #form:resPoolName="{ model, colProps, itemProps }">
      <el-col v-bind="{ ...colProps }">
        <el-form-item v-bind="{ ...itemProps }">
          <ResPoolSelect v-model="model.resPoolName" placeholder="请输入" />
        </el-form-item>
      </el-col>
    </template>

    <template #table:status:simple="{ row }">
      <el-switch
        v-model="row.status"
        active-value="1"
        inactive-value="0"
        :disabled="!$checkPermi(['auto:fortress:updateStatus'])"
        @change="onSwitchChange($event, row)"
      />
    </template>
  </EleSheet>
</template>

<script>
import { listPools, getPools, addPools, updatePools, delPools } from '@/api/auto/fortress'

export default {
  data() {
    return {}
  },

  computed: {
    sheetProps() {
      const value = {
        title: '4A堡垒机',
        api: {
          add: (params) => addPools({ ...params }),
          edit: (params) => updatePools({ ...params }),
          list: listPools,
          info: getPools,
          remove: delPools,
          export: '/res/pools/export',
          template: '/res/pools/importTemplate'
        },
        tableProps: {
          selection: 'multiple'
        },
        infoProps: {
          title: true
        },
        hiddenActions: {
          add: !this.$checkPermi(['auto:fortress:add']),
          edit: !this.$checkPermi(['auto:fortress:edit']),
          remove: !this.$checkPermi(['auto:fortress:remove']),
          export: !this.$checkPermi(['auto:fortress:export'])
        },
        model: [
          {
            type: 'text',
            label: '4A堡垒前置',
            field: 'resPoolName4a',
            form: {
              rules: true
            }
          },
          {
            type: 'text',
            label: '磐基资源池',
            field: 'resPoolName'
          },
          {
            type: 'text',
            label: '管理网IP',
            field: 'managementIp',
            search: {
              hidden: true
            }
          },
          {
            type: 'text',
            label: '业务网IP',
            field: 'serviceIp',
            search: {
              hidden: true
            }
          },
          {
            type: 'text',
            label: '承载网IP',
            field: 'bearerIp',
            search: {
              hidden: true
            }
          },
          {
            type: 'switch',
            label: '启用状态',
            field: 'status',
            form: {
              hidden: true
            },
            search: {
              hidden: true
            },
            info: {
              formatter: (value) => (String(value) === '1' ? '已启用' : '未启用')
            }
          },
          {
            type: 'text',
            label: '创建时间',
            field: 'createTime',
            search: {
              type: 'date-range',
              parameter: (data) => {
                return {
                  startTime: data?.[0],
                  endTime: data?.[1]
                }
              }
            },
            form: {
              hidden: true
            },
            width: 200
          },
          {
            type: 'text',
            label: '修改时间',
            field: 'updateTime',
            search: {
              hidden: true
            },
            form: {
              hidden: true
            },
            width: 200
          }
        ]
      }

      return value
    }
  },
  methods: {
    async onSwitchChange(value, row) {
      const res = await updatePools({ id: row.id, status: value })
      if (res.code === 200) {
        this.$refs.sheetRef.getTableData()
      }
    }
  }
}
</script>

<style></style>
