<template>
  <EleSheet ref="sheetRef" v-bind="sheetProps" class="page-main">
    <template #toolbar:after="scope">
      <el-button
        v-if="$checkPermi(['auto:host:batch:association'])"
        type="primary"
        :disabled="!scope.selected"
        @click="handleAssociate(scope)"
      ><IconLink class="" />批量关联</el-button>
    </template>

    <template v-if="!selectMode" #table:action:before="{ row }">
      <el-button
        size="mini"
        type="text"
        :disabled="!$checkPermi(['auto:host:again:sending'])"
        @click="handleRetry(row)"
      >
        重新发送
      </el-button>
      <el-button
        size="mini"
        type="text"
        :disabled="!$checkPermi(['auto:host:upload:log'])"
        @click="$router.push({ path: '/auto/script', query: { pid: row.id } })"
      >
        上传日志
      </el-button>
    </template>
    <template #after>
      <AssociateDialog ref="associateDialogRef" />
    </template>
  </EleSheet>
</template>

<script>
import { listMaster, getMaster, addMaster, updateMaster, delMaster } from '@/api/auto/host.js'

import AssociateDialog from './AssociateDialog/index.vue'
import { dataMixin } from '@/mixins'

import {
  listNumber
} from '@/api/auto/account.js'
export default {
  props: {
    layout: {
      type: [String, Array],
      default: void 0
    },
    selectMode: {
      type: [Boolean, String],
      default: false
    },
    selectionRowKeys: {
      type: Array,
      default: () => []
    }
  },
  mixins: [
    dataMixin({
      primaryAccountList: {
        default: [],
        async load() {
          const res = await listNumber({
            pageNum: 1,
            pageSize: 200
          })

          const data = (res?.rows || []).map(item => ({
            ...item,
            value: item.id,
            label: item.userName4a
          }))

          return data
        }
      }
    })
  ],
  components: {
    AssociateDialog
  },
  data() {
    return {}
  },
  computed: {
    sheetProps() {
      const value = {
        title: '主机扫描管理',

        layout: this.layout,

        api: {
          add: (params) => addMaster({ ...params }),
          edit: (params) => updateMaster({ ...params }),
          list: listMaster,
          info: getMaster,
          remove: delMaster,
          export: '/res/master/export',
          import: 'res/master/import',
          template: '/res/master/importTemplate'
        },

        tableProps: {
          selection: 'multiple',
          selectionRowKeys: this.selectionRowKeys
        },

        tableActionProps: {
          width: this.selectMode ? 100 : 300
        },

        infoProps: {
          title: true
        },

        hiddenActions: {
          add: this.selectMode || !this.$checkPermi(['auto:host:add']),
          import: this.selectMode || !this.$checkPermi(['auto:host:import']),
          export: this.selectMode || !this.$checkPermi(['auto:host:export']),
          edit: this.selectMode || !this.$checkPermi(['auto:host:edit']),
          remove: this.selectMode || !this.$checkPermi(['auto:host:remove'])
        },

        model: {
          name: {
            type: 'text',
            label: '资源主机名称',
            align: 'left',
            form: {
              rules: true
            },
            search: {
              hidden: true
            },
            width: 200
          },
          ip: {
            type: 'text',
            label: '资源主机IP',
            form: {
              rules: true
            },
            width: 150
          },
          type: {
            type: 'select',
            label: '主机类别',
            search: {
              hidden: true
            },
            options: [
              {
                label: '业务网',
                value: '1'
              },
              {
                label: '管理网',
                value: '2'
              },
              {
                label: '承载网',
                value: '3'
              },
              {
                label: '存储网',
                value: '4'
              },
              {
                label: '私有网',
                value: '5'
              }
            ]
          },
          resPoolName: {
            type: 'text',
            label: '资源池名称'
          },
          resPoolName4a: {
            type: 'text',
            label: '4A堡垒前置'
          },
          primaryAccountNumberId: {
            label: '关联主账号',
            type: 'select',
            options: this.dataMixin.primaryAccountList
          },
          baseStatus: {
            type: 'select',
            label: '脚本上传状态',
            search: {
              sort: -1
            },
            options: [
              {
                label: '未上传',
                value: '0',
                raw: {
                  listClass: 'info'
                }
              },
              {
                label: '上传成功',
                value: '1',
                raw: {
                  listClass: 'success'
                }
              },
              {
                label: '上传失败',
                value: '2',
                raw: {
                  listClass: 'danger'
                }
              }
            ]
          },
          baseScriptName: {
            type: 'text',
            label: '脚本名称',
            search: {
              hidden: true
            },
            width: 200
          },
          baseScriptVersion: {
            type: 'text',
            label: '脚本版本',
            search: {
              hidden: true
            },
            width: 100
          },
          // status: {
          //   type: 'select',
          //   label: '状态',
          //   options: [
          //     {
          //       label: '草稿',
          //       value: '0',
          //       raw: {
          //         listClass: 'info'
          //       }
          //     },
          //     {
          //       label: '正常',
          //       value: '1',
          //       raw: {
          //         listClass: 'success'
          //       }
          //     },
          //     {
          //       label: '异常',
          //       value: '2',
          //       raw: {
          //         listClass: 'warning'
          //       }
          //     }
          //   ],
          //   search: {
          //     sort: -1
          //   }
          // },
          remark: {
            type: 'text',
            label: '备注',
            search: {
              hidden: true
            },
            width: 300
          },
          createTime: {
            type: 'text',
            label: '创建时间',
            search: {
              type: 'date-range',
              parameter: (data) => {
                return {
                  startTime: data?.[0],
                  endTime: data?.[1]
                }
              }
            },
            form: {
              hidden: true
            },
            width: 200
          },
          updateTime: {
            type: 'text',
            label: '修改时间',
            search: {
              hidden: true
            },
            form: {
              hidden: true
            },
            width: 200
          }
        }
      }

      return value
    }
  },
  methods: {
    handleAssociate(event) {
      this.$refs.associateDialogRef.open({
        ids: event.selectionIds,
        success: () => {
          this.$refs.sheetRef.getTableData()
        }
      })
    },
    getQuery() {
      return this.$refs.sheetRef.listParameter()
    },
    getSelection() {
      return this.$refs.sheetRef.tableMixin.selectionIds
    },
    getSelectionList() {
      return this.$refs.sheetRef.tableMixin.selection
    },
    getSearchParams(...args) {
      return this.$refs.sheetRef.searchMixin.parameter(...args)
    },
    async  handleRetry(row) {
      const res = await updateMaster({ id: row.id, baseStatus: '0' })

      if (res.code === 200) {
        this.$message.success('发送成功')
        this.$refs.sheetRef.getTableData()
      }
    }
  }
}
</script>

<style></style>
