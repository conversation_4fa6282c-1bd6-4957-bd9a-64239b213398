<template>
  <div class="">
    <MonitorList />

    <EleSheet ref="sheetRef" v-bind="sheetProps" class="page-main">
      <template #table:scanCollectCount:simple="{ row }">
        <el-link type="primary" @click="handleLading(row)">{{ row.scanCollectCount || 0 }}</el-link>
      </template>

      <template #table:after>
        <el-table-column v-slot="{ row }" label="操作" width="200" fixed="right" align="center">
          <el-button
            type="text"
            size="mini"
            :disabled="!$checkPermi(['auto:task:viewLog'])"
            @click="
              $router.push({
                path: '/auto/log',
                query: {
                  taskId: row.id,
                },
              })
            "
          >
            查看日志
          </el-button>
        </el-table-column>
      </template>
    </EleSheet>

    <FileDialog ref="fileDialogRef" />

    <LadingDialog ref="ladingDialogRef" />
  </div>
</template>

<script>
import MonitorList from './MonitorList/index.vue'
import FileDialog from './FileDialog/index.vue'

import LadingDialog from '@/views/risk/riskBasicSystemLadingData/index.vue'

import request from '@/utils/request.js'

// import { autoTaskType as autoTaskTypeDict } from '@/dicts/index.js'
export default {
  dicts: ['mian_task_status', 'main_task_type'],
  components: {
    MonitorList,
    FileDialog,
    LadingDialog
  },
  computed: {
    sheetProps() {
      const value = {
        title: '前置机检测任务',

        api: {
          list: (params) =>
            request({
              url: '/risk/report/task/list/4a',
              method: 'get',
              params: { ...params }
            })
        },

        model: {
          taskName: {
            type: 'text',
            label: '任务名称',
            align: 'left'
          },
          taskType: {
            type: 'select-dict',
            label: '任务类型',
            options: this.dict.type.main_task_type
          },
          taskStatus: {
            type: 'select-dict',
            label: '任务状态',
            options: this.dict.type.mian_task_status
          },
          scanCollectCount: {
            type: 'text',
            label: '主机扫描数',
            search: {
              hidden: true
            }
          },
          scanHostPercent: {
            type: 'text',
            label: '主机扫描总进度',
            search: {
              hidden: true
            }
          },
          shadowCollectPercent: {
            type: 'text',
            label: 'shadow采集进度',
            search: {
              hidden: true
            }
          },
          basicLineCollectPercent: {
            type: 'text',
            label: '基线采集进度',
            search: {
              hidden: true
            }
          },
          leakTotal: {
            type: 'text',
            label: '弱口令数量',
            search: {
              hidden: true
            }
          },
          lineTotal: {
            type: 'date',
            label: '基线不合规数量',
            search: {
              hidden: true
            }
          }
        }
      }

      return value
    }
  },
  methods: {
    handleFileInfo(row) {
      this.$refs.fileDialogRef.open(row)
    },
    handleLading(row) {
      this.$refs.ladingDialogRef.open({
        params: {
          taskId: row.id
        }
      })
    }
  }
}
</script>

<style></style>
