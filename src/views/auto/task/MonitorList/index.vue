<template>
  <div class="">
    <div v-if="tableData.length > 11" class="absolute -top-10 right-6">
      <span class="" @click="handleToggle">
        <el-button v-if="collapsed" type="text" icon="el-icon-arrow-down">更多</el-button>
        <el-button v-else type="text" icon="el-icon-arrow-up">折叠</el-button>
      </span>
    </div>

    <el-row :gutter="10">
      <el-col v-for="(item, index) of showTableData" :key="index" :span="6" :lg="4">
        <div
          class="bg-white shadow px-4 pt-2 pb-2 mb-4 rounded-lg h-26 flex flex-col justify-between"
        >
          <div class="flex items-center">
            <div class="flex-1 w-0 truncate !leading-none"> {{ item.resPoolName }} </div>
            <div class="flex-none">
              <el-button
                type="text"
                size="mini"
                class="!py-0"
                :disabled="!$checkPermi(['auto:task:edit'])"
                @click="() => sheetRef.handleEdit(item)"
              >编辑</el-button>
            </div>
          </div>
          <div class="text-sm text-gray-500 -mt-1">{{ item.ip }}{{ item.port ? `:${item.port}`:'' }}</div>
          <div class="">
            <EleTagDict :value="item.status" :options="sheetProps.model.status.options" />
          </div>
        </div>
      </el-col>
      <el-col v-if="(!collapsed || tableData.length < visibleNum) && $checkPermi(['auto:task:add'])" ref="colRef" :span="6" :lg="4">
        <div class="bg-white shadow p-3 rounded-lg h-26">
          <div
            class="border border-dashed flex items-center justify-center h-full rounded-lg text-gray-500 space-x-1 cursor-pointer"
            @click="() => sheetRef.handleAdd()"
          >
            <i class="el-icon-plus" />
            <span class="text-sm">添加</span>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- <div v-if="tableData.length > 11" class="text-center relative -top-2">
      <span class="" @click="handleToggle">
        <el-button v-if="collapsed" type="text" icon="el-icon-arrow-down">更多</el-button>
        <el-button v-else type="text" icon="el-icon-arrow-up">折叠</el-button>
      </span>
    </div> -->

    <EleSheet
      v-show="false"
      ref="sheetRef"
      v-bind="sheetProps"
      class="page-main"
      @list-success="onListSuccess"
    >
      <template #form:resPoolName="{ model, colProps, itemProps }">
        <el-col v-bind="{ ...colProps }">
          <el-form-item v-bind="{ ...itemProps }">
            <ResPoolSelect v-model="model.resPoolName" placeholder="请输入" />
          </el-form-item>
        </el-col>
      </template>
    </EleSheet>
  </div>
</template>

<script>
import {
  listMonitor,
  addMonitor,
  updateMonitor,
  getMonitor,
  delMonitor
} from '@/api/auto/monitor.js'

export default {
  data() {
    return {
      sheetRef: null,
      tableData: [],
      collapsed: true,
      colRef: null,
      visibleNum: 6
    }
  },
  computed: {
    sheetProps() {
      const value = {
        title: '服务健康检查',
        layout: 'table,paging',

        api: {
          add: (params) => addMonitor({ ...params }),
          edit: (params) => updateMonitor({ ...params }),
          list: (params) => listMonitor({ params, pageSize: 100 }),
          info: getMonitor,
          remove: delMonitor
        },

        model: {
          name: {
            type: 'text',
            label: '前置服务名称',
            form: {
              rules: true
            }
          },
          resPoolName: {
            type: 'text',
            label: '磐基资源池名称',
            form: {
              rules: true
            }
          },
          ip: {
            type: 'text',
            label: '前置服务IP',
            form: {
              rules: true
            }
          },
          port: {
            type: 'text',
            label: '端口'
          },
          status: {
            label: '状态',
            type: 'select',
            options: [
              {
                label: '异常',
                value: '0',
                raw: {
                  listClass: 'danger'
                }
              },
              {
                label: '正常',
                value: '1',
                raw: {
                  listClass: 'success'
                }
              }
            ],
            form: {
              rules: true
            }
          },
          remarks: {
            type: 'text',
            label: '备注',
            form: {
              type: 'textarea'
            },
            search: {
              hidden: true
            }
          },
          startTime: {
            type: 'date',
            label: 'lastHeartTime',
            form: {
              hidden: true
            }
          },
          lastHeartTime: {
            type: 'date',
            label: '最后一次心跳时间',
            form: {
              hidden: true
            }
          }
        }
      }

      return value
    },

    loading() {
      return this.sheetRef.loading
    },

    showTableData() {
      if (this.collapsed) {
        return this.tableData.filter((item, index) => index < this.visibleNum)
      }

      return this.tableData
    }
  },
  async mounted() {
    await this.$nextTick()
    this.sheetRef = this.$refs.sheetRef
  },
  methods: {
    handleToggle() {
      this.collapsed = !this.collapsed
    },
    onListSuccess(data) {
      this.tableData = data
    }
  }
}
</script>

<style></style>
