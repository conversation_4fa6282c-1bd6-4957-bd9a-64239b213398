<template>
  <div class="page-main page-main--full !pt-1">
    <CategoryTabs
      ref="categoryTabsRef"
      v-bind="{ childSheetProps: sheetProps }"
      @tab-click="onTabClick"
    />

    <div v-full:height class="overflow-hidden">
      <EleSheet
        ref="sheetRef"
        :key="[currentMajorItem, currentSmallItem].join('-')"
        v-bind="sheetProps"
        class=""
        @add-success="onAddSuccess"
        @edit-success="onEditSuccess"
        @remove-success="onRemoveSuccess"
      >
        <template #toolbar:after="{ selected, selection }">
          <el-button v-if="$checkPermi(['audit:template:database:mapping'])" type="primary" @click="handleMapping"> <IconDatabase class="" />数据库映射 </el-button>
          <el-button v-if="$checkPermi(['audit:template:settings'])" type="warning" :disabled="!selected" @click="handleSettings(selection)"> <IconAssignmentAdd class="" />批量配置 </el-button>
          <el-button v-if="$checkPermi(['audit:template:add:field'])" type="primary" icon="el-icon-edit" @click="onFieldAddClick()"> 添加字段 </el-button>
        </template>

        <template #table:status="{ columnProps }">
          <el-table-column v-slot="{ row }" v-bind="{ ...columnProps }">
            <el-switch
              v-model="row.status"
              active-value="1"
              inactive-value="0"
              :disabled="!$checkPermi(['audit:template:updateStatus'])"
              @change="(value) => onSwitchChange(value, row)"
            />
          </el-table-column>
        </template>

        <template #search:templateSecondClassifi="{ itemProps, model }">
          <el-form-item v-bind="{ ...itemProps }">
            <CategorySelect
              ref="categorySelectRef"
              :key="currentMajorItem"
              v-model="model.templateSecondClassifi"
              :params="{
                firstClassify: currentMajorItem,
                distinctType: 'second',
              }"
              @loaded="(data) => onCategoryLoaded(data, model)"
              @change="(value) => onCategoryChange(value, model)"
              @clear="onCategoryClear"
            />
          </el-form-item>
        </template>

        <template #table:after="{ editHandler }">
          <el-table-column v-slot="{ row }" label="操作" align="center" width="300" fixed="right">
            <el-button v-if="$checkPermi(['audit:template:edit'])" size="mini" type="text" @click="editHandler(row)">编辑</el-button>
            <el-button v-if="$checkPermi(['audit:template:field:configuration'])" type="text" size="mini" @click="handleField(row)"> 模版字段配置 </el-button>
          </el-table-column>
        </template>

        <template #after>
          <FieldDialog ref="fieldDialogRef" />
          <MappingDialog ref="mappingDialogRef" />
          <SettingsDialog ref="settingsDialogRef" />
          <FieldAddDialog ref="fieldAddDialogRef" />
        </template>
      </EleSheet>
    </div>

  </div>
</template>

<script>
import {
  addSecurityAudit,
  delSecurityAudit,
  getSecurityAudit,
  listSecurityAudit,
  updateSecurityAudit
} from '@/api/audit/template.js'

// import request from '@/utils/request.js';

import CategoryTabs from './CategoryTabs/index.vue'
import CategorySelect from './CategorySelect/index.vue'

import FieldDialog from './FieldDialog/index.vue'
import MappingDialog from './MappingDialog/index.vue'
import SettingsDialog from './SettingsDialog/index.vue'
import FieldAddDialog from './FieldAddDialog/index.vue'

import { templateBusinessType } from '@/dicts/template/index.js'

export default {
  components: {
    CategoryTabs,
    CategorySelect,
    FieldDialog,
    MappingDialog,
    SettingsDialog,
    FieldAddDialog
  },
  data() {
    return {
      previewProps: {
        visible: false,
        content: '',
        open(content) {
          this.content = content
          this.visible = true
        },
        close() {
          this.visible = false
        },
        onClose() {
          this.content = ''
        }
      },

      currentMajorItem: void 0,
      currentSmallItem: void 0
    }
  },
  computed: {
    sheetProps() {
      const value = {
        title: '模板配置',

        api: {
          add: (params) => addSecurityAudit({ ...params }),
          edit: (params) => updateSecurityAudit({ ...params }),
          list: async(params) => {
            const res = await listSecurityAudit({
              ...params
            })

            return {
              ...res
            }
          },
          info: getSecurityAudit,
          remove: delSecurityAudit,
          export: '/risk/securityAudit/export'
        },

        hiddenActions: {
          add: !this.$checkPermi(['audit:template:add']),
          remove: !this.$checkPermi(['audit:template:remove']),
          export: !this.$checkPermi(['audit:template:export'])
        },

        infoProps: {
          title: true
        },

        tableProps: {
          height: '100%'
        },

        model: {
          templateName: {
            type: 'text',
            label: '模板名称',
            align: 'left',
            form: {
              rules: true
            }
          },
          businessType: {
            type: 'select',
            label: '业务类型',
            align: 'left',
            form: {
              rules: true
            },
            search: {},
            options: templateBusinessType
          },
          templateFirstClassifi: {
            type: 'text',
            label: '大类',
            align: 'left',
            form: {
              rules: true
            },
            search: {
              hidden: true
            },
            value: this.currentMajorItem
          },
          templateSecondClassifi: {
            type: 'text',
            label: '小项',
            align: 'left',
            form: {
              rules: true
            },
            search: {},
            value: this.currentSmallItem
          },
          remark: {
            type: 'text',
            label: '内容',
            form: {
              rules: true
            }
          },
          businessKeyColumns: {
            label: '业务主键',
            search: {
              hidden: true
            },
            form: {
              hidden: true
            },
            table: {
              formatter: (row) => row.businessKeyColumns || '-'
            }
          },
          disposeNum: {
            label: '超期未处理天数',
            search: {
              hidden: true
            },
            form: {
              hidden: true
            },
            table: {
              formatter: (row) => row.disposeNum || '-'
            }
          },
          reorganizeAdvice: {
            label: '整改建议',
            search: {
              hidden: true
            },
            form: {
              hidden: true
            },
            table: {
              formatter: (row) => row.reorganizeAdvice || '-'
            }
          },
          status: {
            type: 'switch',
            label: '状态',
            search: {
              hidden: true
            },
            options: [
              {
                label: void 0,
                value: '1'
              },
              {
                label: void 0,
                value: '0'
              }
            ]
          },
          createBy: {
            type: 'text',
            label: '创建人',
            form: {
              hidden: true
            }
          },
          createTime: {
            type: 'text',
            label: '创建时间',
            search: {
              type: 'date-range',
              parameter: (data) => {
                return {
                  startTime: data?.[0],
                  endTime: data?.[1]
                }
              }
            },
            form: {
              hidden: true
            },
            width: 200
          }
        }
      }

      return value
    }
  },
  methods: {
    handleSettings(selection) {
      this.$refs.settingsDialogRef.open({
        selection,
        success: () => {
          this.$refs.sheetRef.getTableData()
        }
      })
    },
    onAddSuccess() {
      this.$refs.categoryTabsRef.refresh()
      this.$refs.categorySelectRef.refresh()
    },
    onEditSuccess() {
      this.$refs.categoryTabsRef.refresh()
    },
    onRemoveSuccess() {
      this.$refs.categoryTabsRef.refresh()
    },
    onTabClick(event) {
      this.currentMajorItem = event.label
    },
    handleField(row) {
      this.$refs.fieldDialogRef.open(row)
    },
    handleMapping() {
      this.$refs.mappingDialogRef.open({
        params: {
          templateFirstClassifi: this.currentMajorItem,
          templateSecondClassifi: this.currentSmallItem
        }
      })
    },

    async onSwitchChange(status, row) {
      const params = {
        id: row.id,
        status
      }

      const res = await this.sheetProps.api.edit(params)

      if (res.code !== 200) {
        this.$message.warning(res.msg)
      }
    },

    async onCategoryLoaded(data) {
      if (data.some((item) => item.value === this.currentSmallItem)) {
        return false
      }

      if (!this.smallItemLock) {
        this.currentSmallItem = data[0].value
      }
      this.smallItemLock = false

      await this.$nextTick()
      this.$refs.sheetRef.searchMixin.query()
    },
    async onCategoryChange(value) {
      this.currentSmallItem = value

      await this.$nextTick()
      this.$refs.sheetRef.searchMixin.query()
    },

    onCategoryClear() {
      this.smallItemLock = true
      this.currentSmallItem = void 0
    },

    onFieldAddClick() {
      this.$refs.fieldAddDialogRef.open({
        success: () => {
          this.$refs.sheetRef.getTableData()
        }
      })
    }
  }
}
</script>

<style></style>
