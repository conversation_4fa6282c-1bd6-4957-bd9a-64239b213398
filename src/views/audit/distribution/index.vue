<template>
  <div class="flex space-x-4 h-[var(--app-main-height)]">
    <div class="flex-none w-72 overflow-hidden shadow-sm rounded-lg bg-white p-4">
      <AuditTree class="" @node-click="onNodeClick" />
    </div>

    <div class="flex-1 w-0 overflow-hidden flex flex-col space-y-4">
      <div class="flex-1 h-0 relative">
        <EleSheet
          v-if="currentAudit.nodeName"
          :key="[currentAudit.nodeName].join('-')"
          ref="sheetRef"
          class="page-main"
          v-bind="sheetProps"
          @current-change="onCurrentChange"
          @list-success="onListSuccess"
        >
          <template #table:isEnabled:simple="{ row }">
            <el-switch
              v-model="row.isEnabled"
              active-value="true"
              inactive-value="false"
              :disabled="!$checkPermi(['audit:distribution:updateStatus'])"
              @change="(value) => onSwitchChange(value, row)"
            />
          </template>
        </EleSheet>

        <div
          v-if="!currentAudit.nodeName"
          class="absolute inset-0 bg-white/30 z-50"
          title="请先选择左侧审计类"
        />
      </div>
      <div class="flex-none h-96 overflow-hidden shadow-sm rounded-lg bg-white">
        <RuleEditor
          :key="[currentAudit.nodeName].join('-')"
          v-bind="{
            currentRow,
            currentAudit,
          }"
          @success="onRuleEditorSuccess"
        />
      </div>
    </div>
  </div>
</template>

<script>
import { addRule, delRule, getRule, listRule, updateRule } from '@/api/audit/distribution.js'

import request from '@/utils/request.js'

import AuditTree from './AuditTree/index.vue'

import RuleEditor from './RuleEditor/index.vue'
import { pick } from 'lodash-es'

export default {
  components: {
    AuditTree,
    RuleEditor
  },
  data() {
    return {
      currentAudit: {},
      currentRowKey: void 0,
      currentRow: {}
    }
  },
  computed: {
    checkType() {
      return this.$route.query.checkType ?? '14'
    },
    sheetProps() {
      const value = {
        title: '审计分配规则',

        layout: 'toolbar,table,paging',

        lazy: true,

        api: {
          add: (params) => {
            return addRule({
              ...params,
              checkPoint: this.currentAudit.nodeName,
              checkType: this.checkType
            })
          },
          edit: (params) => updateRule({ ...params }),
          list: async(params) => {
            const res = await listRule({
              ...params,
              checkPoint: this.currentAudit.nodeName,
              checkType: this.checkType
            })

            this.currentRowKey = this.tempRowKey || res.rows?.[0]?.id

            return res
          },
          info: async(id) => {
            const res = await getRule(id)
            const data = res?.data || {}

            return {
              ...res,
              data: {
                ...data,
                effectiveTime: [data.startEffectiveTime, data.endEffectiveTime].filter(
                  (item) => !!item
                )
              }
            }
          },
          remove: async(ids) => delRule(ids, pick(this.currentRow, ['modelType', 'riskBasicType'])),
          export: (handler) => {
            handler('/risk/rule/export', {
              parameter: (params = {}) => {
                return {
                  ...params,
                  checkPoint: this.currentAudit.nodeName,
                  checkType: this.checkType
                }
              }
            })
          },
          import: '/risk/rule/import',
          template: ''
        },

        hiddenActions: {
          add: !this.$checkPermi(['audit:distribution:add']),
          edit: !this.$checkPermi(['audit:distribution:edit']),
          remove: !this.$checkPermi(['audit:distribution:remove']),
          export: !this.$checkPermi(['audit:distribution:export']),
          import: !this.$checkPermi(['audit:distribution:import'])
        },

        tableProps: {
          height: '100%',
          selection: 'multiple',
          highlightCurrentRow: true
        },

        infoProps: {
          title: true
        },

        model: {
          ruleNo: {
            type: 'text',
            label: '规则编号',
            rules: true
          },
          ruleName: {
            type: 'text',
            label: '规则名称',
            rules: true
          },
          ruleLevel: {
            type: 'text',
            label: '优先级',
            form: {
              type: 'input-number',
              fieldProps: {
                max: 10000,
                min: 0
              }
            }
          },
          ruleExp: {
            type: 'text',
            label: '表达式',
            form: {
              hidden: true
            }
          },
          riskOperatorGroupName: {
            type: 'text',
            label: '处置分组',
            form: {
              hidden: true
            }
          },
          riskOperatorGroupId: {
            type: 'text',
            label: '分组ID',
            hidden: true
          },
          isEnabled: {
            type: 'switch',
            label: '是否启用',
            search: {
              hidden: true
            },
            options: [{ value: 'true' }, { value: 'false' }],
            info: {
              type: 'text',
              formatter: (model) => (model.isEnabled == 'true' ? '已启用' : '未启用')
            }
          },
          effectiveTime: {
            type: 'date-time-range',
            label: '有效时间',
            width: 350,
            form: {
              rules: true,
              fieldProps: {
                on: {
                  change: (value, ctx) => {
                    ctx.model.startEffectiveTime = value?.[0]
                    ctx.model.endEffectiveTime = value?.[1]
                  }
                }
              }
            },
            table: {
              formatter: (row) =>
                row.startEffectiveTime
                  ? `${row.startEffectiveTime} 至 ${row.endEffectiveTime}`
                  : ''
            },
            info: {
              formatter: (row) =>
                row.startEffectiveTime
                  ? `${row.startEffectiveTime} 至 ${row.endEffectiveTime}`
                  : '',
              lg: 12
            }
          },
          startEffectiveTime: {
            type: 'date-time',
            label: '开始有效时间',
            width: 200,
            hidden: true
          },
          endEffectiveTime: {
            type: 'date-time',
            label: '截至有效时间',
            width: 200,
            hidden: true
          },
          tenName: {
            type: 'text',
            label: '租户名称',
            hidden: true,
            value: this.$store.getters.userInfo.tenName
          },
          tenId: {
            type: 'text',
            label: '租户名称',
            form: {
              type: 'TenantSelect',
              fieldProps: {
                on: {
                  change(value, label, ctx) {
                    ctx.model.tenName = label
                  }
                }
              }
            },
            table: {
              formatter: (row) => row.tenName,
              width: 150
            },
            value: this.$store.getters.userInfo.tenId
          },
          remark: {
            type: 'text',
            label: '备注',
            width: 150
          }
        }
      }

      return value
    }
  },
  methods: {
    async onNodeClick(data) {
      this.tempRowKey = void 0

      this.currentRow = {}
      this.currentRowKey = void 0

      this.currentAudit = data

      await this.$nextTick()

      this.$refs.sheetRef.getTableData()
    },
    onCurrentChange(row) {
      this.currentRow = row
      this.tempRowKey = row.id
      this.$refs.sheetRef.tableMixin.echoCurrentRow(row.id)
    },
    async onRuleEditorSuccess() {
      this.$refs.sheetRef.getTableData()
    },
    async onSwitchChange(isEnabled, row) {
      const params = {
        id: row.id,
        isEnabled
      }

      const res = await this.sheetProps.api.edit(params)

      if (res.code !== 200) {
        this.$message.warning(res.msg)
      }
    },
    async onListSuccess(data) {
      await this.$nextTick()
      this.$refs.sheetRef.tableMixin.getTableRef().setCurrentRow(data?.[0])
    }
  }
}
</script>

<style></style>
