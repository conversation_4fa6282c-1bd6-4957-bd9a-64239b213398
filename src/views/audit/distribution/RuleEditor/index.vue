<template>
  <div class="h-full px-4">
    <el-divider direction="horizontal" content-position="left">规则编辑区域</el-divider>

    <el-form
      ref="formRef"
      :model="formInfo"
      :rules="formRules"
      label-width="70px"
      label-position="right"
      class=""
      hide-required-asterisk
      :disabled="!currentRow.id"
    >
      <div class="flex items-center">
        <el-form-item label="字段名称" prop="fieldName" class="">
          <el-select v-model="formInfo.fieldName" placeholder="请选择" clearable filterable>
            <el-option
              v-for="(item, index) in fieldColumnList"
              :key="index"
              :label="item.templateColumnName"
              :value="item.templateColumnName"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="运算符" prop="fieldOperator" class="">
          <EleSelectDict
            v-model="formInfo.fieldOperator"
            :options="operatorOptions"
            class="!-w-24"
          />
        </el-form-item>

        <el-form-item label="操作值" prop="fieldValue" class="">
          <el-input v-model="formInfo.fieldValue" placeholder="请输入" clearable />
        </el-form-item>

        <el-form-item label="处置分组" prop="riskOperatorGroupId" class="ml-auto">
          <DisposeGroupSelect
            v-model="formInfo.riskOperatorGroupId"
            placeholder="请选择"
            clearable
            :params="{
              groupKey: 'basic_type_audit_dim'
            }"
            @change="(value, label) => (formInfo.riskOperatorGroupName = label)"
          />
        </el-form-item>
      </div>

      <div class="flex items-center mt-4 mb-4">
        <div class="">
          <el-button-group>
            <el-button
              v-for="(item, index) of shortcutOptions"
              :key="index"
              type="default"
              @click="handleShortcut(item)"
            >
              {{ item.label }}
            </el-button>
          </el-button-group>
        </div>

        <div class="ml-32">
          <el-button icon="el-icon-bottom" type="primary" size="medium" :disabled="!$checkPermi(['audit:distribution:generateRule'])" @click="generateExpression">
            生成表达式
          </el-button>
        </div>

        <div class="ml-auto">
          <el-button icon="el-icon-check" type="success" v-bind="{ loading }" :disabled="!$checkPermi(['audit:distribution:saveRule'])" @click="handleSave">
            保存规则
          </el-button>
        </div>
      </div>

      <div class="">
        <el-form-item
          label="规则命中表达式（全表达式解析为整体为 true，则表示命中，执行分组策略）"
          label-position="top"
          label-width="auto"
          prop="ruleExp"
        >
          <el-input
            v-model="formInfo.ruleExp"
            type="textarea"
            rows="7"
            placeholder="请在此生成或手动编辑表达式"
          />
        </el-form-item>
      </div>
    </el-form>
  </div>
</template>

<script>
import EleSelectDict from '@/plugins/element-extends/components/EleField/SelectDict/index.vue'

import { shortcutOptions, operatorOptions, operatorModel } from './config.js'

import { updateRule } from '@/api/audit/distribution.js'

import { listSecurityAuditColumn } from '@/api/audit/template-column.js'

import DisposeGroupSelect from '@/components/business/DisposeGroupSelect/index.vue'

export default {
  components: {
    EleSelectDict,
    DisposeGroupSelect
  },
  props: {
    currentRow: {
      type: Object,
      default: () => ({})
    },
    currentAudit: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      shortcutOptions,
      operatorOptions,

      loading: false,

      formInfo: {
        fieldName: void 0,
        fieldOperator: '==',
        fieldValue: void 0,
        riskOperatorGroupId: void 0,
        riskOperatorGroupName: void 0,
        ruleExp: ''
      },

      formRules: {
        fieldName: [{ required: true, message: '该选项不能为空', trigger: 'blur' }],
        fieldOperator: [{ required: true, message: '该选项不能为空', trigger: 'blur' }],
        fieldValue: [{ required: true, message: '该选项不能为空', trigger: 'blur' }]
        // riskOperatorGroupId: [{ required: true, message: '该选项不能为空', trigger: 'blur' }],
        // ruleExp: [{ required: true, message: '该选项不能为空', trigger: 'blur' }],
      },

      fieldColumnList: []
    }
  },
  computed: {
    operatorParameter() {
      const value = operatorModel[this.formInfo.fieldOperator] || operatorModel.default
      return value.parameter
    },
    autoSpace() {
      if (!this.formInfo.ruleExp || this.formInfo.ruleExp.slice(-1) === ' ') {
        return ''
      }

      return ' '
    }
  },
  watch: {
    'currentRow.id': {
      handler(value) {
        if (!value) {
          return false
        }

        this.getFormInfo()
      },
      immediate: true
    }
  },
  methods: {
    async getFormInfo() {
      await this.getFieldColumnList()

      this.formInfo = {
        ...this.$options.data().formInfo,
        fieldName: this.fieldColumnList?.[0]?.templateColumnName,
        ruleExp: this.currentRow.ruleExp || '',
        riskOperatorGroupId: this.currentRow.riskOperatorGroupId
      }
    },

    async getFieldColumnList() {
      const res = await listSecurityAuditColumn({
        pageSize: 500,
        checkPoint: this.currentAudit.nodeName,
        templateFirstClassifi: this.currentAudit.templateFirstClassifi,
        templateSecondClassifi: this.currentAudit.templateSecondClassifi
      })

      const rows = res?.rows || []

      this.fieldColumnList = rows
    },

    async generateExpression() {
      const errors = []

      await this.$refs.formRef.validateField(
        ['fieldName', 'fieldValue', 'fieldOperator'],
        (err) => {
          if (err) {
            errors.push(error)
          }
        }
      )

      if (errors.length) {
        return false
      }

      this.formInfo.ruleExp +=
          this.autoSpace +
          this.operatorParameter({
            label: this.formInfo.fieldName,
            value: this.formInfo.fieldValue,
            operator: this.formInfo.fieldOperator
          })
    },
    handleShortcut(info) {
      this.formInfo.ruleExp += `${this.autoSpace}${info.value}`
    },

    async handleSave() {
      // const errors = [];

      // await this.$refs.formRef.validateField(['ruleExp', 'riskOperatorGroupId'], (err) => {
      //   if (err) {
      //     errors.push(error);
      //   }
      // });

      // if (errors.length) {
      //   return false;
      // }

      this.loading = true

      const res = await updateRule({
        id: this.currentRow.id,
        ruleExp: this.formInfo.ruleExp,
        riskOperatorGroupId: this.formInfo.riskOperatorGroupId,
        riskOperatorGroupName: this.formInfo.riskOperatorGroupName
      })

      this.loading = false

      if (res.code === 200) {
        this.$message.success(res.msg)
        this.$emit('success')
      }
    }
  }
}
</script>

<style></style>
