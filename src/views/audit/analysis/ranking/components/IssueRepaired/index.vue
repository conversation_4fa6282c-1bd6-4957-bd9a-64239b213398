<template>
  <div class="h-full">
    <div class="flex items-center space-x-2 pb-2">
      <el-button
        type="primary"
        plain
        icon="el-icon-refresh-right"
        v-bind="{ loading }"
        @click="handleRefresh"
      >
        刷新
      </el-button>
      <el-button v-if="$checkPermi(['audit:analysis:ranking:export'])" type="primary" icon="el-icon-download" @click="handleExport">导出</el-button>

      <el-select
        v-model="searchParams.topNumber"
        placeholder="请选择排名位数"
        class="!w-36"
        @change="getTableData"
      >
        <el-option
          v-for="(item, index) in [5, 10, 20, 50]"
          :key="index"
          :label="'TOP' + item"
          :value="item"
        />
      </el-select>

      <el-select
        v-model="searchParams.sortField"
        placeholder="排序字段"
        class="!w-36"
        @change="getTableData"
      >
        <el-option label="处置数" value="disposedCount" />
        <el-option label="处置率" value="percent" />
      </el-select>
    </div>

    <div class="text-white bg-[#509863] py-2 text-center rounded-t-md">{{ title }}</div>

    <div v-full:height v-loading="loading" class="">
      <el-table
        class="el-table--beautify"
        :data="tableData"
        style="width: 100%"
        height="100%"
        border
      >
        <el-table-column type="index" label="排名" width="55" align="center" />
        <el-table-column prop="groupName" label="团队名称" align="left" show-overflow-tooltip />

        <el-table-column v-slot="{ row }" prop="disposedCount" label="数量" align="center">
          {{ row.disposedCount }}
          <!-- <el-link
            type="success"
            @click="$emit('info', { groupId: row.团队ID, type: 'disposed', title })"
          >{{ row.disposedCount }}</el-link> -->
        </el-table-column>

        <el-table-column v-slot="{ row }" prop="disposePercent" label="处置率" align="center">
          {{ row.disposePercent }}
          <!-- <RateLink
            preset="dispose"
            :value="row.disposePercent"
            underline
            @click="$emit('info', { groupId: row.团队ID, type: 'disposed', title })"
          /> -->
        </el-table-column>
        <el-table-column prop="member" label="团队成员" align="center" show-overflow-tooltip />
      </el-table>
    </div>
  </div>
</template>

<script>
import request from '@/utils/request.js'

import getRoleType from '@/utils/getRoleType'
export default {
  data() {
    return {
      loading: false,
      title: '已处置安全审计问题效率最高团队TOP排名',

      tableData: [],

      searchParams: {
        roleType: getRoleType(),
        sortType: 'desc',
        sortField: 'disposedCount',
        topNumber: 5
      }
    }
  },
  created() {
    this.getTableData()
  },
  methods: {
    handleExport() {
      this.download(
        '/risk/businessReport/exportAuditEventGroupRankDispose',
        {
          ...this.searchParams
        },
        `${this.title}_${new Date().getTime()}.xlsx`,
        { method: 'post', headers: { 'Content-Type': 'application/json;charset=utf-8' }}
      )
    },
    handleRefresh() {
      this.getTableData()
    },
    async getTableData() {
      this.loading = true
      const res = await request({
        method: 'post',
        url: '/risk/businessReport/getGroupDisposedRank',
        data: {
          ...this.searchParams
        }
      })
      this.loading = false

      this.tableData = res.data
    }
  }
}
</script>

<style></style>
