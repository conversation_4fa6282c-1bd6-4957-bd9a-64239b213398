<template>
  <EleSheet ref="sheetRef" v-bind="sheetProps" class="page-main page-main--full">
    <template #toolbar:after>
      <el-button
        type="primary"
        :icon="chartFlag ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"
        @click="handleToggleChart"
      >统计图表</el-button>
    </template>

    <template #toolbar-to-table>
      <ChartGroup
        v-if="statsList.length"
        :key="[JSON.stringify(statsList), chartFlag].join('-')"
        class="overflow-hidden"
        :class="chartFlag ? 'h-[300px]' : 'h-0'"
        v-bind="{ statsList }"
      />
    </template>

    <template #search:taskId:simple="{ model }">
      <TaskSelect v-model="model.taskId" :params="{ taskType: '9' }" />
    </template>

    <template #after> </template>
  </EleSheet>
</template>

<script>
import { spanRow } from 'element-ui-table-span-method'
import { omit } from 'lodash-es'
import request from '@/utils/request.js'
import TaskSelect from '@/components/business/TaskSelect/index.vue'
import ChartGroup from './ChartGroup/index.vue'
import Cookie from 'js-cookie'
import getRoleType from '@/utils/getRoleType'
export default {
  components: {
    TaskSelect,
    ChartGroup
  },
  data() {
    return {
      mergeOptions: [{ index: 0, field: 'templateFirstClassifi' }],
      statsList: [],
      flatStatsList: [],
      chartFlag: Cookie.get('audit/analysis/overview') !== 'false'
    }
  },
  computed: {
    sheetProps() {
      const value = {
        layout: 'search,toolbar,table',
        tableProps: {
          showSummary: true,
          height: '100%',
          stripe: true,
          border: true,
          spanMethod: this.spanMethod
        },
        title: this.$route.meta.title,
        api: {
          list: async(params) => {
            const res = await request({
              url: '/risk/businessReport/getDisposedDetail',
              method: 'post',
              data: {
                ...omit(params, ['pageNum', 'pageSize']),
                roleType: getRoleType()
              }
            })

            this.statsList = res.data || []

            this.flatStatsList = this.statsList.flatMap(
              (item) => item.riskTemplateDisposedDetailVoList
            )

            const rows = this.flatStatsList

            return {
              code: 200,
              rows
            }
          },
          export: (handler) =>
            handler('/risk/businessReport/exportAuditEventDispose', {
              parameter: (params) => omit(params, ['pageNum', 'pageSize']),
              options: {
                method: 'post',
                headers: { 'Content-Type': 'application/json;charset=utf-8' }
              }
            })

          // add: (params) => addController({ ...params }),
          // edit: (params) => updateController({ ...params }),
          // info: getController,
          // remove: delController,
          // import: '',
          // template: ''
        },

        infoProps: {
          title: true
        },

        hiddenActions: {
          export: !this.$checkPermi(['audit:analysis:overview:export'])
        },

        model: {
          templateFirstClassifi: {
            type: 'text',
            label: '审计大类',
            search: {
              hidden: true
            }
          },
          templateSecondClassifi: {
            type: 'text',
            label: '审计小项',
            search: {
              hidden: true
            }
          },
          totalCount: {
            label: '审计问题数',
            search: {
              hidden: true
            }
          },
          dispatchedCount: {
            label: '分配数',
            search: {
              hidden: true
            }
          },
          disposedCount: {
            label: '已处置数',
            search: {
              hidden: true
            }
          },
          unDisposedCount: {
            label: '未处置数',
            search: {
              hidden: true
            }
          },
          disposePercent: {
            label: '处置率',
            search: {
              hidden: true
            }
          },
          approvedCount: {
            label: '审核通过数',
            search: {
              hidden: true
            }
          },
          approvedPercent: {
            label: '审核通过率',
            search: {
              hidden: true
            }
          },
          createTime: {
            hidden: true,
            type: 'text',
            label: '时间区间',
            search: {
              hidden: false,
              type: 'date-range',
              parameter: (data) => {
                return {
                  startTime: data?.[0],
                  endTime: data?.[1]
                }
              }
            }
          }
        }
      }

      return value
    }
  },
  methods: {
    handleRefresh() {
      this.$refs.sheetRef.getTableData()
    },
    handleToggleChart() {
      this.chartFlag = !this.chartFlag
      Cookie.set('audit/analysis/overview', this.chartFlag)
    },
    spanMethod({ row, column, rowIndex, columnIndex }) {
      return spanRow(
        { row, column, rowIndex, columnIndex },
        this.flatStatsList,
        this.mergeOptions
      )
    }
  }
}
</script>

<style></style>
