<template>
  <EleSheet ref="sheetRef" v-bind="sheetProps" class="page-main page-main--full" @list-success="onListSuccess">
    <template #search:riskOperatorGroupId:simple="{ model }">
      <DisposeGroupSelect
        ref="disposeGroupSelectRef"
        v-model="model.riskOperatorGroupId"
        placeholder="选择处置组"
        clearable
        :params="{
          groupKey: 'basic_type_audit_dim',
        }"
      />
    </template>

    <template #after> </template>
  </EleSheet>
</template>

<script>
import { spanRow } from 'element-ui-table-span-method'
import { omit } from 'lodash-es'
import request from '@/utils/request.js'
import DisposeGroupSelect from '@/components/business/DisposeGroupSelect/index.vue'
import getRoleType from '@/utils/getRoleType'
export default {
  components: {
    DisposeGroupSelect
  },
  data() {
    return {
      mergeOptions: [{ index: 0, field: 'taskName' }],
      statsList: []
    }
  },
  computed: {
    sheetProps() {
      const value = {
        layout: 'search,toolbar,table',

        title: this.$route.meta.title,

        tableProps: {
          showSummary: false,
          height: '100%',
          stripe: true,
          border: true,
          spanMethod: this.spanMethod
        },

        api: {
          list: async(params) => {
            const res = await request({
              url: '/risk/businessReport/getGroupDisposedDetail',
              method: 'post',
              data: {
                ...omit(params, ['pageNum', 'pageSize']),
                roleType: getRoleType()
              }
            })

            const data = res.data || []

            const rows = data

            return {
              code: 200,
              rows
            }
          },
          export: (handler) =>
            handler('/risk/businessReport/exportAuditEventGroupDispose', {
              parameter: (params) => omit(params, ['pageNum', 'pageSize']),
              options: {
                method: 'post',
                headers: { 'Content-Type': 'application/json;charset=utf-8' }
              }
            })

          // add: (params) => addController({ ...params }),
          // edit: (params) => updateController({ ...params }),
          // info: getController,
          // remove: delController,
          // import: '',
          // template: ''
        },

        hiddenActions: {
          export: !this.$checkPermi(['audit:analysis:situation:export'])
        },

        infoProps: {
          title: true
        },

        model: {
          taskName: {
            label: '任务名称'
          },
          groupName: {
            type: 'text',
            label: '处置组',
            search: {
              hidden: true
            }
          },
          totalCount: {
            label: '问题总数',
            search: {
              hidden: true
            }
          },
          disposedCount: {
            label: '已处置数',
            search: {
              hidden: true
            }
          },
          unDisposedCount: {
            label: '未处置数',
            search: {
              hidden: true
            }
          },
          disposePercent: {
            label: '处置率',
            search: {
              hidden: true
            }
          },
          approvedCount: {
            label: '审核通过数',
            search: {
              hidden: true
            }
          },
          approvedPercent: {
            label: '审核通过率',
            search: {
              hidden: true
            }
          },

          riskOperatorGroupId: {
            hidden: true,
            label: '处置组',
            search: {
              hidden: false
            }
          },
          createTime: {
            hidden: true,
            type: 'text',
            label: '时间区间',
            search: {
              hidden: false,
              type: 'date-range',
              parameter: (data) => {
                return {
                  startTime: data?.[0],
                  endTime: data?.[1]
                }
              }
            }
          }
        }
      }

      return value
    }
  },
  methods: {
    spanMethod({ row, column, rowIndex, columnIndex }) {
      return spanRow({ row, column, rowIndex, columnIndex }, this.statsList, this.mergeOptions)
    },
    async onListSuccess(data) {
      this.statsList = data

      await this.$nextTick()

      this.$refs.sheetRef.doLayout()
    }
  }
}
</script>

<style></style>
