<template>
  <div>
    <el-row v-if="source == 'index'" :gutter="16">
      <el-col
        v-for="(item, index) in statisticsList"
        :key="index"
        :xs="24"
        :sm="12"
        :md="6"
        :lg="4"
        :xl="4"
      >
        <div
          class="statistics-item"
          :class="queryParams.pwdServiceId == item.id ? 'is-active' : ''"
        >
          <div class="sa-flex sa-row-between sa-m-b-4">
            <div class="name sa-table-line-1">{{ item.name }}</div>
            <el-button
              v-if="item.status == 1 || item.status == 3"
              size="mini"
              type="text"
              :disabled="!$checkPermi(['risk:passwordCheckService:edit'])"
              @click="onEditStatistics(item)"
            >编辑</el-button>
          </div>
          <div class="ip sa-m-b-4">
            {{ item.ip }} {{ item.pord?`:${item.pord}`:'' }}
          </div>
          <div class="sa-flex">
            <div class="type">
              <dict-tag :options="dict.type.pwd_check_service_type" :value="item.type" />
            </div>
            <span class="sa-m-r-4 sa-m-l-4">|</span>
            <div class="status">
              <dict-tag :options="dict.type.pwd_check_service_status" :value="item.status" />
            </div>
          </div>
        </div>
      </el-col>
      <el-col v-if="$checkPermi(['risk:passwordCheckService:add'])" :xs="24" :sm="12" :md="6" :lg="4" :xl="4">
        <div class="statistics-item" @click="onAddStatistics">
          <div class="add sa-flex sa-row-center">+ 添加</div>
        </div>
      </el-col>
    </el-row>
    <div class="page-main" :class="pageMainClass">
      <el-form
        ref="queryForm"
        class="sa-query"
        :model="queryParams"
        :inline="true"
        label-width="68px"
      >
        <el-form-item v-if="source == 'index'" label="任务名称" prop="mainTaskName">
          <el-input
            v-model="queryParams.mainTaskName"
            placeholder="请输入任务名称"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item v-if="source == 'index'" label="任务类型" prop="mainTaskType">
          <el-select v-model="queryParams.mainTaskType" placeholder="请选择" clearable>
            <el-option
              v-for="dict in dict.type.main_task_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <!-- <el-form-item v-if="source == 'detail'" label="弱口令检测服务名称" prop="pwdServiceName">
          <el-input
            v-model="queryParams.pwdServiceName"
            placeholder="请输入弱口令检测服务名称"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item> -->
        <el-form-item v-if="source == 'index' || source == 'detail'" label="状态" prop="status">
          <el-select v-model="queryParams.status" placeholder="请选择" clearable>
            <el-option
              v-for="dict in dict.type.pwd_check_task_status"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item class="query-handle">
          <el-button type="primary" plain icon="el-icon-search" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh-left" @click="resetQuery">重置</el-button>
          <EleSheetSearchMore :parent-ref="()=> $refs.queryForm" />
        </el-form-item>
      </el-form>
      <el-table v-loading="loading" class="sa-table" :data="list">
        <el-table-column
          v-if="source == 'index'"
          label="任务名称"
          prop="mainTaskName"
          align="center"
        />
        <!-- <el-table-column
          label="弱口令检测服务名称"
          prop="pwdServiceName"
          align="center"
          min-width="200"
        /> -->
        <el-table-column label="任务类型" align="center" min-width="120">
          <template slot-scope="scope">
            <dict-tag :options="dict.type.main_task_type" :value="scope.row.mainTaskType" />
          </template>
        </el-table-column>
        <el-table-column label="状态" align="center" min-width="120">
          <template slot-scope="scope">
            <dict-tag :options="dict.type.pwd_check_task_status" :value="scope.row.status" />
          </template>
        </el-table-column>
        <el-table-column label="文件数" align="center" min-width="120">
          <template slot-scope="scope">
            {{ scope.row.weakPasswordFinishedNum }}/{{ scope.row.fileNum }}
          </template>
        </el-table-column>
        <el-table-column
          label="安全问题数"
          prop="weakPasswordNum"
          align="center"
          min-width="120"
        />
        <el-table-column v-slot="{ row }" label="采集策略" prop="weight" align="center" min-width="120">
          <EleTagDict :value="row.weight" :options="gatherDict" />
        </el-table-column>
        <el-table-column label="创建时间" align="center" prop="createTime" min-width="180" />
        <el-table-column label="操作" align="center" fixed="right" min-width="200">
          <template slot-scope="scope">
            <el-button v-if="$checkPermi(['risk:passwordCheckFileDetails:list'])" size="mini" type="text" @click="onDetail(scope.row)">查看文件明细</el-button>
            <el-button
              v-if="scope.row.status == 1"
              size="mini"
              type="text"
              @click="handleUpdate(scope.row)"
            >查看</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="sa-footer sa-row-center">
        <sa-pagination
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </div>
    </div>

    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="任务名称" prop="mainTaskName">
          <el-input v-model="form.mainTaskName" placeholder="请输入" disabled />
        </el-form-item>
        <el-form-item label="任务类型" prop="mainTaskType">
          <el-select v-model="form.mainTaskType" placeholder="请选择" disabled>
            <el-option
              v-for="dict in dict.type.main_task_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="检测报告类型" prop="checkType">
          {{ form.checkType == 1 ? '安全报告' : '核验报告' }}
          <!-- <el-select v-model="form.checkType" placeholder="请选择" disabled>
            <el-option
              v-for="dict in dict.type.pwd_check_service_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select> -->
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="form.status" placeholder="请选择" disabled>
            <el-option
              v-for="dict in dict.type.pwd_check_task_status"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <!-- <el-form-item label="权重" prop="weight">
          <el-input-number v-model="form.weight" controls-position="right" :min="0" />
        </el-form-item> -->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
        <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
      </div>
    </el-dialog>

    <el-dialog :title="title" :visible.sync="scanTask.visible" width="500px" append-to-body>
      <el-form ref="form" :model="scanTask.form" :rules="scanTask.rules" label-width="160px">
        <el-form-item label="弱口令检测服务名称" prop="name">
          <el-input v-model="scanTask.form.name" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="服务类型" prop="type">
          <el-select v-model="scanTask.form.type" placeholder="请选择">
            <el-option
              v-for="dict in dict.type.pwd_check_service_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="IP" prop="ip">
          <el-input v-model="scanTask.form.ip" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="端口" prop="pord">
          <el-input v-model="scanTask.form.pord" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="scanTask.form.status" placeholder="请选择">
            <el-option
              v-for="dict in dict.type.pwd_check_service_status"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
              :disabled="dict.value == 2"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="备注" prop="remarks">
          <el-input v-model="scanTask.form.remarks" placeholder="请输入" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="scanTask.visible = false">取 消</el-button>
        <el-button
          :loading="buttonLoading"
          type="primary"
          @click="submitFormStatistics"
        >确 定</el-button>
      </div>
    </el-dialog>

    <el-dialog title="查看文件明细" :visible.sync="detailData.visible" width="500px" append-to-body>
      <div class="page-main sa-p-t-0 sa-p-r-0 sa-p-l-0">
        <el-form
          ref="queryForm"
          class="sa-query"
          :model="detailData.queryParams"
          :inline="true"
          label-width="68px"
        >
          <el-form-item label="弱口令检测服务名称" prop="pwdServiceName">
            <el-input
              v-model="detailData.queryParams.pwdServiceName"
              placeholder="请输入弱口令检测服务名称"
              clearable
              size="small"
              @keyup.enter.native="detailHandleQuery"
            />
          </el-form-item>
          <el-form-item label="IP" prop="ip">
            <el-input
              v-model="detailData.queryParams.ip"
              placeholder="请输入ip"
              clearable
              size="small"
            />
          </el-form-item>
          <el-form-item label="文件名" prop="fileName">
            <el-input
              v-model="detailData.queryParams.fileName"
              placeholder="请输入文件名"
              clearable
              size="small"
              @keyup.enter.native="detailHandleQuery"
            />
          </el-form-item>
          <el-form-item label="文件夹名" prop="folderName">
            <el-input
              v-model="detailData.queryParams.folderName"
              placeholder="请输入文件夹名"
              clearable
              size="small"
              @keyup.enter.native="detailHandleQuery"
            />
          </el-form-item>
          <el-form-item label="状态" prop="status">
            <el-select v-model="detailData.queryParams.status" placeholder="请选择">
              <el-option
                v-for="dict in dict.type.pwd_check_file_status"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
                @keyup.enter.native="detailHandleQuery"
              />
            </el-select>
          </el-form-item>
          <el-form-item class="query-handle">
            <el-button type="primary" plain icon="el-icon-search" @click="detailHandleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh-left" @click="detailResetQuery">重置</el-button>
          </el-form-item>
        </el-form>
        <el-table v-loading="loading" class="sa-table" :data="detailData.list">
          <el-table-column
            label="弱口令检测服务名称"
            prop="pwdServiceName"
            align="center"
            min-width="200"
          />
          <el-table-column label="IP" prop="ip" align="center" min-width="150" />
          <el-table-column label="文件名称" prop="fileName" align="center" min-width="150" />
          <el-table-column label="文件夹名" prop="folderName" align="center" min-width="200" />
          <el-table-column label="弱密码数" prop="weakPwdNum" align="center" min-width="120" />
          <el-table-column label="状态" prop="status" align="center" min-width="120">
            <template slot-scope="scope">
              <dict-tag :options="dict.type.pwd_check_file_status" :value="scope.row.status" />
            </template>
          </el-table-column>
          <el-table-column label="开始检测时间" align="center" prop="startTime" min-width="180" />
          <el-table-column label="检测结束时间" align="center" prop="endTime" min-width="180" />
          <el-table-column label="创建时间" align="center" prop="createTime" min-width="180" />
        </el-table>
        <div class="sa-footer sa-row-center">
          <sa-pagination
            v-show="detailData.total > 0"
            :total="detailData.total"
            :page.sync="detailData.queryParams.pageNum"
            :limit.sync="detailData.queryParams.pageSize"
            @pagination="getCheckFile"
          />
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { addInfo, delInfo, exportInfo, getInfo, listInfo, updateInfo } from '@/api/ima/info'
import { getToken } from '@/utils/auth'
import request from '@/utils/request'

export default {
  components: {},
  dicts: [
    'pwd_check_service_type',
    'pwd_check_service_status',
    'main_task_type',
    'pwd_check_task_status',
    'pwd_check_file_status'
  ],
  props: {
    source: {
      type: String,
      default: 'index'
    },
    search: {
      type: Object,
      default: () => ({
        mainTaskId: ''
      })
    },
    pageMainClass: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      gatherDict: [{ label: '离线采集', value: '1' }, { label: '在线采集', value: '2' }],
      // 按钮loading
      buttonLoading: false,
      // 遮罩层
      loading: true,
      // 总条数
      total: 0,
      list: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        pwdServiceId: '',

        mainTaskName: '',
        mainTaskType: '',
        status: ''
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},

      statisticsList: [],

      scanTask: {
        visible: false,
        form: {},
        rules: {
          name: [{ required: true, message: '请输入', trigger: 'blur' }],
          type: [{ required: true, message: '请选择', trigger: 'blur' }],
          ip: [{ required: true, message: '请输入', trigger: 'blur' }],
          pord: [{ required: true, message: '请输入', trigger: 'blur' }],
          status: [{ required: true, message: '请选择', trigger: 'blur' }]
        }
      },

      detailData: {
        visible: false,
        list: [],
        total: 0,
        queryParams: {
          pageNum: 1,
          pageSize: 10,
          pwdTaskId: '',
          fileName: '',
          folderName: '',
          status: '',
          ip: '',
          pwdServiceName: ''
        }
      }
    }
  },
  created() {
    Object.assign(this.queryParams, { ...(this.$props.search || {}) })

    if (this.$props.source == 'index') {
      this.getStatisticsList()
    }
    this.getList()
  },
  methods: {
    getStatisticsList(row) {
      this.loading = true
      request({
        url: '/risk/passwordCheckService/list',
        method: 'get',
        params: {
          pageNum: 1,
          pageSize: 100
        }
      }).then((response) => {
        this.statisticsList = response.rows
        this.loading = false
      })
    },
    onStatistics(row) {
      this.loading = true
      if (!row || (row && this.queryParams.pwdServiceId == row.id)) {
        this.queryParams.pwdServiceId = ''
      } else {
        this.queryParams.pwdServiceId = row.id
      }
      this.queryParams.pageNum = 1
      this.getList()
    },
    getList() {
      if (this.$props.source == 'detail') {
        this.queryParams.mainTaskId = this.$props.search.mainTaskId
      }

      request({
        url: '/risk/passwordCheckTask/list',
        method: 'get',
        params: this.queryParams
      }).then((response) => {
        this.list = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    handleQuery() {
      this.detailData.queryParams.pageNum = 1
      this.getList()
    },
    resetQuery() {
      this.queryParams.mainTaskName = ''
      this.queryParams.mainTaskType = ''
      this.queryParams.pwdServiceName = ''
      this.queryParams.status = ''
      this.handleQuery()
    },
    cancel() {
      this.open = false
      this.reset()
    },
    reset() {
      this.form = {}
      this.resetForm('form')
    },
    handleAdd() {
      this.reset()
      this.open = true
      this.title = '添加'
    },
    handleUpdate(row) {
      this.loading = true
      // this.reset();
      // const id = row.id || this.ids;
      // getInfo(id).then((response) => {
      this.loading = false
      this.form = row
      this.open = true
      this.title = '修改'
      // });
    },
    submitForm() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          // this.buttonLoading = true;
          if (this.form.id != null) {
            request({
              url: '/risk/passwordCheckTask',
              method: 'put',
              data: this.form
            }).then((response) => {
              this.$modal.msgSuccess('修改成功')
              this.open = false
              this.getList()
            })
          } else {
            addInfo(this.form)
              .then((response) => {
                this.$modal.msgSuccess('新增成功')
                this.open = false
                this.getList()
              })
              .finally(() => {
                this.buttonLoading = false
              })
          }
        }
      })
    },
    handleDelete(row) {
      const ids = row.id || this.ids
      this.$confirm('是否确认删除当前所选数据项？', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.loading = true
          return delInfo(ids)
        })
        .then(() => {
          this.loading = false
          this.getList()
          this.$modal.msgSuccess('删除成功')
        })
        .catch(() => {})
    },
    onAddStatistics() {
      this.scanTask.form = {
        name: '',
        type: '',
        ip: '',
        pord: '',
        remarks: '',
        status: ''
      }
      this.scanTask.visible = true
      this.title = '添加'
    },
    onEditStatistics(row) {
      this.loading = true
      this.scanTask.form = {
        name: '',
        type: '',
        ip: '',
        pord: '',
        remarks: '',
        status: ''
      }
      this.loading = false
      this.scanTask.form = row
      this.scanTask.visible = true
      this.title = '修改'
    },
    submitFormStatistics() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          if (this.scanTask.form.id != null) {
            request({
              url: '/risk/passwordCheckService',
              method: 'put',
              data: this.scanTask.form
            }).then((response) => {
              this.$modal.msgSuccess('修改成功')
              this.scanTask.visible = false
              this.getStatisticsList()
            })
          } else {
            request({
              url: '/risk/passwordCheckService',
              method: 'post',
              data: this.scanTask.form
            }).then((response) => {
              this.$modal.msgSuccess('添加成功')
              this.scanTask.visible = false
              this.getStatisticsList()
            })
          }
        }
      })
    },
    onDetail(row) {
      this.detailData.visible = true
      this.detailData.queryParams.pwdTaskId = row.id
      this.detailResetQuery()
      this.getCheckFile()
    },
    getCheckFile() {
      request({
        url: '/risk/passwordCheckFileDetails/list',
        method: 'get',
        params: this.detailData.queryParams
      }).then((response) => {
        this.detailData.list = response.rows
        this.detailData.total = response.total
      })
    },
    detailHandleQuery() {
      this.detailData.queryParams.pageNum = 1
      this.getCheckFile()
    },
    detailResetQuery() {
      this.detailData.queryParams.fileName = ''
      this.detailData.queryParams.folderName = ''
      this.detailData.queryParams.status = ''
      this.detailData.queryParams.ip = ''
      this.detailData.queryParams.pwdServiceName = ''
      this.detailHandleQuery()
    }
  }
}
</script>

<style lang="scss" scoped>
  .statistics-item {
    height: 100px;
    border-radius: 8px;
    background: #ffffff;
    box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.04);
    margin: 0 0 16px;
    padding: 12px;
    cursor: pointer;
    &.is-active {
      border: 1px solid #36a5f4;
    }
    .name {
      font-size: 16px;
      color: #333333;
    }
    .ip {
      font-size: 14px;
      color: #666666;
    }
    .type,
    span {
      font-size: 14px;
      color: #999999;
    }
    .add {
      width: 100%;
      height: 100%;
      border: 1px dashed #eee;
      font-size: 14px;
      color: #999999;
    }
  }
</style>
