<template>
  <EleSheet ref="sheetRef" v-bind="sheetProps" class="page-main h-full" v-on="$listeners">
    <template #before> </template>

    <template #toolbar:after="{ selected, singleSelected, selectionIds, selection }">
      <el-button
        v-hasPermi="['risk:warn:yx:dispose']"
        type="primary"
        :disabled="!singleSelected"
        @click="onDisposeClick(selectionIds, selection)"
      ><IconHandyman class="" />处置</el-button>

      <el-button
        v-if="$checkPermi(['risk:turnoverRecord:yx:goBackRiskData'])"
        type="warning"
        :disabled="!selected"
        @click="onBackClick(selectionIds, selection)"
      ><IconUndo class="el-icon" />退回</el-button>

      <el-button
        v-if="$checkPermi(['risk:turnoverRecord:yx:transferRiskData'])"
        type="warning"
        :disabled="!selected"
        @click="handleAssign(selectionIds, { title: '转派' })"
      ><IconForward class="" />转派</el-button>

      <!-- <el-button
        v-if="$checkPermi(['risk:yx:group:markers'])"
        type="success"
        :disabled="!selected"
        @click="onMarkClick(selectionIds)"
      >
        <IconCheck class="icon" />标记
      </el-button> -->

      <el-button
        v-if="$checkPermi(['risk:middle:yx:assign'])"
        type="warning"
        icon="iconfont icon-piliangfenpei1"
        :disabled="!selected"
        @click="handleAssign(selectionIds)"
      >分配</el-button>
    </template>

    <template #search:deptName:simple="{ model }">
      <CommonDepartmentSelect
        v-model="model.deptName"
        placeholder="请输入"
        clearable
        return-name
        @change="() => (model.systemName = void 0)"
      />
    </template>

    <template #search:systemName:simple="{ model }">
      <CommonSystemSelect
        v-model="model.systemName"
        placeholder="请输入"
        return-name
        clearable
        :params="{
          deptName: model.deptName,
        }"
      />
    </template>

    <template #search:riskOperatorGroupId:simple="{ model }">
      <DisposeGroupSelect
        v-model="model.riskOperatorGroupId"
        placeholder="选择处置分组"
        clearable
        :params="{
          groupKey: 'basic_type_middle_dim',
          disposeType,
        }"
      />
    </template>

    <template #table:action:after="{ row }">
      <el-dropdown :key="row.id" v-check-dropdown-items>
        <el-button type="text" size="mini">更多</el-button>

        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item
              v-if="
                $checkPermi(['risk:turnoverRecord:yx:goBackRiskData']) &&
                  !['2'].includes(String(row.status))
              "
              @click.native="onBackClick([row.id], row)"
            >退回</el-dropdown-item>
            <el-dropdown-item
              v-if="
                $checkPermi(['risk:turnoverRecord:yx:transferRiskData']) &&
                  !['2'].includes(String(row.status))
              "
              @click.native="handleAssign([row.id], { title: '转派' })"
            >转派</el-dropdown-item>
            <el-dropdown-item
              v-if="$checkPermi(['risk:yx:relevance:list'])"
              @click.native="onAnalyzeClick(row)"
            >分析</el-dropdown-item>
            <!-- <el-dropdown-item
              v-if="$checkPermi(['risk:yx:group:markers'])"
              @click.native="onMarkClick([row.id], row)"
            >标记</el-dropdown-item> -->
            <!-- <el-dropdown-item
              v-if="$checkPermi(['risk:yx:group:whiteList'])"
              @click.native="onWhiteClick([row.id], row)"
            >加入白名单</el-dropdown-item> -->
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </template>

    <template #after>
      <AssignDialog ref="assignDialogRef" />
      <TurnDialog ref="turnDialogRef" />
      <MarkDialog ref="markDialogRef" />
      <DisposeDialog ref="disposeDialogRef" />
      <WhiteDialog ref="whiteDialogRef" />
      <AnalyzeDialog ref="analyzeDialogRef" />
      <ReturnDialog ref="returnDialogRef" />
    </template>
  </EleSheet>
</template>

<script>
import request from '@/utils/request.js'
import AssignDialog from '@/views/risk/basic/components/AssignDialog/index.vue'
import TurnDialog from '@/views/risk/warn/middleHandleStatistics/ContainerGroup/TurnDialog/index.vue'
import MarkDialog from '@/views/risk/warn/middleHandleStatistics/ContainerGroup/MarkDialog/index.vue'
import DisposeDialog from '@/views/risk/basic/components/DisposeDialog/index.vue'
import WhiteDialog from '@/views/risk/warn/middleHandleStatistics/ContainerGroup/WhiteDialog/index.vue'
import AnalyzeDialog from './AnalyzeDialog/index.vue'
import ReturnDialog from '@/views/risk/basic/components/ReturnDialog/index.vue'

export default {
  dicts: ['allocation_status', 'container_safety_one_dispose_status', 'dispose_status_container_yx'],
  components: {
    AssignDialog,
    TurnDialog,
    MarkDialog,
    DisposeDialog,
    WhiteDialog,
    AnalyzeDialog,
    ReturnDialog
  },
  data() {
    return {
      riskType: '22',
      scopeRiskType: '6',
      riskContainerSafetyType: '4',
      disposeType: '容器安全'
    }
  },
  computed: {
    sheetProps() {
      const value = {
        title: '3期-亚信智网',

        lazy: false,

        api: {
          // add: (params) => addController({ ...params }),
          // edit: (params) => updateController({ ...params }),
          // remove: delController,
          list: async(params) => request({ url: '/risk/yx/group/list', method: 'get', params }),
          info: async(id) => request({ url: `/risk/yx/${id}`, method: 'get' }),
          export: '/risk/yx/group/export',
          import: '',
          template: ''
        },

        flowProps: {
          preset: 'disposal',
          params: {
            riskType: this.riskType
          }
        },

        hiddenActions: {
          export: !this.$checkPermi(['risk:middle:yx:export'])
        },

        tableProps: {
          selection: 'multiple',
          height: '100%'
        },

        infoProps: {
          title: true
        },

        model: {
          dataType: {
            label: '告警类型',
            search: {},
            table: { width: 150, align: 'left' },
            form: {}
          },
          deptName: { label: '部门名称', search: {}, table: { width: 150 }, form: { width: 150 }},
          systemName: { label: '业务系统', search: {}, table: { width: 150 }, form: {}},
          alertLevel: { label: '风险等级', search: {}, table: {}, form: {}},
          ctrlIp: {
            label: '主机IP',
            search: {
              placeholder: '主机IP(多IP请用&quot;,&quot;分开)',
              parameter: (value) => {
                return value ? value.replace('，', ',') : void 0
              }
            },
            table: { width: 150 },
            form: {}
          },
          status: {
            label: '处置状态',
            type: 'select',
            search: {},
            table: {},
            form: {},
            options: this.dict.type.dispose_status_container_yx,
            group: 'flow'
          },
          dispatchStatus: {
            type: 'select',
            label: '分配状态',
            search: {},
            table: {},
            form: {},
            options: this.dict.type.allocation_status,
            group: 'flow'
          },
          riskOperatorGroupName: {
            label: '处置分组',
            search: {
              hidden: true
            },
            table: {},
            form: {
              hidden: true
            },
            group: 'flow'
          },
          riskOperatorGroupId: {
            label: '处置分组',
            search: {},
            table: {
              hidden: true
            },
            form: {}
          },

          createTime: {
            type: 'text',
            label: '创建时间',
            search: {
              type: 'date-time-range',
              parameter: (data) => {
                return {
                  startTime: data?.[0],
                  endTime: data?.[1]
                }
              }
            },
            form: {
              hidden: true
            },
            width: 200
          },
          starTime: { label: '告警时间', group: 'detail', search: { hidden: true }, table: { hidden: true }, form: {}},
          alertTypeCode: { label: '告警类型编码', group: 'detail', search: { hidden: true }, table: { hidden: true }, form: {},
            info: { hidden: (data) => !['系统告警'].includes(data.dataType) }
          },
          alertTypeName: { label: '告警类型', group: 'detail', search: { hidden: true }, table: { hidden: true }, form: {},
            info: { hidden: (data) => !['系统告警'].includes(data.dataType) }
          },
          username: { label: '操作用户', group: 'detail', search: { hidden: true }, table: { hidden: true }, form: {},
            info: { hidden: (data) => !['系统告警'].includes(data.dataType) }
          },
          userIp: { label: '操作用户IP', group: 'detail', search: { hidden: true }, table: { hidden: true }, form: {},
            info: { hidden: (data) => !['系统告警'].includes(data.dataType) }
          },
          target: { label: '操作对象', group: 'detail', search: { hidden: true }, table: { hidden: true }, form: {},
            info: { hidden: (data) => !['系统告警'].includes(data.dataType) }
          },
          platformId: { label: '告警平台ID', group: 'detail', search: { hidden: true }, table: { hidden: true }, form: {},
            info: { hidden: (data) => !['微隔离', '防病毒', 'WebShell', '恶意行为检测', '防暴力破解', '异常登录', '完整性监控', '行为模型', '虚拟补丁'].includes(data.dataType) }
          },
          level1SecurityDomainId: { label: '一级安全域ID', group: 'detail', search: { hidden: true }, table: { hidden: true }, form: {},
            info: { hidden: (data) => !['微隔离', '防病毒', 'WebShell', '恶意行为检测', '防暴力破解', '异常登录', '完整性监控', '行为模型', '虚拟补丁'].includes(data.dataType) }
          },
          level2SecurityDomainId: { label: '二级安全域ID', group: 'detail', search: { hidden: true }, table: { hidden: true }, form: {},
            info: { hidden: (data) => !['微隔离', '防病毒', 'WebShell', '恶意行为检测', '防暴力破解', '异常登录', '完整性监控', '行为模型', '虚拟补丁'].includes(data.dataType) }
          },
          level3SecurityDomainId: { label: '三级安全域ID', group: 'detail', search: { hidden: true }, table: { hidden: true }, form: {},
            info: { hidden: (data) => !['微隔离', '防病毒', 'WebShell', '恶意行为检测', '防暴力破解', '异常登录', '完整性监控', '行为模型', '虚拟补丁'].includes(data.dataType) }
          },
          level4SecurityDomainId: { label: '四级安全域ID', group: 'detail', search: { hidden: true }, table: { hidden: true }, form: {},
            info: { hidden: (data) => !['微隔离', '防病毒', 'WebShell', '恶意行为检测', '防暴力破解', '异常登录', '完整性监控', '行为模型', '虚拟补丁'].includes(data.dataType) }
          },
          entityType: { label: '工作负载类型', group: 'detail', search: { hidden: true }, table: { hidden: true }, form: {},
            info: { hidden: (data) => !['微隔离', '防病毒', 'WebShell', '恶意行为检测', '防暴力破解', '异常登录', '完整性监控', '行为模型', '虚拟补丁'].includes(data.dataType), formatter: (value) => (String(value) === '0' ? '主机' : '容器') }
          },
          objId: { label: '采集对象ID', group: 'detail', search: { hidden: true }, table: { hidden: true }, form: {},
            info: { hidden: (data) => !['微隔离', '防病毒', 'WebShell', '恶意行为检测', '防暴力破解', '异常登录', '完整性监控', '行为模型', '虚拟补丁'].includes(data.dataType) }
          },
          objName: { label: '采集对象名称', group: 'detail', search: { hidden: true }, table: { hidden: true }, form: {},
            info: { hidden: (data) => !['微隔离', '防病毒', 'WebShell', '恶意行为检测', '防暴力破解', '异常登录', '完整性监控', '行为模型', '虚拟补丁'].includes(data.dataType) }
          },
          sourceType: { label: '源类型', group: 'detail', search: { hidden: true }, table: { hidden: true }, form: {},
            info: { hidden: (data) => !['微隔离', '虚拟补丁'].includes(data.dataType), formatter: (value) => (String(value) === '0' ? '主机' : '容器') }
          },
          sourceId: { label: '源ID', group: 'detail', search: { hidden: true }, table: { hidden: true }, form: {},
            info: { hidden: (data) => !['微隔离', '虚拟补丁'].includes(data.dataType) }
          },
          destType: { label: '目的类型', group: 'detail', search: { hidden: true }, table: { hidden: true }, form: {},
            info: { hidden: (data) => !['微隔离', '虚拟补丁'].includes(data.dataType), formatter: (value) => (String(value) === '0' ? '主机' : '容器') }
          },
          destId: { label: '目的对象ID', group: 'detail', search: { hidden: true }, table: { hidden: true }, form: {},
            info: { hidden: (data) => !['微隔离', '虚拟补丁'].includes(data.dataType) }
          },
          destObject: { label: '目的对象', group: 'detail', search: { hidden: true }, table: { hidden: true }, form: {},
            info: { hidden: (data) => !['行为模型'].includes(data.dataType) }
          },
          policyName: { label: '策略名', group: 'detail', search: { hidden: true }, table: { hidden: true }, form: {},
            info: { hidden: (data) => !['微隔离', '防病毒', 'WebShell', '恶意行为检测', '防暴力破解', '异常登录', '完整性监控', '行为模型', '虚拟补丁', '镜像运行控制'].includes(data.dataType) }
          },
          protocol: { label: '协议', group: 'detail', search: { hidden: true }, table: { hidden: true }, form: {},
            info: { hidden: (data) => !['微隔离'].includes(data.dataType), formatter: (value) => (String(value) === '100' ? 'TCP' : (String(value) === '101' ? 'UDP' : (String(value) === '102' ? 'ICMP' : (String(value) === '11' ? 'DNS' : '未知')))) }
          },
          sourceAddress: { label: '源地址', group: 'detail', search: { hidden: true }, table: { hidden: true }, form: {},
            info: { hidden: (data) => !['微隔离'].includes(data.dataType) }
          },
          sourcePort: { label: '源端口', group: 'detail', search: { hidden: true }, table: { hidden: true }, form: {},
            info: { hidden: (data) => !['微隔离'].includes(data.dataType) }
          },
          destAddress: { label: '目的地址', group: 'detail', search: { hidden: true }, table: { hidden: true }, form: {},
            info: { hidden: (data) => !['微隔离'].includes(data.dataType) }
          },
          destPort: { label: '目的端口', group: 'detail', search: { hidden: true }, table: { hidden: true }, form: {},
            info: { hidden: (data) => !['微隔离', '虚拟补丁'].includes(data.dataType) }
          },
          direction: { label: '方向', group: 'detail', search: { hidden: true }, table: { hidden: true }, form: {},
            info: { hidden: (data) => !['微隔离'].includes(data.dataType), formatter: (value) => (String(value) === '0' ? '入' : '出') }
          },
          domain: { label: '安全域', group: 'detail', search: { hidden: true }, table: { hidden: true }, form: {},
            info: { hidden: (data) => !['微隔离'].includes(data.dataType) }
          },
          url: { label: 'URL', group: 'detail', search: { hidden: true }, table: { hidden: true }, form: {},
            info: { hidden: (data) => !['微隔离'].includes(data.dataType) }
          },
          action: { label: '处理动作', group: 'detail', search: { hidden: true }, table: { hidden: true }, form: {},
            info: { hidden: (data) => !['微隔离', '防病毒', 'WebShell', '恶意行为检测', '防暴力破解', '行为模型', '虚拟补丁', '镜像运行控制'].includes(data.dataType) }
          },
          systemType: { label: '系统类型', group: 'detail', search: { hidden: true }, table: { hidden: true }, form: {},
            info: { hidden: (data) => !['微隔离', '防病毒', 'WebShell', '恶意行为检测', '防暴力破解', '异常登录', '完整性监控', '行为模型', '虚拟补丁'].includes(data.dataType) }
          },
          totalCount: { label: '发生次数', group: 'detail', search: { hidden: true }, table: { hidden: true }, form: {},
            info: { hidden: (data) => !['微隔离'].includes(data.dataType) }
          },
          objIp: { label: '采集对象IP', group: 'detail', search: { hidden: true }, table: { hidden: true }, form: {},
            info: { hidden: (data) => !['防病毒', 'WebShell', '恶意行为检测', '防暴力破解', '异常登录', '完整性监控', '行为模型'].includes(data.dataType) }
          },
          malwareName: { label: '恶意程序名', group: 'detail', search: { hidden: true }, table: { hidden: true }, form: {},
            info: { hidden: (data) => !['防病毒'].includes(data.dataType) }
          },
          filePath: { label: '文件路径', group: 'detail', search: { hidden: true }, table: { hidden: true }, form: {},
            info: { hidden: (data) => !['防病毒', 'WebShell', '完整性监控'].includes(data.dataType) }
          },
          quarantinePath: { label: '隔离路径', group: 'detail', search: { hidden: true }, table: { hidden: true }, form: {},
            info: { hidden: (data) => !['防病毒', 'WebShell'].includes(data.dataType) }
          },
          md5: { label: '文件MD5', group: 'detail', search: { hidden: true }, table: { hidden: true }, form: {},
            info: { hidden: (data) => !['防病毒', 'WebShell'].includes(data.dataType) }
          },
          webshellName: { label: 'WebShell名称', group: 'detail', search: { hidden: true }, table: { hidden: true }, form: {},
            info: { hidden: (data) => !['WebShell'].includes(data.dataType) }
          },
          rulesId: { label: '规则ID', group: 'detail', search: { hidden: true }, table: { hidden: true }, form: {},
            info: { hidden: (data) => !['恶意行为检测', '虚拟补丁'].includes(data.dataType) }
          },
          ruleName: { label: '规则名', group: 'detail', search: { hidden: true }, table: { hidden: true }, form: {},
            info: { hidden: (data) => !['恶意行为检测', '完整性监控', '虚拟补丁'].includes(data.dataType) }
          },
          service: { label: '服务名称', group: 'detail', search: { hidden: true }, table: { hidden: true }, form: {},
            info: { hidden: (data) => !['防暴力破解'].includes(data.dataType) }
          },
          attackAccount: { label: '攻击账号', group: 'detail', search: { hidden: true }, table: { hidden: true }, form: {},
            info: { hidden: (data) => !['防暴力破解'].includes(data.dataType) }
          },
          attackSource: { label: '攻击源IP', group: 'detail', search: { hidden: true }, table: { hidden: true }, form: {},
            info: { hidden: (data) => !['防暴力破解'].includes(data.dataType) }
          },
          attackCount: { label: '攻击次数', group: 'detail', search: { hidden: true }, table: { hidden: true }, form: {},
            info: { hidden: (data) => !['防暴力破解'].includes(data.dataType) }
          },
          loginUser: { label: '登录用户', group: 'detail', search: { hidden: true }, table: { hidden: true }, form: {},
            info: { hidden: (data) => !['异常登录'].includes(data.dataType) }
          },
          loginIp: { label: '登录IP', group: 'detail', search: { hidden: true }, table: { hidden: true }, form: {},
            info: { hidden: (data) => !['异常登录'].includes(data.dataType) }
          },
          content: { label: '内容', group: 'detail', search: { hidden: true }, table: { hidden: true }, form: {},
            info: { hidden: (data) => !['完整性监控'].includes(data.dataType) }
          },
          event: { label: '事件', group: 'detail', search: { hidden: true }, table: { hidden: true }, form: {},
            info: { hidden: (data) => !['完整性监控'].includes(data.dataType), formatter: (value) => (String(value) === '0' ? '文件' : (String(value) === '1' ? '注册表' : '目录')) }
          },
          sourceObject: { label: '源对象', group: 'detail', search: { hidden: true }, table: { hidden: true }, form: {},
            info: { hidden: (data) => !['行为模型'].includes(data.dataType) }
          },
          behavior: { label: '行为', group: 'detail', search: { hidden: true }, table: { hidden: true }, form: {},
            info: { hidden: (data) => !['行为模型'].includes(data.dataType) }
          },
          quantity: { label: '数量', group: 'detail', search: { hidden: true }, table: { hidden: true }, form: {},
            info: { hidden: (data) => !['行为模型'].includes(data.dataType) }
          },
          attackType: { label: '攻击类型', group: 'detail', search: { hidden: true }, table: { hidden: true }, form: {},
            info: { hidden: (data) => !['虚拟补丁'].includes(data.dataType) }
          },
          sourceIp: { label: '源IP', group: 'detail', search: { hidden: true }, table: { hidden: true }, form: {},
            info: { hidden: (data) => !['虚拟补丁'].includes(data.dataType) }
          },
          destIp: { label: '目的IP', group: 'detail', search: { hidden: true }, table: { hidden: true }, form: {},
            info: { hidden: (data) => !['虚拟补丁'].includes(data.dataType) }
          },
          packetCapture: { label: '抓包包名', group: 'detail', search: { hidden: true }, table: { hidden: true }, form: {},
            info: { hidden: (data) => !['虚拟补丁'].includes(data.dataType) }
          },
          imageId: { label: '镜像ID', group: 'detail', search: { hidden: true }, table: { hidden: true }, form: {},
            info: { hidden: (data) => !['镜像运行控制'].includes(data.dataType) }
          },
          imageName: { label: '镜像名', group: 'detail', search: { hidden: true }, table: { hidden: true }, form: {},
            info: { hidden: (data) => !['镜像运行控制'].includes(data.dataType) }
          },
          source: { label: '来源', group: 'detail', search: { hidden: true }, table: { hidden: true }, form: {},
            info: { hidden: (data) => !['镜像运行控制'].includes(data.dataType) }
          },
          reason: { label: '原因', group: 'detail', search: { hidden: true }, table: { hidden: true }, form: {},
            info: { hidden: (data) => !['镜像运行控制'].includes(data.dataType) }
          },
          details: { label: '细节信息', group: 'detail', search: { hidden: true }, table: { hidden: true }, form: {},
            info: { hidden: (data) => !['系统告警', '恶意行为检测', '防暴力破解', '异常登录'].includes(data.dataType) }
          },
          tenantId: { label: '告警租户ID', group: 'detail', search: { hidden: true }, table: { hidden: true }, form: {}},
          msg: {
            label: '原始消息内容',
            group: 'detail',
            search: { hidden: true },
            table: { hidden: true },
            form: {}
          }
        }
      }

      return value
    }
  },
  methods: {
    handleAssign(ids, args = {}) {
      const params = {
        ids,
        riskType: this.riskType,
        disposeType: this.disposeType,
        groupKey: 'basic_type_middle_dim'
      }

      this.$refs.assignDialogRef.open({
        ...args,
        params,
        success: () => {
          this.$refs.sheetRef.getTableData()
        }
      })
    },
    async onBackClick(ids, selection) {
      const params = Array.isArray(ids)
        ? {
          ids,
          selection: selection
        }
        : {
          id: ids[0]
        }

      Object.assign(params, {
        riskType: this.riskType
        // riskCategory: ''
      })

      this.$refs.returnDialogRef.open({
        params,
        success: () => {
          this.$refs.sheetRef.getTableData()
        }
      })
    },
    onTurnClick(ids) {
      this.$refs.turnDialogRef.open({
        params: {
          riskType: this.scopeRiskType,
          riskIds: ids
        },
        success: () => {
          this.$refs.sheetRef.getTableData()
        }
      })
    },
    onMarkClick(ids) {
      this.$refs.markDialogRef.open({
        params: {
          riskContainerSafetyType: this.riskContainerSafetyType,
          ids
        },
        success: () => {
          this.$refs.sheetRef.getTableData()
        }
      })
    },
    onDisposeClick(ids, selection) {
      let params = {}

      if (Array.isArray(ids)) {
        params = {
          ids,
          selection
        }
      } else {
        params = {
          id: ids[0]
        }
      }

      Object.assign(params, {
        riskType: this.riskType
        // riskCategory: ''
      })

      this.$refs.disposeDialogRef.open({
        params,
        success: () => {
          this.$refs.sheetRef.getTableData()
        }
      })
    },
    onWhiteClick(ids) {
      this.$refs.whiteDialogRef.open({
        params: {
          riskContainerSafetyType: this.riskContainerSafetyType,
          ids
        },
        success: () => {
          this.$refs.sheetRef.getTableData()
        }
      })
    },
    onAnalyzeClick(row) {
      this.$refs.analyzeDialogRef.open({
        sheetProps: this.sheetProps,
        params: {
          id: row.id
        },
        success: () => {
          this.$refs.sheetRef.getTableData()
        }
      })
    }
  }
}
</script>

<style></style>
