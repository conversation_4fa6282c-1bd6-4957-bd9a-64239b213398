<template>
  <div class="page-main !pb-4">
    <EleFormSearch
      ref="formSearchRef"
      v-slot="{ model, query, reset }"
      :default-model="defaultModel"
      class="sa-query"
      @query="getTableData"
    >
      <el-form-item label="维度" class="">
        <el-select v-model="model.dimension" placeholder="请选择" clearable filterable>
          <el-option label="月份" value="month" />
          <el-option label="申请人" value="apply" />
        </el-select>
      </el-form-item>

      <el-form-item v-if="['month'].includes(model.dimension)" label="时间区间" class="daterange">
        <EleDatePickerRange
          type="daterange"
          value-format="yyyy-MM-dd"
          format="yyyy-MM-dd"
          :start-value.sync="model.startTime"
          :end-value.sync="model.endTime"
          :picker-options="pickerOptions"
          :default-time="['00:00:00', '23:59:59']"
        />
      </el-form-item>

      <el-form-item v-if="['apply'].includes(model.dimension)" label="申请人" class="">
        <ApplySelect v-model="model.name" />
      </el-form-item>

      <el-form-item class="query-handle">
        <el-button
          type="primary"
          plain
          :title="isDisabledSearch ? disabledTip : ''"
          :disabled="isDisabledSearch"
          icon="el-icon-search"
          @click="query"
        >搜索</el-button>
        <el-button icon="el-icon-refresh-left" @click="reset">重置</el-button>
      </el-form-item>
    </EleFormSearch>

    <div class="sa-title">
      <el-button
        type="primary"
        icon="el-icon-download"
        :disabled="isDisabledSearch || !$checkPermi(['risk:riskReportForms:safety:export'])"
        :title="isDisabledSearch ? disabledTip : ''"
        @click="handleExport"
      >导出</el-button>
    </div>

    <el-table
      :key="tableData.length"
      v-loading="loading"
      border
      class="!overflow-hidden !rounded-lg sa-table"
      :data="tableData"
      v-bind="{ spanMethod }"
      style="width: 100%"
    >
      <el-table-column prop="dimension" label="月份/申请人" width="100" />
      <el-table-column prop="安全任务总数" label="安全任务总数" align="center" width="150">
        <template #header>
          <div class="flex items-center justify-center w-full space-x-1">
            <div class="">安全任务总数</div>
            <el-tooltip
              class="item"
              effect="dark"
              content="说明计算公式：包括系统漏扫，基线，弱口令，镜像扫描，黄金镜像任务总数累加"
              placement="top"
            >
              <el-link type="info" :underline="false">
                <i class="el-icon-question" />
              </el-link>
            </el-tooltip>
          </div>
        </template>
      </el-table-column>
      <el-table-column v-slot="{ row }" prop="" label="系统漏扫任务" align="center" width="150">
        <el-link
          type="primary"
          :underline="true"
          @click="handleInfo({ row, taskType: '3', title: '系统漏扫任务' })"
        >
          {{ row['系统漏扫任务'] }}
        </el-link>
      </el-table-column>
      <el-table-column v-slot="{ row }" prop="" label="基线任务" align="center">
        <el-link
          type="primary"
          :underline="true"
          @click="handleInfo({ row, taskType: '4', title: '基线任务' })"
        >
          {{ row['基线任务'] }}
        </el-link>
      </el-table-column>
      <el-table-column v-slot="{ row }" prop="" label="弱口令任务" align="center" width="150">
        <el-link
          type="primary"
          :underline="true"
          @click="handleInfo({ row, taskType: '5', title: '弱口令任务' })"
        >
          {{ row['弱口令任务'] }}
        </el-link>
      </el-table-column>
      <el-table-column v-slot="{ row }" prop="" label="镜像扫描任务" align="center" width="150">
        <el-link
          type="primary"
          :underline="true"
          @click="handleInfo({ row, taskType: '6', title: '镜像扫描任务' })"
        >
          {{ row['镜像扫描任务'] }}
        </el-link>
      </el-table-column>
      <el-table-column v-slot="{ row }" prop="" label="黄金镜像任务" align="center" width="150">
        <el-link
          type="primary"
          :underline="true"
          @click="handleInfo({ row, taskType: '2', title: '黄金镜像任务' })"
        >
          {{ row['黄金镜像任务'] }}
        </el-link>
      </el-table-column>
      <el-table-column prop="" label="检查主机总数" align="center" width="150">
        <template #header>
          <div class="flex items-center justify-center w-full space-x-1">
            <div class="">检查主机总数</div>
            <el-tooltip
              class="item"
              effect="dark"
              content="说明计算公式：系统漏洞，基线，弱口令去重累加"
              placement="top"
            >
              <el-link type="info" :underline="false">
                <i class="el-icon-question" />
              </el-link>
            </el-tooltip>
          </div>
        </template>

        <template #default="{ row }">
          <el-link
            type="primary"
            :underline="true"
            @click="handleInfoHost({ row, title: '检查主机总数' })"
          >
            {{ row['检查主机总数'] }}
          </el-link>
        </template>
      </el-table-column>
      <el-table-column prop="" label="安全问题总数" align="center" width="150">
        <template #header>
          <div class="flex items-center justify-center w-full space-x-1">
            <div class="">安全问题总数</div>
            <el-tooltip
              class="item"
              effect="dark"
              content="说明计算公式：包括系统漏扫，基线，弱口令，镜像扫描，黄金镜像任务总数累加"
              placement="top"
            >
              <el-link type="info" :underline="false">
                <i class="el-icon-question" />
              </el-link>
            </el-tooltip>
          </div>
        </template>

        <template #default="{ row }">
          <el-link
            type="primary"
            :underline="true"
            @click="handleInfoProblem({ row, title: '安全任务' })"
          >
            {{ row['安全问题总数'] }}
          </el-link>
        </template>
      </el-table-column>
      <el-table-column prop="" label="安全问题处置总数" align="center" width="160">
        <template #header>
          <div class="flex items-center justify-center w-full space-x-1">
            <div class="">安全问题处置总数</div>
            <el-tooltip
              class="item"
              effect="dark"
              content="说明计算公式：包括系统漏扫，基线，弱口令，镜像扫描安全问题已处置数量累加"
              placement="top"
            >
              <el-link type="info" :underline="false">
                <i class="el-icon-question" />
              </el-link>
            </el-tooltip>
          </div>
        </template>

        <template #default="{ row }">
          <el-link
            type="primary"
            :underline="true"
            @click="handleInfoProblem({ row, title: '已处置安全任务', disposed: true })"
          >
            {{ row['安全问题处置总数'] }}
          </el-link>
        </template>
      </el-table-column>
      <el-table-column prop="安全任务处置率" label="安全任务处置率" align="center" width="150" />
    </el-table>

    <TaskDialog ref="taskDialogRef" />
    <HostDialog ref="hostDialogRef" />
    <ProblemDialog ref="problemDialogRef" />
  </div>
</template>

<script>
import dayjs from 'dayjs'

import request from '@/utils/request.js'
import DetectSelect from '@/views/risk/riskReportForms/detect/components/DetectSelect/index.vue'
import { spanRow } from 'element-ui-table-span-method'

import ApplySelect from './components/ApplySelect/index.vue'

// import getRoleType from '@/utils/getRoleType.js';

import disabledDate from '@/utils/disabledDate.js'

import TaskDialog from './components/TaskDialog/index.vue'
import ProblemDialog from './components/ProblemDialog/index.vue'
import HostDialog from './components/HostDialog/index.vue'

export default {
  components: {
    DetectSelect,
    ApplySelect,
    TaskDialog,
    ProblemDialog,
    HostDialog
  },
  data() {
    return {
      loading: false,
      tableData: [],
      mergeOptions: [],

      pickerOptions: {
        disabledDate
      },

      defaultModel: {
        name: void 0,
        dimension: 'month',
        startTime: dayjs().startOf('day').subtract(3, 'day').format('YYYY-MM-DD'),
        endTime: dayjs().endOf('day').format('YYYY-MM-DD')

        // ...(['other'].includes(getRoleType())
        //   ? {
        //       dimension: 'apply',
        //     }
        //   : {
        //       dimension: 'month',
        //       startTime: dayjs().startOf('day').subtract(3, 'day').format('YYYY-MM-DD'),
        //       endTime: dayjs().endOf('day').format('YYYY-MM-DD'),
        //     }),
        // roleType: getRoleType(),
      },

      formSearchRef: {}
    }
  },
  computed: {
    isDisabledSearch() {
      const model = this.formSearchRef.model || {}

      if (['month'].includes(model.dimension) && model.startTime && model.endTime) {
        return false
      }

      if (['apply'].includes(model.dimension) && model.name) {
        return false
      }

      return true
    },
    disabledTip() {
      const model = this.formSearchRef.model || {}

      if (['month'].includes(model.dimension)) {
        return '请先选择时间区间'
      } else {
        return '请先选择申请人'
      }
    }
  },
  async mounted() {
    await this.$nextTick()
    this.formSearchRef = this.$refs.formSearchRef
    this.getTableData()
  },
  methods: {
    async handleInfo({ row, title, ...params } = {}) {
      const model = this.formSearchRef.model

      this.$refs.taskDialogRef.open({
        ...params,
        title: `${title}列表`,
        ...(['month'].includes(model.dimension)
          ? {
            month: row.月份,
            startTime: model.startTime,
            endTime: model.endTime
          }
          : {
            applicant: row.申请人
          })
      })
    },
    handleInfoHost({ row }) {
      const model = this.formSearchRef.model

      this.$refs.hostDialogRef.open({
        title: '检查主机列表',
        ...(['month'].includes(model.dimension)
          ? {
            type: 'time',
            month: row.月份,
            startTime: model.startTime,
            endTime: model.endTime
          }
          : {
            type: 'name',
            name: row.申请人
          })
      })
    },
    handleInfoProblem({ row, title, ...params }) {
      const model = this.formSearchRef.model

      this.$refs.problemDialogRef.open({
        title: `${title}列表`,
        ...params,
        ...(['month'].includes(model.dimension)
          ? {
            type: 'time',
            month: row.月份,
            startTime: model.startTime,
            endTime: model.endTime
          }
          : {
            type: 'name',
            name: row.申请人
          })
      })
    },

    handleExport() {
      const params = this.formSearchRef.model

      if (['month'].includes(params.dimension)) {
        this.download(
          '/risk/riskReportForms/exportSecurityTaskAnalyzeByTemplate',
          {
            startTime: params.startTime,
            endTime: params.endTime
          },
          `安全任务分析_${new Date().getTime()}.xlsx`
        )

        return
      }

      this.download(
        '/risk/riskReportForms/exportSecurityTaskAnalyzeByNameTemplate',
        {
          name: params.name
        },
        `安全任务分析_${new Date().getTime()}.xlsx`
      )
    },
    async getTableData() {
      this.loading = true

      const params = this.$refs.formSearchRef.model

      let res

      if (['month'].includes(params.dimension)) {
        res = await request({
          url: '/risk/riskReportForms/securityTaskAnalyze',
          method: 'get',
          params: {
            ...params
          }
        })

        this.tableData = (res.data || []).flat().map((item) => ({
          dimension: item.月份,
          ...item
        }))
      } else if (['apply'].includes(params.dimension)) {
        res = await request({
          url: '/risk/riskReportForms/securityTaskAnalyzeByName',
          method: 'get',
          params: {
            ...params
          }
        })

        this.tableData = (res.data || []).flat().map((item) => ({
          dimension: item.申请人,
          ...item
        }))
      }

      this.loading = false
    },
    spanMethod({ row, column, rowIndex, columnIndex }) {
      return spanRow({ row, column, rowIndex, columnIndex }, this.tableData, this.mergeOptions)
    }
  }
}
</script>

<style></style>
