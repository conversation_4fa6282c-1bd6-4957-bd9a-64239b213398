<template>
  <div class="page-main !h-[var(--app-main-height)] !pb-4 flex flex-col">
    <EleFormSearch
      ref="formSearchRef"
      v-slot="{ model, query, reset }"
      :default-model="defaultModel"
      class="sa-query flex-none"
      @query="onQuery"
    >
      <el-form-item label="时间区间" class="daterange">
        <EleDatePickerRange
          type="daterange"
          value-format="yyyy-MM-dd"
          format="yyyy-MM-dd"
          :start-value.sync="model.startTime"
          :end-value.sync="model.endTime"
          :picker-options="pickerOptions"
          :default-time="['00:00:00', '23:59:59']"
        />
      </el-form-item>

      <el-form-item class="query-handle">
        <el-button type="primary" plain icon="el-icon-search" @click="query">搜索</el-button>
        <el-button icon="el-icon-refresh-left" @click="reset">重置</el-button>
      </el-form-item>
    </EleFormSearch>

    <div class="sa-title flex-none">
      <el-button
        type="primary"
        icon="el-icon-download"
        :disabled="!$checkPermi(['risk:riskReportForms:knowledge:export'])"
        @click="handleExport"
      >导出</el-button>
    </div>

    <div class="flex-1 h-0 overflow-hidden">
      <StatsTable ref="statsTableRef" :key="statsKey" v-loading="loading" v-bind="{ statsData }" />
    </div>
  </div>
</template>

<script>
import dayjs from 'dayjs'

import StatsTable from './components/StatsTable/index.vue'

import request from '@/utils/request.js'

import disabledDate from '@/utils/disabledDate.js'

export default {
  components: {
    StatsTable
  },
  data() {
    return {
      loading: false,
      pickerOptions: {
        disabledDate
      },

      defaultModel: {
        startTime: dayjs().startOf('day').subtract(3, 'day').format('YYYY-MM-DD'),
        endTime: dayjs().endOf('day').format('YYYY-MM-DD')
      },

      statsData: []
    }
  },
  computed: {
    statsKey() {
      return JSON.stringify(this.statsData)
    }
  },
  async mounted() {
    await this.$nextTick()
    this.getStatsData()
  },
  methods: {
    handleExport() {
      this.download(
        '/risk/riskReportForms/exportKnowledgeSituation',
        {
          ...this.$refs.formSearchRef.model
        },
        `知识库分析_${new Date().getTime()}.xlsx`
      )
    },
    onQuery() {
      this.getStatsData()
    },
    async getStatsData() {
      this.loading = true

      const res = await request({
        url: '/risk/riskReportForms/knowledgeAnalyze',
        method: 'get',
        params: {
          ...this.$refs.formSearchRef.model
        }
      })

      this.loading = false

      if (res.code === 200) {
        this.statsData = res.data
      }
    }
  }
}
</script>

<style></style>
