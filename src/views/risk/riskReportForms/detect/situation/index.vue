<template>
  <div class="page-main !h-[var(--app-main-height)] !pb-4 flex flex-col">
    <EleFormSearch
      ref="formSearchRef"
      v-slot="{ model, query, reset }"
      :default-model="defaultModel"
      class="sa-query flex-none"
      @query="getTableData"
    >
      <el-form-item label="基础检测任务" class="!w-96">
        <DetectSelect v-model="model.taskIds" multiple class="!w-full" @loaded="onDetectLoaded" />
      </el-form-item>

      <el-form-item class="query-handle">
        <el-button type="primary" plain icon="el-icon-search" @click="query">搜索</el-button>
        <el-button icon="el-icon-refresh-left" @click="reset">重置</el-button>
      </el-form-item>
    </EleFormSearch>

    <div class="sa-title flex-none">
      <el-button
        type="primary"
        icon="el-icon-download"
        :disabled="isDisabledExport"
        @click="handleExport"
      >导出</el-button>
    </div>

    <div class="flex-1 h-0 overflow-hidden">
      <el-table
        :key="[tableData.length, monthColumns.length].join(',')"
        v-loading="loading"
        :data="tableData"
        border
        v-bind="{ spanMethod }"
        height="100%"
        style="width: 100%"
        class="!overflow-hidden !rounded-lg"
      >
        <el-table-column prop="运维组" label="运维组" align="center" show-overflow-tooltip />

        <el-table-column
          v-for="(month, monthIndex) of monthColumns"
          :key="month"
          :label="month"
          align="center"
        >
          <el-table-column
            v-for="(task, taskIndex) of typeColumns"
            :key="task"
            :label="task"
            align="center"
          >
            <el-table-column v-slot="{ row }" label="总数" align="center">
              <el-link
                type="primary"
                :underline="true"
                @click="
                  handleInfo({
                    row: row[`${month}${task}`],
                    title: '总数',
                  })
                "
              >
                {{ row[`${month}${task}`]['总数'] }}
              </el-link>
            </el-table-column>
            <el-table-column v-slot="{ row }" label="未处置数" align="center">
              <el-link
                type="primary"
                :underline="true"
                @click="
                  handleInfo({
                    row: row[`${month}${task}`],
                    statusList: ['1', '5'],
                    title: '未处置数',
                  })
                "
              >
                {{ row[`${month}${task}`]['未处置数'] }}
              </el-link>
            </el-table-column>
            <el-table-column prop="" label="处置率" align="center">
              <template #header>
                <div class="flex items-center justify-center w-full space-x-1">
                  <div class="">处置率</div>
                  <el-tooltip
                    class="item"
                    effect="dark"
                    content="说明计算公式：已处置数量/新增安全问题数量"
                    placement="top"
                  >
                    <el-link type="info" :underline="false">
                      <i class="el-icon-question" />
                    </el-link>
                  </el-tooltip>
                </div>
              </template>

              <template #default="{ row }">
                <RateLink :value="row[`${month}${task}`]['处置率']" preset="dispose" />
              </template>
            </el-table-column>
            <el-table-column prop="" label="复现数" align="center">
              <template #header>
                <div class="flex items-center justify-center w-full space-x-1">
                  <div class="">复现数</div>
                  <el-tooltip
                    class="item"
                    effect="dark"
                    content="当月处置总数在当月之后发现问题总数范围内的数量(去重后)"
                    placement="top"
                  >
                    <el-link type="info" :underline="false">
                      <i class="el-icon-question" />
                    </el-link>
                  </el-tooltip>
                </div>
              </template>

              <template #default="{ row }">
                <el-link
                  type="primary"
                  :underline="true"
                  @click="
                    handleInfo({
                      row: row[`${month}${task}`],
                      title: '复现数',
                      reappear: true,
                    })
                  "
                >
                  {{ row[`${month}${task}`]['复现数'] }}
                </el-link>
              </template>
            </el-table-column>
            <el-table-column prop="" label="复现率" align="center">
              <template #header>
                <div class="flex items-center justify-center w-full space-x-1">
                  <div class="">复现率</div>
                  <el-tooltip
                    class="item"
                    effect="dark"
                    content="说明计算公式：复现数量/当月处置总数"
                    placement="top"
                  >
                    <el-link type="info" :underline="false">
                      <i class="el-icon-question" />
                    </el-link>
                  </el-tooltip>
                </div>
              </template>

              <template #default="{ row }">
                <RateLink :value="row[`${month}${task}`]['复现率']" preset="reappear" />
              </template>
            </el-table-column>
          </el-table-column>
        </el-table-column>
      </el-table>
    </div>

    <ProblemDialog ref="problemDialogRef" />
  </div>
</template>

<script>
import request from '@/utils/request.js'
import DetectSelect from '@/views/risk/riskReportForms/detect/components/DetectSelect/index.vue'
import { spanRow } from 'element-ui-table-span-method'
import { groupBy } from 'lodash-es'

import getRoleType from '@/utils/getRoleType.js'

import ProblemDialog from './components/ProblemDialog/index.vue'

export default {
  components: {
    DetectSelect,
    ProblemDialog
  },
  data() {
    return {
      loading: false,
      tableData: [],
      mergeOptions: [{ index: 0, field: '运维组' }],

      monthColumns: [],
      typeColumns: [],

      tableMap: {},

      defaultModel: {
        taskIds: [],
        roleType: getRoleType()
      },

      formSearchRef: {}
    }
  },
  computed: {
    isDisabledExport() {
      return !this.formSearchRef.model?.taskIds?.length || !this.$checkPermi(['risk:riskReportForms:detect:situation:export'])
    }
  },
  async mounted() {
    await this.$nextTick()
    this.formSearchRef = this.$refs.formSearchRef
  },
  methods: {
    handleInfo({ row, title, reappear, ...params } = {}) {
      const taskType = {
        系统漏洞: 'system',
        基线: 'line',
        弱口令: 'password'
      }[row.类型]

      this.$refs.problemDialogRef.open({
        title: `${row.类型}问题${title}列表`,
        ...params,
        taskType,
        taskId: row['任务Id'],
        reappear,
        ...(reappear
          ? {
            riskType: taskType,
            groupId: row['运维组Id']
          }
          : {
            riskOperatorGroupId: row['运维组Id']
          })
      })
    },
    async onDetectLoaded(data) {
      Object.assign(this.defaultModel, {
        taskIds: [data[0].value]
      })

      await this.$nextTick()

      this.getTableData()
    },
    handleExport() {
      const params = this.formSearchRef.model
      this.download(
        '/risk/riskReportForms/exportDisposeContextAnalyzeTemplate',
        {
          taskIds: params.taskIds.join(','),
          roleType: params.roleType
        },
        `运维处置情况分析_${new Date().getTime()}.xlsx`
      )
    },
    async getTableData() {
      this.loading = true

      const res = await request({
        url: '/risk/riskReportForms/disposeContextAnalyze',
        method: 'get',
        params: {
          ...this.$refs.formSearchRef.model,
          taskIds: this.$refs.formSearchRef.model.taskIds.join(',')
        }
      })

      this.loading = false

      const data = (res.data || []).flat()

      this.monthColumns = Object.keys(groupBy(data, '月份'))
      this.typeColumns = Object.keys(groupBy(data, '类型'))

      this.tableMap = data.reduce((obj, item) => {
        const key = `${item.运维组}${item.月份}${item.类型}`
        obj[key] = {
          ...item
        }
        return obj
      }, {})

      const orgMap = groupBy(Object.values(this.tableMap), '运维组')

      this.tableData = Object.keys(orgMap).map((item) => ({
        运维组: item,
        ...Object.entries(this.tableMap).reduce((obj, [key, value]) => {
          const findKey = item
          if (key.includes(findKey)) {
            obj[key.replace(findKey, '')] = value
          }
          return obj
        }, {})
      }))
    },
    spanMethod({ row, column, rowIndex, columnIndex }) {
      return spanRow({ row, column, rowIndex, columnIndex }, this.tableData, this.mergeOptions)
    }
  }
}
</script>

<style></style>
