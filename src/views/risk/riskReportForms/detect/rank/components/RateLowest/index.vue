<template>
  <div class="">
    <div class="flex items-center space-x-2 pb-2">
      <el-button
        type="primary"
        plain
        icon="el-icon-refresh-right"
        v-bind="{ loading }"
        @click="handleRefresh"
      >
        刷新
      </el-button>
      <el-button
        v-if="$checkPermi(['risk:riskReportForms:detect:rank:export'])"
        type="primary"
        icon="el-icon-download"
        @click="handleExport"
      >导出</el-button>

      <el-select
        v-model="searchParams.topNumber"
        placeholder="请选择排名位数"
        class="!w-36"
        @change="getTableData"
      >
        <el-option
          v-for="(item, index) in [5, 10, 20, 50]"
          :key="index"
          :label="'TOP' + item"
          :value="item"
        />
      </el-select>
    </div>

    <div class="text-white bg-[#D25D5A] py-2 text-center">{{ title }}</div>

    <div v-loading="loading" class="h-64">
      <el-table :data="tableData" style="width: 100%" height="100%" border>
        <el-table-column type="index" label="排名" width="55" align="center" />
        <el-table-column prop="团队名称" label="团队名称" align="left" show-overflow-tooltip />
        <el-table-column v-slot="{ row }" prop="修复率" label="修复率" align="center">
          <RateLink
            preset="repair"
            :value="row['修复率']"
            underline
            @click="$emit('info', { groupId: row.团队ID, type: 'disposed', title })"
          />
        </el-table-column>
        <el-table-column prop="团队成员" label="团队成员" align="center" show-overflow-tooltip />
      </el-table>
    </div>
  </div>
</template>

<script>
import request from '@/utils/request.js'
export default {
  data() {
    return {
      loading: false,
      title: '修复率最低团队TOP排名',
      tableData: [],
      searchParams: {
        type: 'asc',
        topNumber: 5
      }
    }
  },
  created() {
    this.getTableData()
  },
  methods: {
    handleExport() {
      this.download(
        '/risk/riskReportForms/exportTeamDisposeRateAnalyze',
        {
          ...this.searchParams
        },
        `${this.title}模板_${new Date().getTime()}.xlsx`
      )
    },
    handleRefresh() {
      this.getTableData()
    },
    async getTableData() {
      this.loading = true
      const res = await request({
        method: 'get',
        url: '/risk/riskReportForms/disposalRateRank',
        params: {
          ...this.searchParams
        }
      })
      this.loading = false

      this.tableData = res.data
    }
  }
}
</script>

<style></style>
