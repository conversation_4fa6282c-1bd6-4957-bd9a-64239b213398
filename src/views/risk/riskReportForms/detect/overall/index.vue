<template>
  <div class="page-main !pb-4">
    <EleFormSearch
      ref="formSearchRef"
      v-slot="{ model, query, reset }"
      :default-model="defaultModel"
      class="sa-query"
      @query="getTableData"
    >
      <el-form-item label="基础检测任务" class="!w-96">
        <DetectSelect v-model="model.taskIds" multiple class="!w-full" @loaded="onDetectLoaded" />
      </el-form-item>

      <el-form-item class="query-handle">
        <el-button type="primary" plain icon="el-icon-search" @click="query">搜索</el-button>
        <el-button icon="el-icon-refresh-left" @click="reset">重置</el-button>
      </el-form-item>
    </EleFormSearch>

    <div class="sa-title">
      <el-button
        type="primary"
        icon="el-icon-download"
        :disabled="isDisabledExport"
        @click="handleExport"
      >导出</el-button>
    </div>

    <el-table
      :key="tableData.length"
      v-loading="loading"
      class="sa-table !overflow-hidden !rounded-lg"
      :data="tableData"
      border
      v-bind="{ spanMethod }"
      style="width: 100%"
    >
      <el-table-column prop="任务名称" label="任务名称" width="150" show-overflow-tooltip />
      <el-table-column prop="类型" label="类型" />
      <el-table-column prop="发现问题数量" label="发现问题数量" align="center" width="150" />
      <el-table-column v-slot="{ row }" prop="" label="新增问题数量" align="center" width="150">
        <el-link
          type="primary"
          :underline="true"
          @click="handleInfo({ row, historyRepeatFlag: '0', title: '新增问题' })"
        >
          {{ row['新增问题数量'] }}
        </el-link>
      </el-table-column>
      <el-table-column v-slot="{ row }" prop="" label="已处置数量" align="center" width="150">
        <el-link
          type="primary"
          :underline="true"
          @click="handleInfo({ row, statusList: ['2', '3', '4'], title: '已处置' })"
        >
          {{ row['已处置数量'] }}
        </el-link>
      </el-table-column>
      <el-table-column v-slot="{ row }" prop="" label="未处置数量" align="center" width="150">
        <el-link
          type="primary"
          :underline="true"
          @click="handleInfo({ row, statusList: ['1', '5'], title: '未处置' })"
        >
          {{ row['未处置数量'] }}
        </el-link>
      </el-table-column>

      <el-table-column v-slot="{ row }" prop="" label="处置率" align="center">
        <RateLink preset="dispose" :value="row['处置率']" />
      </el-table-column>

      <el-table-column prop="复现数量" label="复现数量" align="center" width="150">
        <template #header>
          <div class="flex items-center justify-center w-full space-x-1">
            <div class="">复现数量</div>
            <el-tooltip
              class="item"
              effect="dark"
              content="当月处置总数在当月之后发现问题总数范围内的数量(去重后)"
              placement="top"
            >
              <el-link type="info" :underline="false">
                <i class="el-icon-question" />
              </el-link>
            </el-tooltip>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="" label="复现率" align="center" width="150">
        <template #header>
          <div class="flex items-center justify-center w-full space-x-1">
            <div class="">复现率</div>
            <el-tooltip class="item" effect="dark" content="复现数量/当月处置总数" placement="top">
              <el-link type="info" :underline="false">
                <i class="el-icon-question" />
              </el-link>
            </el-tooltip>
          </div>
        </template>

        <template #default="{ row }">
          <RateLink preset="reappear" :value="row['复现率']" />
        </template>
      </el-table-column>
      <el-table-column prop="检查主机数量" width="150" align="center">
        <template #header>
          <div class="flex items-center justify-center w-full space-x-1">
            <div class="">检查主机数量(总)</div>
            <el-tooltip
              class="item"
              effect="dark"
              content="说明计算公式：报告解析到的主机去重总数量"
              placement="top"
            >
              <el-link type="info" :underline="false">
                <i class="el-icon-question" />
              </el-link>
            </el-tooltip>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="问题主机数量" label="问题主机数量(总)" align="center" width="150" />
      <el-table-column
        prop="检查主机总数量"
        width="180"
        align="center"
        class-name="text-green-500"
      >
        <template #header>
          <div class="flex items-center justify-center w-full space-x-1">
            <div class="">检查主机总数量(总)</div>
            <el-tooltip
              class="item"
              effect="dark"
              content="说明计算公式：系统漏洞，基线，弱口令去重累加"
              placement="top"
            >
              <el-link type="info" :underline="false">
                <i class="el-icon-question" />
              </el-link>
            </el-tooltip>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        prop="问题主机总数量"
        width="180"
        align="center"
        class-name="text-red-500"
      >
        <template #header>
          <div class="flex items-center justify-center w-full space-x-1">
            <div class="">问题主机总数量(总)</div>
            <el-tooltip
              class="item"
              effect="dark"
              content="说明计算公式：系统漏洞，基线，弱口令去重累加"
              placement="top"
            >
              <el-link type="info" :underline="false">
                <i class="el-icon-question" />
              </el-link>
            </el-tooltip>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <ProblemDialog ref="problemDialogRef" />
  </div>
</template>

<script>
import request from '@/utils/request.js'
import DetectSelect from '@/views/risk/riskReportForms/detect/components/DetectSelect/index.vue'
import { spanRow } from 'element-ui-table-span-method'
import ProblemDialog from './components/ProblemDialog/index.vue'

export default {
  components: {
    DetectSelect,
    ProblemDialog
  },
  data() {
    return {
      loading: false,
      tableData: [],
      mergeOptions: [
        { index: 0, field: '任务名称' },
        { index: 11, field: '检查主机总数量' },
        { index: 12, field: '问题主机总数量' }
      ],
      defaultModel: {
        taskIds: []
      },
      formSearchRef: {}
    }
  },
  computed: {
    isDisabledExport() {
      return !this.formSearchRef.model?.taskIds?.length || !this.$checkPermi(['risk:riskReportForms:detect:overall:export'])
    }
  },
  async mounted() {
    await this.$nextTick()
    this.formSearchRef = this.$refs.formSearchRef
  },
  methods: {
    async handleInfo({ row, title, ...params }) {
      const taskId = row['任务Id']

      const taskType = {
        系统漏洞: 'system',
        基线: 'line',
        弱口令: 'password'
      }[row.类型]

      this.$refs.problemDialogRef.open({
        title: `${row.类型}${title}列表`,
        taskId,
        taskType,
        ...params
      })
    },
    async onDetectLoaded(data) {
      Object.assign(this.defaultModel, {
        taskIds: [data[0].value]
      })
      await this.$nextTick()
      this.getTableData()
    },
    handleExport() {
      const taskIds = this.$refs.formSearchRef.model.taskIds.join(',')
      this.download(
        '/risk/riskReportForms/exportTotalDisposeSituation',
        {
          taskIds
        },
        `整体处置情况分析_${new Date().getTime()}.xlsx`
      )
    },
    async getTableData() {
      this.loading = true

      const res = await request({
        url: '/risk/riskReportForms/totalDisposeCount',
        method: 'post',
        data: {
          ...this.$refs.formSearchRef.model
        }
      })

      this.loading = false

      this.tableData = (res.data || []).flat()
    },
    spanMethod({ row, column, rowIndex, columnIndex }) {
      return spanRow({ row, column, rowIndex, columnIndex }, this.tableData, this.mergeOptions)
    }
  }
}
</script>

<style></style>
