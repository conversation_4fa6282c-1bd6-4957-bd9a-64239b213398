<template>
  <div class="page-main">
    <el-form
      v-show="showSearch"
      ref="queryForm"
      class="sa-query"
      :model="queryParams"
      size="small"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="业务系统" prop="systemName">
        <el-input
          v-model="queryParams.systemName"
          placeholder="请输入业务系统"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="IP" prop="ip">
        <el-input
          v-model="queryParams.ip"
          placeholder="请输入IP"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="文件名称" prop="fileName">
        <el-select
          v-model="queryParams.fileName"
          placeholder="请选择文件名称"
          clearable
          @change="handleQuery"
        >
          <el-option
            v-for="dict in riskTaskFileInfoList"
            :key="dict.key"
            :label="dict.value"
            :value="dict.key"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="是否适配模板" prop="isFitTemplate">
        <el-select
          v-model="queryParams.isFitTemplate"
          placeholder="请选择是否适配模板"
          clearable
          @change="handleQuery"
        >
          <el-option label="是" value="0" />
          <el-option label="否" value="1" />
        </el-select>
      </el-form-item>
      <el-form-item class="query-handle">
        <el-button type="primary" plain icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh-left" @click="resetQuery">重置</el-button>
        <EleSheetSearchMore :parent-ref="()=> $refs.queryForm" />
      </el-form-item>
    </el-form>

    <div class="sa-title">
      <sa-toolbar :show-search.sync="showSearch" @queryTable="getList" />

      <el-button
        v-if="$checkPermi(['risk:baseLineXmlFileInfo:list:export'])"
        type="primary"
        icon="el-icon-download"
        @click="handleExport"
      >导出列表</el-button>
    </div>

    <el-table v-loading="loading" class="sa-table" :data="list">
      <el-table-column label="是否适配模板" align="center" prop="isFitTemplate">
        <!-- <template slot="header" slot-scope="scope">
          <el-dropdown
            trigger="click"
            size="medium "
            @command="(command) => handleCommand(command, scope.row)"
          >
            <span> 是否适配模板<i class="el-icon-arrow-down el-icon--right" /> </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item command="0">是</el-dropdown-item>
              <el-dropdown-item command="1">否</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template> -->
        <template slot-scope="scope">
          {{ scope.row.isFitTemplate == 1 ? '否' : '是' }}
        </template>
      </el-table-column>
      <el-table-column label="业务系统" align="center" prop="systemName" min-width="200" />
      <el-table-column label="IP" align="center" prop="ip">
        <template v-if="$checkPermi(['risk:xmlCheckResult:list'])" slot-scope="scope">
          {{ scope.row.ip }}
        </template>
      </el-table-column>
      <el-table-column label="磐基业务系统" align="center" prop="pjSystemName" />
      <el-table-column label="磐基部门名称" align="center" prop="pjDeptName" />
      <el-table-column label="操作" align="center" fixed="right" width="200">
        <template slot-scope="scope">
          <el-button size="mini" type="text" @click="onOpenXmlCheckResult(scope.row)">结果预览</el-button>
          <el-button size="mini" type="text" @click="handlePreviewFile(scope.row)">文件预览</el-button>
        </template>
      </el-table-column>
    </el-table>

    <div class="sa-footer sa-row-center">
      <sa-pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </div>

    <XmlCheckResultOpen ref="xmlCheckResultRef" :xml-check-result-data="xmlCheckResultData" />

    <CodePreviewDialog ref="codePreviewDialogRef" />
  </div>
</template>

<script>
import request from '@/utils/request'
import XmlCheckResultOpen from '@/views/risk/xmlCheckResult/open.vue'
import CodePreviewDialog from '@/components/CodePreviewDialog/index.vue'

export default {
  components: {
    XmlCheckResultOpen,
    CodePreviewDialog
  },
  props: {
    sourceType: {
      type: String,
      default: 'index'
    },
    search: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 基线漏洞表格数据
      list: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        systemName: null,
        ip: null,
        fileName: null,
        xmlFilePath: null
      },

      riskTaskFileInfoList: [],

      xmlCheckResultData: {
        search: {}
      }
    }
  },
  created() {
    this.getList()
    this.getRiskTaskFileInfoList()
  },

  methods: {
    async handlePreviewFile(row) {
      const res = await request({
        url: `/risk/baseLineXmlFileInfo/${row.id}`,
        method: 'get'
      })

      const content = res.data.fileContext

      this.$refs.codePreviewDialogRef.open({ language: 'xml', content })
    },
    async handleExport() {
      const params = {
        ...this.queryParams,
        taskId: this.search.taskId
      }

      this.download(
        '/risk/baseLineXmlFileInfo/export',
        params,
        `基线文件列表_${new Date().getTime()}.xlsx`
      )
    },
    getList() {
      this.loading = true
      this.queryParams = {
        ...this.queryParams,
        ...this.$props.search
      }
      request({
        url: '/risk/baseLineXmlFileInfo/list',
        method: 'get',
        params: this.queryParams
      }).then((response) => {
        this.list = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    getRiskTaskFileInfoList() {
      request({
        url: `/risk/baseLineXmlFileInfo/getRiskTaskFileInfoList/${this.$props.search.taskId}`,
        method: 'get'
      }).then((response) => {
        this.riskTaskFileInfoList = response.data
      })
    },
    onOpenXmlCheckResult(row) {
      this.xmlCheckResultData.search = {
        taskId: this.$props.search.taskId,
        fileId: row.fileId,
        xmlFileInfoId: row.id
      }
      this.$refs.xmlCheckResultRef.show()
    },
    handleCommand(command, row) {
      this.queryParams.isFitTemplate = command
      this.getList()
    }
  }
}
</script>
