<template>
  <el-dialog
    v-if="visible"
    :title="
      monthlyEnterData.type == 'detail'
        ? '查看'
        : monthlyEnterData.type == 'preview' || monthlyEnterData.type == 'templateInfo'
          ? '预览'
          : '录入'
    "
    :visible.sync="visible"
    class="monthly-enter"
    width="800px"
    append-to-body
    @closed="onClosed"
  >
    <div v-if="['enter'].includes(monthlyEnterData.type)" class="absolute top-4 right-5 z-10">
      <el-button
        type="primary"
        plain
        icon="el-icon-refresh-right"
        :loading="loading"
        @click="handleRefresh"
      >刷新数据</el-button>
    </div>

    <div class="monthly-enter-content">
      <el-button
        v-if="monthlyEnterData.type == 'preview' && $checkPermi(['risk:week:preview:export'])"
        class="monthly-enter-upload"
        type="primary"
        icon="el-icon-download"
        @click="onDownload"
      >导出</el-button>

      <template v-if="renderLoop">
        <template v-for="(item, index) in list">
          <loop-item
            :key="index"
            :item-data="item"
            :level="1"
            :disabled-preset-table="['enter'].includes(monthlyEnterData.type)"
          />
        </template>
      </template>
    </div>
    <div
      v-if="
        !(
          monthlyEnterData.type == 'detail' ||
          monthlyEnterData.type == 'preview' ||
          monthlyEnterData.type == 'templateInfo'
        )
      "
      slot="footer"
      class="dialog-footer"
    >
      <el-button @click="visible = false">取 消</el-button>
      <el-button type="primary" @click="onConfirm">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { keyBy } from 'lodash-es'
import request from '@/utils/request'
import LoopItem from './loop-item.vue'

export default { // type: enter=周报管理录入|view=周报管理录入里面列表上的录入|detail=周报管理录入里面列表上的查看|preview=周报管理预览
  components: {
    LoopItem
  },
  props: ['monthlyEnterData'],
  data() {
    return {
      loading: false,
      // 遮罩层
      visible: false,

      list: [],

      renderLoop: true
    }
  },
  methods: {
    onClosed() {
      this.loading = false
      this.list = []
    },
    async reRenderLoop() {
      this.renderLoop = false
      await this.$nextTick()
      this.renderLoop = true
    },
    async handleRefresh() {
      this.loading = true

      const res = await request({
        url: `/risk/monthlyEnter/refreshEnterTreeList`,
        method: 'get',
        params: {
          ...this.$props.monthlyEnterData.form
        }
      })

      if (res.code == 200) {
        this.list = this.mergeMonthly(res.data, this.list)

        this.list.forEach((element, index) => {
          this.onLoopItem(null, element, index)
        })
      }

      this.loading = false
    },
    mergeMonthly(data, oldData) {
      const oldMap = keyBy(oldData, 'id')

      const value = data.reduce((arr, item) => {
        const oldItem = oldMap[item.id]

        const templateTableDataVoList = []

        if (item?.templateTableDataVoList?.length) {
          templateTableDataVoList.push(...(item.templateTableDataVoList || []))
        } else if (oldItem?.tableDataList?.length) {
          templateTableDataVoList.push(...oldItem.tableDataList)
        } else if (oldItem?.templateTableDataVoList) {
          templateTableDataVoList.push(...(oldItem.templateTableDataVoList || []))
        }

        const enterInfoVo = oldItem?.enterInfoVo?.content
          ? oldItem.enterInfoVo
          : item.enterInfoVo
        const content = oldItem?.content || item.content || oldItem?.enterInfoVo?.content

        arr.push({
          ...(oldItem || item),
          templateTableDataVoList,
          enterInfoVo: {
            ...enterInfoVo,
            content
          },
          content,
          children: this.mergeMonthly(item.children || [], oldItem?.children || [])
        })

        return arr
      }, [])

      return value
    },
    show() {
      this.getEnterTreeList()
    },
    async getEnterTreeList({ handleType } = {}) {
      const loading = this.$loading({
        text: '努力加载中，请稍后...',
        spinner: 'el-icon-loading'
      })

      if (
        this.$props.monthlyEnterData.type == 'view' ||
          this.$props.monthlyEnterData.type == 'detail'
      ) {
        this.list = [{ ...this.$props.monthlyEnterData.data, idx: 1 }]

        this.visible = true
      } else if (this.$props.monthlyEnterData.type == 'templateInfo') {
        await request({
          url: `/risk/templateInfo/getMonthlyTempTreeList`,
          method: 'get',
          params: this.$props.monthlyEnterData.form
        })
          .then((response) => {
            if (response.code == 200) {
              this.list = response.data

              this.list.forEach((element, index) => {
                this.onLoopItem(null, element, index)
              })

              this.visible = true
            }
          })
          .catch((e) => console.warn(e))
      } else {
        await request({
          url: `/risk/monthlyEnter/getEnterTreeList`,
          method: 'get',
          params: this.$props.monthlyEnterData.form
        })
          .then((response) => {
            if (response.code == 200) {
              this.list = response.data

              this.list.forEach((element, index) => {
                this.onLoopItem(null, element, index)
              })

              this.visible = true
            }
          })
          .catch((e) => console.warn(e))
      }

      loading.close()
    },
    onLoopItem(parent = null, item, index) {
      item.idx = parent ? parent.idx + '.' + (index + 1) : index + 1
      if (
        this.$props.monthlyEnterData.type == 'preview' ||
          this.$props.monthlyEnterData.type == 'templateInfo'
      ) {
        item.isAuthEnter = false
      }
      if (item.children && item.children.length > 0) {
        item.children.forEach((i, j) => {
          this.onLoopItem(item, i, j)
        })
      }
    },
    onLoopData(item) {
      for (var key in item) {
        if (key == 'id') {
          item.titleId = item.id
          delete item[key]
        } else if (
          key == 'content' ||
            key == 'tableDataList' ||
            key == 'children' ||
            key == 'directoryType' ||
            key == 'type'
        ) {
        } else {
          delete item[key]
        }
      }

      if (item.children && item.children.length > 0) {
        for (const item of item.children) {
          this.onLoopData(item)
        }
      }
    },
    onConfirm() {
      const submitForm = {
        ...this.$props.monthlyEnterData.form,
        titleList: JSON.parse(JSON.stringify(this.list))
      }
      for (var item of submitForm.titleList) {
        this.onLoopData(item)
      }

      if (['view'].includes(this.monthlyEnterData.type)) {
        Object.assign(submitForm, {
          isAllDelete: '1'
        })
      }

      request({
        url: `/risk/monthlyEnter/monthlyEnterSave`,
        method: 'post',
        data: submitForm
      }).then((response) => {
        if (response.code == 200) {
          this.visible = false
          this.$emit('confirm')
        }
      })
    },
    onDownload() {
      request({
        url: `/risk/monthlyEnter/exportEnterMonthlyDataOutlineWord`,
        method: 'get',
        params: this.$props.monthlyEnterData.form
      }).then((response) => {
        if (response.code == 200) {
          this.download(
            response.data.url,
            {},
            response.data.fileName
          )
        }
      })
    }
  }
}
</script>

<style lang="postcss" scoped>
  ::v-deep.monthly-enter {
    .el-dialog__body {
      @apply relative;
    }
    .monthly-enter-content {
      position: relative;
    }
    .monthly-enter-upload {
      position: absolute;
      top: 0;
      right: 0;
    }
  }
</style>
