<template>
  <div class="page-main">
    <el-form
      v-show="showSearch"
      ref="queryForm"
      class="sa-query"
      :model="queryParams"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="模板名称" prop="templateName">
        <el-input
          v-model="queryParams.templateName"
          placeholder="请输入模板名称"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <!-- <el-form-item label="周报模板文件" prop="templateFileUrl">
        <el-input
          v-model="queryParams.templateFileUrl"
          placeholder="请输入周报模板文件"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态">
          <el-option
            v-for="(value, key) in statusObj"
            :key="key"
            :label="value"
            :value="key"
          />
        </el-select>
      </el-form-item>
      <el-form-item class="daterange" label="创建时间" prop="daterange">
        <el-date-picker
          v-model="daterange"
          type="daterange"
          value-format="yyyy-MM-dd"
          format="yyyy-MM-dd"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="onChangeDaterange"
        />
      </el-form-item>
      <el-form-item class="query-handle">
        <el-button type="primary" plain icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh-left" @click="resetQuery">重置</el-button>
        <EleSheetSearchMore :parent-ref="()=> $refs.queryForm" />
      </el-form-item>
    </el-form>

    <div class="sa-title">
      <sa-toolbar :show-search.sync="showSearch" @queryTable="getList" />
      <el-button
        v-hasPermi="['risk:week:template:add']"
        type="primary"
        icon="el-icon-plus"
        @click="handleAdd"
      >新增</el-button>
    </div>

    <el-table
      v-loading="loading"
      class="sa-table"
      :data="list"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column
        v-for="item in templateInfoColumn"
        :key="item.field"
        :label="item.label"
        :min-width="item.width || 120"
        align="center"
      >
        <template slot-scope="scope">
          <template v-if="item.field == 'status'">
            <el-switch
              v-model="scope.row.status"
              active-value="1"
              inactive-value="0"
              :disabled="!$checkPermi(['risk:week:template:updateStatus'])"
              @change="onEditStatus(scope.row)"
            />
          </template>
          <template v-else>
            {{ scope.row[item.field] || '-' }}
          </template>
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" align="center" width="320">
        <template slot-scope="scope">
          <el-button
            v-hasPermi="['risk:week:template:edit']"
            size="mini"
            type="text"
            @click="handleUpdate(scope.row)"
          >编辑</el-button>
          <el-button
            v-hasPermi="['risk:week:template:preview']"
            size="mini"
            type="text"
            @click="onMonthlyEnterPreview(scope.row)"
          >预览</el-button>
          <el-button v-hasPermi="['risk:week:template:user:assign']" size="mini" type="text" @click="onUserAssignClick(scope.row)">分配人员</el-button>
          <!-- <el-button size="mini" type="text" @click="handleImport(2)">导入模板文件</el-button> -->
          <el-button
            v-hasPermi="['risk:week:template:config']"
            size="mini"
            type="text"
            @click="onTemplateOutline(scope.row)"
          >配置模板</el-button>
          <el-button
            v-hasPermi="['risk:week:template:remove']"
            size="mini"
            type="text"
            @click="handleDelete(scope.row)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <div class="sa-footer sa-row-center">
      <sa-pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </div>

    <!-- 添加或编辑对话框 -->
    <el-dialog v-if="open" :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="模板名称" prop="templateName">
          <el-input v-model="form.templateName" placeholder="请输入模板名称" />
        </el-form-item>
        <!-- <el-form-item label="周报模板文件" prop="templateFileUrl">
          <el-input v-model="form.templateFileUrl" placeholder="请输入周报模板文件" />
        </el-form-item> -->
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status" placeholder="请选择状态">
            <el-radio v-for="(value, key) in statusObj" :key="key" :label="key">
              {{ value }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" placeholder="请输入备注" />
        </el-form-item>
        <!-- <el-form-item label="创建人" prop="createBy">
          <el-input v-model="form.createBy" placeholder="请输入创建人" />
        </el-form-item>
        <el-form-item label="创建时间" prop="createTime">
          <el-date-picker
            clearable
            size="small"
            v-model="form.createTime"
            type="datetime"
            value-format="yyyy-MM-dd HH:mm:ss"
            placeholder="选择创建时间"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="更新人" prop="updateBy">
          <el-input v-model="form.updateBy" placeholder="请输入更新人" />
        </el-form-item>
        <el-form-item label="更新时间" prop="updateTime">
          <el-date-picker
            clearable
            size="small"
            v-model="form.updateTime"
            type="datetime"
            value-format="yyyy-MM-dd HH:mm:ss"
            placeholder="选择更新时间"
          >
          </el-date-picker>
        </el-form-item> -->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
        <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
      </div>
    </el-dialog>
    <!-- 导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload" />
        <div class="el-upload__text">
          将文件拖到此处，或
          <em>点击上传</em>
        </div>
        <template v-if="upload.type == 1">
          <div slot="tip" class="el-upload__tip">
            <el-link
              type="info"
              style="font-size: 14px; color: green"
              @click="importTemplate"
            >点击下载模板</el-link>
          </div>
          <div
            slot="tip"
            class="el-upload__tip"
            style="color: red"
          >提示：仅允许导入“xls”或“xlsx”格式文件！</div>
        </template>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button @click="upload.open = false">取 消</el-button>
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
      </div>
    </el-dialog>

    <MonthlyEnter ref="monthlyEnterRef" :monthly-enter-data="monthlyEnterData" @confirm="getList" />

    <UserAssignDialog ref="userAssignDialogRef" />
  </div>
</template>

<script>
import {
  addInfo,
  delInfo,
  exportInfo,
  getInfo,
  listInfo,
  updateInfo
} from '@/api/risk/monthly/templateInfo'
import { getToken } from '@/utils/auth'
import { templateInfoColumn } from '../data'
import MonthlyEnter from '../monthlyEnter/index.vue'
import UserAssignDialog from './components/UserAssignDialog/index.vue'

export default {
  name: 'TemplateInfo',
  components: {
    MonthlyEnter,
    UserAssignDialog
  },
  data() {
    return {
      templateInfoColumn,
      // 按钮loading
      buttonLoading: false,
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 周报模板信息表格数据
      list: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 导入参数
      upload: {
        // 是否显示弹出层
        open: false,
        // 弹出层标题
        title: '',
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: { Authorization: 'Bearer ' + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + '/risk/templateInfo/import'
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        templateName: undefined,
        // templateFileUrl: undefined,
        status: undefined
      },
      daterange: [],
      // 表单参数
      form: {},
      // 表单校验
      rules: {},

      statusObj: {
        0: '停用',
        1: '在用'
      },

      monthlyEnterData: {
        open: false,
        type: '',
        form: {}
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    /** 查询周报模板信息列表 */
    getList() {
      this.loading = true
      this.onChangeDaterange()
      listInfo(this.queryParams).then((response) => {
        this.list = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        id: undefined,
        templateName: undefined,
        // templateFileUrl: undefined,
        status: '0',
        remark: undefined
        // createBy: undefined,
        // createTime: undefined,
        // updateBy: undefined,
        // updateTime: undefined,
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.daterange = []
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = '新增'
    },
    /** 编辑按钮操作 */
    handleUpdate(row) {
      this.loading = true
      this.reset()
      const id = row.id || this.ids
      getInfo(id).then((response) => {
        this.loading = false
        this.form = response.data
        this.open = true
        this.title = '编辑'
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          this.buttonLoading = true
          if (this.form.id != null) {
            updateInfo(this.form)
              .then((response) => {
                this.$modal.msgSuccess('编辑成功')
                this.open = false
                this.getList()
              })
              .finally(() => {
                this.buttonLoading = false
              })
          } else {
            addInfo(this.form)
              .then((response) => {
                this.$modal.msgSuccess('新增成功')
                this.open = false
                this.getList()
              })
              .finally(() => {
                this.buttonLoading = false
              })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids
      this.$confirm('是否确认删除当前所选数据项？', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.loading = true
          return delInfo(ids)
        })
        .then(() => {
          this.loading = false
          this.getList()
          this.$modal.msgSuccess('删除成功')
        })
        .catch(() => {})
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams
      this.$confirm('是否确认导出所有周报模板信息数据项?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.exportLoading = true
          return exportInfo(queryParams)
        })
        .then((response) => {
          this.download(response.msg)
          this.exportLoading = false
        })
        .catch(() => {})
    },
    /** 导入按钮操作 */
    handleImport(type = 1) {
      this.upload.type = type
      this.upload.title = '周报模板信息导入'
      if (type == 2) {
        this.upload.title = '导入模板文件'
        // this.upload.url = '';
      }
      this.upload.open = true
    },
    /** 下载模板操作 */
    importTemplate() {
      importTemplate().then((response) => {
        this.download(response.msg)
      })
    },
    /** 文件上传中处理*/
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true
    },
    /** 文件上传成功处理*/
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false
      this.upload.isUploading = false
      this.$refs.upload.clearFiles()
      this.$alert(response.msg, '导入结果', { dangerouslyUseHTMLString: true })
      this.getList()
    },
    /** 提交上传文件*/
    submitFileForm() {
      this.$refs.upload.submit()
    },
    onChangeDaterange() {
      if (this.daterange && this.daterange.length > 0) {
        this.queryParams.startTime = this.daterange[0]
        this.queryParams.endTime = this.daterange[1]
      } else {
        delete this.queryParams.startTime
        delete this.queryParams.endTime
      }
    },
    onEditStatus(row) {
      updateInfo(row).then((response) => {
        this.$modal.msgSuccess('编辑成功')
        this.getList()
      })
    },
    onTemplateOutline(row) {
      this.$router.push({
        path: '/pso/templateOutline',
        query: {
          templateId: row.id
        }
      })
    },
    onMonthlyEnterPreview(row) {
      this.monthlyEnterData.type = 'templateInfo'
      this.monthlyEnterData.form = {
        templateId: row.id
      }
      this.$refs.monthlyEnterRef.show()
    },
    onUserAssignClick(row) {
      this.$refs.userAssignDialogRef.open({
        params: {
          id: row.id
        }
      })
    }
  }
}
</script>
