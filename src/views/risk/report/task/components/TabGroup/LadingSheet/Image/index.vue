<template>
  <div class="page-main page-main--flat">
    <el-form
      v-show="showSearch"
      ref="queryForm"
      class="sa-query"
      :model="queryParams"
      size="small"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="集群名称" prop="clusterName">
        <el-input
          v-model="queryParams.clusterName"
          placeholder="请输入集群名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="登录方式" prop="loginMode">
        <el-input
          v-model="queryParams.loginMode"
          placeholder="请输入登录方式"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="镜像名称" prop="imageName">
        <el-input
          v-model="queryParams.imageName"
          placeholder="请输入镜像名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="镜像版本" prop="imageVersion">
        <el-input
          v-model="queryParams.imageVersion"
          placeholder="请输入镜像版本"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="项目" prop="item">
        <el-input
          v-model="queryParams.item"
          placeholder="请输入项目"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="仓库" prop="stash">
        <el-input
          v-model="queryParams.stash"
          placeholder="请输入仓库"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="harbor库地址" prop="harborLibraryAddress">
        <el-input
          v-model="queryParams.harborLibraryAddress"
          placeholder="请输入harbor库地址"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="端口" prop="port">
        <el-input
          v-model="queryParams.port"
          placeholder="请输入端口"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="账号" prop="userName">
        <el-input
          v-model="queryParams.userName"
          placeholder="请输入账号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="密码" prop="password">
        <el-input
          v-model="queryParams.password"
          placeholder="请输入密码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item class="query-handle">
        <el-button type="primary" plain icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh-left" @click="resetQuery">重置</el-button>
        <EleSheetSearchMore :parent-ref="() => $refs.queryForm" />
      </el-form-item>
    </el-form>
    <el-table ref="multipleTableRef" v-loading="loading" class="sa-table" :data="list">
      <el-table-column label="容器平台" prop="containerPlatform" width="150" align="left" show-overflow-tooltip />
      <el-table-column label="集群名称" align="center" prop="clusterName" width="150" show-overflow-tooltip />
      <el-table-column label="登录方式" align="center" prop="loginMode" show-overflow-tooltip />
      <el-table-column label="镜像名称" align="center" prop="imageName" width="150" show-overflow-tooltip />
      <el-table-column label="镜像版本" align="center" prop="imageVersion" show-overflow-tooltip />
      <el-table-column label="项目" align="center" prop="item" show-overflow-tooltip />
      <el-table-column label="仓库" align="center" prop="stash" show-overflow-tooltip />
      <el-table-column label="harbor库地址" align="center" prop="harborLibraryAddress" width="150" show-overflow-tooltip />
      <el-table-column label="端口" align="center" prop="port" show-overflow-tooltip />
      <el-table-column label="账号" align="center" prop="userName" show-overflow-tooltip />
      <el-table-column label="密码" align="center" prop="password" show-overflow-tooltip />

      <el-table-column v-slot="{ row }" label="SCA扫描状态" align="center" width="150">
        <EleTagDict :value="row.scanStatus" :options="dict.type.sca_scan_status" />
      </el-table-column>

      <el-table-column v-slot="{ row }" label="小佑扫描状态" align="center" width="150">
        <EleTagDict :value="row.containerScanStatus" :options="dict.type.container_scan_status" />
      </el-table-column>

      <el-table-column v-slot="{ row }" label="亚信智网扫描状态" align="center" width="150">
        <EleTagDict :value="row.yxzwScanStatus" :options="dict.type.yxzw_scan_status" />
      </el-table-column>

      <el-table-column v-slot="{ row }" label="青藤云扫描状态" align="center" width="150">
        <EleTagDict :value="row.qtyScanStatus" :options="dict.type.qty_scan_status" />
      </el-table-column>

      <el-table-column v-slot="{ row }" label="探真扫描状态" align="center" width="150">
        <EleTagDict :value="row.tzScanStatus" :options="dict.type.tz_scan_status" />
      </el-table-column>

      <el-table-column label="备注" align="center" prop="remarks" show-overflow-tooltip />

      <el-table-column fixed="right" label="操作" align="center" width="240">
        <template #default="{ row,$index }">
          <el-dropdown v-if="$checkPermi(['risk:report:task:image:downloadReport'])" :key="row.id + $index" v-check-dropdown-items>
            <el-button class="!mr-2" type="text" size="mini">
              下载扫描报告<i class="el-icon-arrow-down el-icon--right"></i>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item v-if="row.scaDownload" @click.native="onDownloadClick(row.scaDownload)">SCA扫描报告</el-dropdown-item>
                <el-dropdown-item v-if="row.containerDownload" @click.native="onDownloadClick(row.containerDownload)">小佑扫描报告</el-dropdown-item>
                <el-dropdown-item v-if="['2'].includes(row.yxzwScanStatus)" @click.native="onScanExportClick(row, '3')">亚信智网扫描报告</el-dropdown-item>
                <el-dropdown-item v-if="['SUCCESS'].includes(row.qtyScanStatus)" @click.native="onScanExportClick(row, '4')">青藤云扫描报告</el-dropdown-item>
                <el-dropdown-item v-if="['finished'].includes(row.tzScanStatus)" @click.native="onScanExportClick(row, '6')">探真扫描报告</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>

          <!-- <el-button
            size="mini"
            type="text"
            @click="onBasicImage(scope.row)"
          >查看漏洞详情</el-button> -->
        </template>
      </el-table-column>
    </el-table>

    <div class="sa-footer sa-row-center">
      <sa-pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </div>

    <basic-image-index ref="basicImageIndexRef" :basic-image-index-data="basicImageIndexData" />
  </div>
</template>

<script>
import request from '@/utils/request'
import BasicImageIndex from '@/views/risk/riskBasicImageLadingData/BasicImageDetail.vue'

export default {
  dicts: ['sca_scan_status', 'container_scan_status', 'yxzw_scan_status', 'qty_scan_status', 'tz_scan_status'],
  components: {
    BasicImageIndex
  },
  props: {
    basicImageData: {
      type: Object,
      default: () => ({
        search: {}
      })
    },
    params: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      showSearch: true,
      // 总条数
      total: 0,
      // 镜像工单漏洞信息表格数据
      list: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        taskId: undefined,
        clusterName: undefined,
        loginMode: undefined,
        imageName: undefined,
        imageVersion: undefined,
        item: undefined,
        stash: undefined,
        harborLibraryAddress: undefined,
        port: undefined,
        userName: undefined,
        password: undefined
      },

      visible: false,

      basicImageIndexData: {
        show: {
          taskName: true
        },
        search: {}
      }
    }
  },
  created() {
    this.visible = true
    this.getList()
  },
  methods: {
    /** 查询镜像工单漏洞信息列表 */
    getList() {
      this.loading = true
      this.queryParams = {
        ...this.queryParams,
        ...this.$props.basicImageData.search,
        ...this.$props.params
      }
      request({
        url: '/risk/riskBasicImageLadingData/list',
        method: 'get',
        params: this.queryParams
      }).then((response) => {
        this.list = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    onBasicImage(row) {
      this.basicImageIndexData.search = {
        imageId: row.id,
        queryType: '0'
      }
      this.$refs.basicImageIndexRef.show()
    },
    onDownloadClick(url) {
      this.download(url)
    },
    onScanExportClick(row, scanType) {
      const scanTypeText = {
        3: '亚信智网',
        4: '青藤云',
        6: '探真'
      }[scanType]

      this.download('/risk/riskBasicImageLadingData/exportReport', { id: row.id, scanType }, `镜像扫描报告（${scanTypeText}）_${Date.now()}.pdf`)
    }
  }
}
</script>
