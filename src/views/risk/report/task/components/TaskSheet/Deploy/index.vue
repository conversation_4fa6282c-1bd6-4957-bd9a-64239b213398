<template>
  <EleSheet ref="sheetRef" v-bind="sheetProps" class="page-main page-main--full" @add-success="onAddSuccess" @edit-success="onEditSuccess">
    <template #before="{ searchMixin: { lazyModel, parameter } }">
      <TaskStats :key="[taskType, JSON.stringify(lazyModel)].join(',')" v-bind="{ queryParams: { ...parameter(), taskType } }" />
    </template>

    <template #toolbar:after="{ singleSelected, selectionIds, selection, removeHandler }">
      <el-button
        v-hasPermi="['risk:report:task:del']"
        type="danger"
        icon="el-icon-delete"
        :disabled="!singleSelected"
        @click="removeHandler(selectionIds)"
      >删除
      </el-button>

      <el-button
        v-hasPermi="['risk:report:task:invalidate']"
        type="warning"
        icon="el-icon-document-delete"
        :disabled="!singleSelected"
        @click="onVoidClick(selectionIds, selection)"
      >作废
      </el-button>
    </template>

    <template #table:taskName:simple="{ row }">
      <span class="text-primary-500 hover:underline cursor-pointer" @click="handleEdit(row)">
        {{ row.taskName }}
      </span>
    </template>

    <template #info:deployFiles:simple="{ data }">
      <span class="text-primary-500 hover:underline cursor-pointer" @click="handleDownload(data)">
        {{ data.uploadFileName }}
      </span>
    </template>

    <template #table:after="{ infoHandler }">
      <el-table-column v-slot="{ row }" label="操作" fixed="right" align="center" width="150">
        <el-button type="text" size="mini" @click="infoHandler(row)">查看</el-button>

        <el-button v-if="$checkPermi(['deploy:audit']) && ['10'].includes(row.taskStatus) && checkRolePersonType(['2'])" type="text" size="mini" @click="handleAudit(row)">审核</el-button>

        <el-button
          v-if="
            $checkPermi(['deploy:resubmit']) &&
              ['2'].includes(String(row.auditIdentification)) &&
              ['12'].includes(String(row.taskStatus)) &&
              checkRolePersonType(['2', '3'],{ reversed: true })
          "
          type="text"
          size="mini"
          @click="handleAuditRepeat(row)"
        >重新提交</el-button>

        <el-button v-if="$checkPermi(['deploy:prove']) && ['11'].includes(row.taskStatus) && checkRolePersonType(['3'])" type="text" size="mini" @click="handlePublish(row)">部署</el-button>

        <el-button v-if="$checkPermi(['risk:report:confirmation:statement']) && ['6'].includes(row.taskStatus)" type="text" size="mini" @click="handleStatement(row)">确认结单</el-button>
      </el-table-column>
    </template>

    <template #edit:after="{ model }">
      <TabGroup
        :key="model.id"
        v-bind="{
          hiddenUpload: true,
          fileTabName: '已上传文件列表',
          formInfo: model,
          excludeColumns: ['status']
        }"
      />
    </template>

    <template #after>
      <AuditDialog ref="auditDialogRef" v-bind="{ sheetProps }" />
      <AuditRepeatDialog ref="auditRepeatDialogRef" />
      <PublishDialog ref="publishDialogRef" @success="handleRefresh" />
    </template>
  </EleSheet>
</template>

<script>
import request from '@/utils/request.js'

import { listTask, getTask, addTask, updateTask, delTask } from '@/api/risk/report/task.js'

import AuditDialog from './AuditDialog/index.vue'
import AuditRepeatDialog from './AuditRepeatDialog/index.vue'
import PublishDialog from './PublishDialog/index.vue'
import TaskStats from '@/views/risk/report/task/components/TaskStats/index.vue'

import { dataMixin } from '@/mixins'

import TabGroup from '@/views/risk/report/task/components/TabGroup/index.vue'

import { disabledDatePast } from '@/utils/disabledDate.js'

export default {
  dicts: ['main_task_type', 'task_status_15', 'selfAudit_report_type', 'deploy_tool_type', 'resource_pool_type', 'res_res_pool_data', 'work_order_type'],
  components: {
    TaskStats,
    AuditDialog,
    AuditRepeatDialog,
    PublishDialog,
    TabGroup
  },
  mixins: [
    dataMixin({
      projectManagerList: {
        default: [],
        async load() {
          const res = await request({
            url: `/risk/report/task/getProjectManagerList`,
            method: 'get'
          })

          const value = res.data.map(item => ({
            ...item,
            label: `${item.primaryAccountNumber}-${item.nickName}`,
            value: String(item.userId)
          }))

          return value
        }
      },
      secureGroupApproveList: {
        default: [],
        async load() {
          const res = await request({
            url: `/risk/report/task/getProjectManagerList`,
            method: 'get',
            params: {
              personType: 2
            }
          })

          const value = res.data.map(item => ({
            ...item,
            label: `${item.primaryAccountNumber}-${item.nickName}`,
            value: String(item.userId)
          }))

          return value
        }
      }
    })
  ],

  props: {
    taskType: {
      type: [String, Number],
      default: ''
    }
  },
  data() {
    return {
      activeDeployType: '1',
      activeDeptName: ''
    }
  },
  computed: {
    sheetProps() {
      const value = {
        title: '安全产品部署任务',

        lazy: false,

        api: {
          add: async(params) => {
            const withParams = {
              ...params,
              taskType: this.taskType
            }

            delete withParams.deployTime
            delete withParams.deployFiles

            const nextId = await this.generateTaskId()

            Object.assign(withParams, { nextId })

            return addTask(withParams)
          },
          edit: (params) => updateTask({ ...params }),
          list: async(params) => listTask({ ...params, taskType: this.taskType }),
          info: getTask,
          remove: delTask,
          export: (handler) => handler('/risk/report/task/export', { parameter: (params) => ({ ...params, taskType: this.taskType }) }),
          import: '',
          template: ''
        },

        infoProps: {
          title: true
        },

        tableProps: {
          height: '100%'
        },

        editProps: {
          disabled: !this.$checkPermi(['risk:report:task:edit'])
        },

        hiddenActions: {
          add: !this.$checkPermi(['risk:report:task:add']),
          edit: true,
          remove: !this.$checkPermi(['risk:report:task:edl']),
          export: !this.$checkPermi(['risk:report:task:export'])
        },

        model: {
          taskType: {
            label: '任务类型',
            type: 'select',
            width: 150,
            options: this.dict.type.main_task_type.filter((item) => this.taskType == item.value),
            value: this.dict.type.main_task_type.find((item) => this.taskType == item.value)
              ?.value,
            form: {
              rules: true,
              fieldProps: {
                disabled: true
              }
            },
            search: {
              hidden: true
            },
            table: {
              hidden: true
            }
          },
          taskName: {
            label: '任务名称',
            type: 'text',
            form: {
              hidden: true
            },
            table: {
              align: 'left'
            },
            width: 150
          },
          taskStatus: {
            type: 'select',
            label: '任务状态',
            width: 150,
            form: {
              hidden: true
            },
            options: this.dict.type.task_status_15
          },
          deptName: {
            label: '所属部门',
            width: 120,
            search: {
              fieldProps: {
                returnName: true
              },
              type: 'CommonDepartmentSelect'
            },
            form: {
              fieldProps: {
                returnName: true,
                on: {
                  change: (value, ctx) => {
                    this.activeDeptName = value
                    ctx.model.systemName = void 0
                  }
                }
              },
              rules: true,
              type: 'CommonDepartmentSelect'
            }
          },
          systemName: {
            label: '所属项目',
            search: {
              type: 'CommonSystemSelect',
              fieldProps: {
                returnName: true
              }
            },
            form: {
              rules: true,
              type: 'CommonSystemSelect',
              fieldProps: {
                returnName: true,
                params: {
                  deptName: this.activeDeptName
                }
              }
            }
          },
          workOrderNumber: {
            label: '工单编号',
            form: {
              sort: 35,
              fieldProps: {
                placeholder: '系统自动生成',
                disabled: true
              }
            }
          },
          workOrderType: {
            label: '交付/运维工单类型',
            type: 'select',
            options: this.dict.type.work_order_type,
            rules: true
          },
          createByName: {
            label: '创建人',
            type: 'text',
            width: 150,
            form: {
              hidden: true,
              rules: true
            }
          },
          createTime: {
            type: 'text',
            label: '创建时间',
            search: {
              type: 'date-range',
              parameter: (data) => {
                return {
                  startTime: data?.[0],
                  endTime: data?.[1]
                }
              }
            },
            form: {
              hidden: true
            },
            width: 200
          },

          deployToolType: {
            label: '工具类型',
            type: 'select',
            options: this.dict.type.deploy_tool_type,
            search: {
              hidden: true
            },
            table: {
              hidden: true
            },
            form: {
              rules: true
            }
          },
          resourcePoolType: {
            label: '资源池类型',
            type: 'select',
            options: this.dict.type.resource_pool_type,
            search: {
              hidden: true
            },
            table: {
              hidden: true
            },
            form: {
              rules: true
            }
          },
          scanPoolNames: {
            label: '资源池',
            type: 'select',
            options: this.dict.type.res_res_pool_data,
            search: {
              hidden: true
            },
            table: {
              hidden: true
            },
            form: {
              rules: true
            }
          },
          upperCloudWorkOrderNumber: {
            label: '上云工单号',
            search: {
              hidden: true
            },
            form: {
              rules: true
            },
            table: {
              hidden: true
            }
          },
          deployType: {
            type: 'select',
            label: '部署类型',
            search: {
              hidden: true
            },
            table: {
              hidden: true
            },
            form: {
              rules: true,
              type: 'radio',
              value: '1',
              fieldProps: {
                on: {
                  change: (value) => {
                    this.activeDeployType = value
                  }
                }
              }
            },
            options: [
              {
                label: '新增',
                value: '1'
              },
              {
                label: '扩容',
                value: '2'
              }
            ]
          },
          expansionNumber: {
            label: '扩容主机数量',
            search: {
              hidden: true
            },
            table: {
              hidden: true
            },
            form: {
              type: 'input-number',
              hidden: !['2'].includes(this.activeDeployType),
              fieldProps: {
                min: 1
              },
              rules: true
            }
          },
          deployTime: {
            type: 'text',
            label: '可部署开始/结束时间',
            search: {
              hidden: true
            },
            info: {
              hidden: true
            },
            form: {
              type: 'date-time-range',
              fieldProps: {
                pickerOptions: { disabledDate: disabledDatePast }
              },
              parameter: (data) => {
                return {
                  approveDeployStartTime: data?.[0],
                  deployEndTime: data?.[1]
                }
              }

            },
            edit: {
              formatter: (row) => {
                return row.deployEndTime ? [row.approveDeployStartTime, row.deployEndTime] : []
              }
            },
            table: {
              hidden: true
            }
          },
          approveDeployStartTime: {
            hidden: true,
            label: '可部署开始时间',
            info: {
              hidden: false
            }
          },
          deployEndTime: {
            hidden: true,
            label: '可部署结束时间',
            info: {
              hidden: false
            }
          },
          projectManagerId: {
            label: '局方项目经理',
            type: 'select',
            options: this.dataMixin.projectManagerList,
            search: {
              hidden: true
            },
            table: {
              hidden: true
            },
            form: {
              rules: true
            }
          },
          secureGroupApproveId: {
            label: '安全组长审批',
            type: 'select',
            options: this.dataMixin.secureGroupApproveList,
            search: {
              hidden: true
            },
            table: {
              hidden: true
            },
            form: {
              rules: true
            }
          },
          deployInformation: {
            label: '部署信息',
            search: {
              hidden: true
            },
            table: {
              hidden: true
            },
            form: {
              type: 'textarea'
            }
          },
          deployFiles: {
            label: '部署信息附件',
            search: {
              hidden: true
            },
            add: {
              rules: true
            },
            info: {
              hidden: true
            },
            form: {
              type: 'upload',
              parameter: (data) => ({ uploadFileUrl: data[0]?.url, uploadFileName: data[0]?.name }),
              formatter: (row) => {
                return row.uploadFileUrl ? [{ url: row.uploadFileUrl, name: row.uploadFileName }] : []
              }
            },
            table: {
              hidden: true
            }
          },
          deployProveInfo: {
            label: '部署证明信息',
            search: {
              hidden: true
            },
            form: {
              hidden: true
            },
            table: {
              hidden: true
            }
          },
          deployPerson: {
            label: '部署人员',
            search: {
              hidden: true
            },
            form: {
              hidden: true
            },
            table: {
              hidden: true
            }
          },
          planDeployStartTime: {
            label: '计划部署开始时间',
            search: {
              hidden: true
            },
            form: {
              hidden: true
            },
            table: {
              hidden: true
            }
          },
          planDeployEndTime: {
            label: '计划部署结束时间',
            search: {
              hidden: true
            },
            form: {
              hidden: true
            },
            table: {
              hidden: true
            }
          },
          secureGroupApproveName: {
            label: '审核人',
            search: {
              hidden: true
            },
            form: {
              hidden: true
            },
            table: {
              hidden: true
            }
          },
          actualDeployStartTime: {
            label: '实际部署开始时间',
            search: {
              hidden: true
            },
            form: {
              hidden: true
            },
            table: {
              hidden: true
            }
          },
          actualDeployEndTime: {
            label: '实际部署结束时间',
            search: {
              hidden: true
            },
            form: {
              hidden: true
            },
            table: {
              hidden: true
            }
          }
        }
      }

      return value
    }
  },
  methods: {
    checkRolePersonType(values = [], { reversed = false } = {}) {
      const { personType, role } = this.$store.getters.userInfo

      if (['1', '2'].includes(String(role.roleType))) {
        return true
      }

      if (reversed) {
        if (!values.includes(String(personType))) {
          return true
        }
      } else {
        if (values.includes(String(personType))) {
          return true
        }
      }

      return false
    },
    onAddSuccess() {
      this.activeDeptName = ''
    },
    onEditSuccess() {
      this.activeDeptName = ''
    },
    async generateTaskId() {
      const res = await request({
        url: '/risk/report/task/getTaskNextId'
      })

      return res?.nextId
    },
    handleRefresh() {
      this.$refs.sheetRef.getTableData()
    },
    handleEdit(row) {
      this.$refs.sheetRef.handleEdit(row)
    },
    handleAudit(row) {
      this.$refs.auditDialogRef.open({
        params: {
          ...row,
          taskId: row.id,
          taskType: row.taskType
        },
        success: () => {
          this.handleRefresh()
        }
      })
    },
    handleAuditRepeat(row) {
      this.$refs.auditRepeatDialogRef.open({
        params: {
          taskId: row.id,
          taskType: row.taskType
        },
        success: () => {
          this.handleRefresh()
        }
      })
    },
    handlePublish(row) {
      this.$refs.publishDialogRef.open({
        params: {
          ...row,
          taskId: row.id,
          taskType: row.taskType
        },
        success: () => {
          this.handleRefresh()
        }
      })
    },
    async handleStatement(row) {
      try {
        await this.$confirm('是否确认已结单？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
      } catch (error) {
        return error
      }

      const res = await request({
        url: `/risk/report/task/confirmationStatement`,
        method: 'post',
        data: {
          id: row.id,
          taskType: row.taskType,
          taskStatus: 4
        }
      })

      if (res.code === 200) {
        this.$message.success(res.msg)
        this.handleRefresh()
      } else {
        this.$message.warning(res.msg)
      }
    },
    handleDownload(row) {
      if (!row.uploadFileUrl) return false

      window.open(row.uploadFileUrl)
    },

    async onVoidClick(ids, selection) {
      const disabledRow = selection.find(item => ['4'].includes(String(item.taskStatus)))

      if (disabledRow) {
        this.$message.warning(`任务名称：${disabledRow.taskName} 的任务类型不支持作废`)
        return false
      }

      try {
        await this.$confirm('是否确认作废？', '提示', {
          type: 'warning'
        })
      } catch (error) {
        return error
      }

      const res = await request({
        url: `/risk/report/task/invalidate`,
        method: 'post',
        params: {
          taskId: ids[0]
        }
      })

      if (res.code === 200) {
        this.$message.success(res.msg)
        this.handleRefresh()
      }
    }
  }
}
</script>

<style></style>
