<template>
  <EleSheet ref="sheetRef" v-bind="sheetProps" class="page-main page-main--full">
    <template #before="{ searchMixin: { lazyModel, parameter } }">
      <TaskStats :key="[taskType, JSON.stringify(lazyModel)].join(',')" v-bind="{ queryParams: { ...parameter(), taskType } }" />
    </template>

    <template #table:taskName:simple="{ row }">
      <span class="text-primary-500 hover:underline cursor-pointer" @click="handleInfo(row)">
        {{ row.taskName }}
      </span>
    </template>

    <template #table:after>
      <el-table-column v-slot="{ row }" label="操作" fixed="right" align="center" width="150">
        <el-button v-if="$checkPermi(['risk:selfAudit:attachment']) && ['1'].includes(row.reportType)" type="text" size="mini" @click="handleAttachment(row)">附件</el-button>
        <el-button v-if="$checkPermi(['risk:selfAudit:issue']) && !['1'].includes(row.reportType)" type="text" size="mini" @click="handleIssue(row)">下发</el-button>
        <el-button type="text" size="mini" @click="handleView(row)">详情</el-button>
      </el-table-column>
    </template>

    <template #after>
      <IssueDialog ref="issueDialogRef" />
      <ViewDialog ref="viewDialogRef" />
      <AttachmentDialog
        ref="attachmentDialogRef"
        v-bind="{
          excludeColumns: [
            'reportFileType',
            'status',
            'remarks',
            'updateTime'
          ]
        }"
      />
    </template>
  </EleSheet>
</template>

<script>
import request from '@/utils/request.js'

import { listTask, getTask, addTask, updateTask, delTask } from '@/api/risk/report/task.js'

import IssueDialog from './IssueDialog/index.vue'
import ViewDialog from './ViewDialog/index.vue'
import TaskStats from '@/views/risk/report/task/components/TaskStats/index.vue'

import AttachmentDialog from '@/views/risk/report/url/ImportDialog/index.vue'

export default {
  dicts: ['main_task_type', 'task_status_14', 'selfAudit_report_type'],
  components: {
    IssueDialog,
    ViewDialog,
    TaskStats,
    AttachmentDialog
  },
  props: {
    taskType: {
      type: [String, Number],
      default: ''
    }
  },
  data() {
    return {}
  },
  computed: {
    sheetProps() {
      const value = {
        title: '合作伙伴自审计',

        lazy: false,

        api: {
          add: (params) => addTask({ ...params }),
          edit: (params) => updateTask({ ...params }),
          list: async(params) => listTask({ ...params, taskType: this.taskType }),
          info: getTask,
          remove: delTask,
          export: (handler) => handler('/risk/report/task/export', { parameter: (params) => ({ ...params, taskType: this.taskType }) }),
          import: '',
          template: ''
        },

        editProps: {
          disabled: !this.$checkPermi(['risk:report:task:edit'])
        },

        hiddenActions: {
          add: !this.$checkPermi(['risk:report:task:add']),
          edit: true,
          remove: !this.$checkPermi(['risk:report:task:edl']),
          export: !this.$checkPermi(['risk:report:task:export'])
        },

        infoProps: {
          title: true
        },

        tableProps: {
          height: '100%'
        },

        model: {
          taskType: {
            label: '任务类型',
            type: 'select',
            width: 150,
            options: this.dict.type.main_task_type.filter((item) => this.taskType == item.value),
            value: this.dict.type.main_task_type.find((item) => this.taskType == item.value)
              ?.value,
            form: {
              rules: true,
              fieldProps: {
                disabled: true
              }
            },
            search: {
              hidden: true
            },
            table: {
              hidden: true
            }
          },
          taskName: {
            label: '任务名称',
            type: 'text',
            form: {
              rules: true
            },
            table: {
              align: 'left'
            },
            width: 150
          },
          taskStatus: {
            type: 'select',
            label: '任务状态',
            width: 150,
            form: {
              hidden: true
            },
            options: this.dict.type.task_status_14
          },
          deptName: {
            label: '所属部门',
            type: 'text',
            width: 120,
            add: {
              hidden: true
            },
            edit: {
              fieldProps: {
                disabled: true
              }
            }
          },
          taskCode: {
            label: '工单编号',
            type: 'text',
            width: 250,
            add: {
              hidden: true
            },
            edit: {
              fieldProps: {
                disabled: true
              }
            }
          },
          reportType: {
            label: '报告类型',
            type: 'select',
            form: {
              type: 'radio',
              value: '0'
            },
            options: this.dict.type.selfAudit_report_type,
            width: 150
          },
          totalCount: {
            label: '总数',
            type: 'text',
            width: 150,
            form: {
              hidden: true
            },
            search: {
              hidden: true
            }
          },
          disposedCount: {
            label: '已处置数',
            type: 'text',
            width: 150,
            form: {
              hidden: true
            },
            search: {
              hidden: true
            }
          },
          createByName: {
            label: '创建人',
            type: 'text',
            width: 150,
            form: {
              hidden: true,
              rules: true
            }
          },

          remarks: {
            label: '备注',
            type: 'text',
            width: 200,
            search: {
              hidden: true
            },
            form: {
              hidden: false,
              type: 'textarea'
            }
          },
          createTime: {
            type: 'text',
            label: '创建时间',
            search: {
              type: 'date-range',
              parameter: (data) => {
                return {
                  startTime: data?.[0],
                  endTime: data?.[1]
                }
              }
            },
            form: {
              hidden: true
            },
            width: 200
          }
        }
      }

      return value
    }
  },
  methods: {
    handleAttachment(row) {
      this.$refs.attachmentDialogRef.open({
        params: row
      })
    },
    handleInfo(row) {
      this.$refs.sheetRef.handleEdit(row)
    },
    handleIssue(row) {
      this.$refs.issueDialogRef.open({
        params: {
          taskId: row.id
        },
        success: () => {
          this.$refs.sheetRef.getTableData()
        }
      })
    },
    handleView(row) {
      this.$refs.viewDialogRef.open({
        params: {
          reportType: row.reportType,
          taskId: row.id
        },
        success: () => {
          this.$refs.sheetRef.getTableData()
        }
      })
    }
  }
}
</script>

<style></style>
