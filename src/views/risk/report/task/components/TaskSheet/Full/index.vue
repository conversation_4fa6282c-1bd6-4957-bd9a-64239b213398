<template>
  <TaskCompatible v-bind="{ sheetSelection }">
    <template #sheet="{ data, actions }">
      <EleSheet
        ref="sheetRef"
        class="!overflow-hidden"
        v-bind="getSheetProps(data)"
        @selection-change="onSelectionChange"
      >
        <template #table:taskName:simple="{ row }">
          <span
            class="hover:underline text-primary-500 cursor-pointer"
            @click="actions.handleUpdate(row)"
          >
            {{ row.taskName }}
          </span>
        </template>
        <template #table:after>
          <el-table-column
            v-if="actions.showActionBar"
            v-slot="{ row }"
            label="操作"
            width="200"
            align="center"
            fixed="right"
          >
            <slot name="table-actions-before" v-bind="{ row }" />
            <TaskActions v-bind="{ ...actions, row }" />
          </el-table-column>
        </template>
      </EleSheet>
    </template>
  </TaskCompatible>
</template>

<script>
import TaskCompatible from '@/views/risk/report/task/task-compatible.vue'
import TaskActions from '@/views/risk/report/task/components/TaskActions/index.vue'

export default {
  dicts: ['task_status_1'],

  components: {
    TaskCompatible,
    TaskActions
  },
  data() {
    return {
      tableData: [],
      sheetSelection: []
    }
  },
  computed: {
    sheetProps() {
      const value = {
        api: {},

        layout: 'table',

        tableProps: {
          height: '100%',
          selection: 'multiple',
          reserveSelection: false
        },

        hiddenActions: {
          add: !this.$checkPermi(['risk:report:task:add']),
          edit: true,
          remove: !this.$checkPermi(['risk:report:task:edl']),
          export: !this.$checkPermi(['risk:report:task:export'])
        },

        model: {
          taskName: {
            label: '任务名称',
            align: 'left',
            width: 200
          },
          taskStatus: {
            type: 'select',
            label: '任务状态',
            options: this.dict.type.task_status_1
          },
          totalNumber: {
            label: '漏洞总数'
          },
          totalSystemNumber: {
            label: '系统漏洞数量'
          },
          totalLineNumber: {
            label: '基线不合规数量'
          },
          totalPasswordNumber: {
            label: '弱口令数量'
          },
          totalWebNumber: {
            label: '应用漏洞数量'
          },
          totalDispatchNumber: {
            label: '待分配总数'
          },
          totalSystemDispatchNum: {
            label: '系统漏洞待分配'
          },
          totalLineDispatchNum: {
            label: '基线待分配'
          },
          totalPasswordDispatchNum: {
            label: '弱口令待分配'
          },
          totalWebDispatchNum: {
            label: '应用漏洞待分配'
          },
          createByName: {
            label: '创建人',
            width: 150
          },
          createTime: {
            label: '创建时间',
            width: 200
          }
        }
      }

      return value
    }
  },
  watch: {
    tableData: {
      handler() {
        this.$nextTick(() => {
          this.$refs.sheetRef.getTableData()
        })
      },
      deep: true
    }
  },
  methods: {
    getSheetProps(data) {
      this.tableData = data

      return {
        ...this.sheetProps,
        api: {
          ...this.sheetProps.api,
          list: async() => {
            return {
              code: 200,
              rows: this.tableData || []
            }
          }
        }
      }
    },
    onSelectionChange(selection) {
      this.sheetSelection = selection
    }
  }
}
</script>

<style></style>
