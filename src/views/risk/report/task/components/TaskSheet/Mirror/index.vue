<template>
  <TaskCompatible v-bind="{ sheetSelection }">
    <template #sheet="{ data, actions }">
      <EleSheet ref="sheetRef" class="!overflow-hidden" v-bind="getSheetProps(data)" @selection-change="onSelectionChange">
        <template #table:taskName:simple="{ row }">
          <span class="hover:underline text-primary-500 cursor-pointer" @click="actions.handleUpdate(row)">
            {{ row.taskName }}
          </span>
        </template>

        <template #table:after>
          <el-table-column
            v-if="actions.showActionBar"
            v-slot="{ row }"
            label="操作"
            width="200"
            align="center"
            fixed="right"
          >
            <slot name="table-actions-before" v-bind="{ row }" />
            <TaskActions v-bind="{ ...actions, row }" />
          </el-table-column>
        </template>
      </EleSheet>
    </template>
  </TaskCompatible>
</template>

<script>
import TaskCompatible from '@/views/risk/report/task/task-compatible.vue'
import TaskActions from '@/views/risk/report/task/components/TaskActions/index.vue'

export default {
  dicts: ['task_status_6', 'work_order_type'],

  components: {
    TaskCompatible,
    TaskActions
  },
  data() {
    return {
      tableData: [],
      sheetSelection: []
    }
  },
  computed: {
    sheetProps() {
      const value = {
        api: {},

        layout: 'table',

        tableProps: {
          height: '100%',
          selection: 'multiple',
          reserveSelection: false
        },

        hiddenActions: {
          add: !this.$checkPermi(['risk:report:task:add']),
          edit: true,
          remove: !this.$checkPermi(['risk:report:task:edl']),
          export: !this.$checkPermi(['risk:report:task:export'])
        },

        model: {
          taskName: {
            label: '任务名称',
            align: 'left',
            width: 200
          },
          taskStatus: {
            type: 'select',
            label: '任务状态',
            options: this.dict.type.task_status_6
          },
          deptName: {
            label: '所属部门',
            width: 150
          },
          systemName: {
            label: '所属系统',
            width: 150
          },
          workOrderNumber: {
            label: '工单编号',
            width: 200
          },
          workOrderType: {
            label: '工单类型',
            type: 'select',
            options: this.dict.type.work_order_type
          },
          totalNumber: {
            label: '漏洞数量'
          },
          createByName: {
            label: '创建人',
            width: 150
          },
          createTime: {
            label: '创建时间',
            width: 200
          }
        }
      }

      return value
    }
  },
  watch: {
    tableData: {
      handler() {
        this.$nextTick(() => {
          this.$refs.sheetRef.getTableData()
        })
      },
      deep: true
    }
  },
  methods: {
    getSheetProps(data) {
      this.tableData = data

      return {
        ...this.sheetProps,
        api: {
          ...this.sheetProps.api,
          list: async() => {
            return {
              code: 200,
              rows: this.tableData || []
            }
          }
        }
      }
    },
    onSelectionChange(selection) {
      this.sheetSelection = selection
    }
  }
}
</script>

<style></style>
