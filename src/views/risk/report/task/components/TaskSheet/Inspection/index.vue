<template>
  <EleSheet ref="sheetRef" v-bind="sheetProps" class="page-main page-main--full" @add-success="onAddSuccess" @edit-success="onEditSuccess">
    <template #before="{ searchMixin: { lazyModel, parameter } }">
      <TaskStats :key="[taskType, JSON.stringify(lazyModel)].join(',')" v-bind="{ queryParams: { ...parameter(), taskType } }" />
    </template>

    <template #toolbar:after="{ singleSelected, selectionIds, selection, removeHandler }">
      <el-button
        v-hasPermi="['risk:report:task:del']"
        type="danger"
        icon="el-icon-delete"
        :disabled="!singleSelected"
        @click="removeHandler(selectionIds)"
      >删除
      </el-button>

      <!-- <el-button
        v-hasPermi="['risk:report:task:invalidate']"
        type="warning"
        icon="el-icon-document-delete"
        :disabled="!singleSelected"
        @click="onVoidClick(selectionIds, selection)"
      >作废
      </el-button> -->
    </template>

    <template #table:taskName:simple="{ row }">
      <span class="text-primary-500 hover:underline cursor-pointer" @click="handleEdit(row)">
        {{ row.taskName }}
      </span>
    </template>

    <template #table:after>
      <el-table-column v-slot="{ row }" label="操作" fixed="right" align="center" width="150">
        <el-button type="text" size="mini" @click="handleInfo(row)">查看</el-button>
        <el-button v-if="$checkPermi(['inspection:task:report']) && ['4'].includes(row.taskStatus)" type="text" size="mini" @click="handleReport(row)">下载报告</el-button>
        <el-button v-if="$checkPermi(['inspection:task:stop']) && ['2'].includes(row.taskStatus)" type="text" size="mini" @click="handleStop(row)">结束任务</el-button>
      </el-table-column>
    </template>

    <template #after>
      <InfoDialog ref="infoDialogRef" />
    </template>

  </EleSheet>
</template>

<script>
import request from '@/utils/request.js'
import { dataMixin } from '@/mixins'

import { listTask, getTask, addTask, updateTask, delTask } from '@/api/risk/report/task.js'

import TaskStats from '@/views/risk/report/task/components/TaskStats/index.vue'

import InfoDialog from './InfoDialog/index.vue'

export default {
  dicts: ['main_task_type', 'task_status_18', 'work_order_type'],
  components: {
    TaskStats,
    InfoDialog
  },
  mixins: [
    dataMixin({})
  ],

  props: {
    taskType: {
      type: [String, Number],
      default: ''
    }
  },
  data() {
    return {
      activeDeployType: '1',
      activeDeptName: ''
    }
  },
  computed: {
    sheetProps() {
      const value = {
        title: '安全巡检任务',

        lazy: false,

        api: {
          add: async(params) => {
            const nextId = await this.generateTaskId()

            Object.assign(params, { nextId })

            return addTask(params)
          },
          edit: (params) => updateTask({ ...params }),
          list: async(params) => listTask({ ...params, taskType: this.taskType }),
          info: getTask,
          remove: delTask,
          export: (handler) => handler('/risk/report/task/export', { parameter: (params) => ({ ...params, taskType: this.taskType }) }),
          import: '',
          template: ''
        },

        infoProps: {
          title: true
        },

        tableProps: {
          height: '100%'
        },

        editProps: {
          disabled: !this.$checkPermi(['risk:report:task:edit'])
        },

        hiddenActions: {
          add: !this.$checkPermi(['risk:report:task:add']),
          edit: true,
          remove: !this.$checkPermi(['risk:report:task:edl']),
          export: !this.$checkPermi(['risk:report:task:export'])
        },

        model: {
          taskType: {
            label: '任务类型',
            type: 'select',
            width: 150,
            options: this.dict.type.main_task_type.filter((item) => this.taskType == item.value),
            value: this.taskType,
            form: {
              rules: true,
              fieldProps: {
                disabled: true
              }
            },
            search: {
              hidden: true
            },
            table: {
              hidden: true
            }
          },
          taskName: {
            label: '任务名称',
            type: 'text',
            form: {
              rules: true
            },
            table: {
              align: 'left'
            },
            edit: {
              fieldProps: {
                disabled: true
              }
            }
          },
          taskStatus: {
            type: 'select',
            label: '任务状态',
            form: {
              hidden: true
            },
            options: this.dict.type.task_status_18
          },
          securePatrolPerson: {
            label: '巡检人',
            search: {
              hidden: true
            },
            form: {
              rules: true
            }
          },
          securePatrolHostNum: {
            label: '安全巡检主机数量',
            hidden: ['search', 'form']
          },
          securePatrolRiskHostNum: {
            label: '安全巡检风险主机数量',
            hidden: ['search', 'form']
          },
          createBy: {
            label: '创建人',
            type: 'text',
            form: {
              hidden: true,
              rules: true
            }
          },
          createTime: {
            type: 'text',
            label: '创建时间',
            search: {
              type: 'date-range',
              parameter: (data) => {
                return {
                  startTime: data?.[0],
                  endTime: data?.[1]
                }
              }
            },
            form: {
              hidden: true
            },
            width: 200
          },

          securePatrolDate: {
            label: '安全巡检日期',
            search: {
              hidden: true
            },
            table: {
              hidden: true
            },
            form: {
              rules: true
            }
          }

        }
      }

      return value
    }
  },
  methods: {
    onAddSuccess() {
      this.activeDeptName = ''
    },
    onEditSuccess() {
      this.activeDeptName = ''
    },
    handleRefresh() {
      this.$refs.sheetRef.getTableData()
    },
    async generateTaskId() {
      const res = await request({
        url: '/risk/report/task/getTaskNextId'
      })

      return res?.nextId
    },
    handleEdit(row) {
      this.$refs.sheetRef.handleEdit(row)
    },
    handlePublish(row) {
      this.$refs.publishDialogRef.open({
        params: {
          ...row,
          taskId: row.id,
          taskType: row.taskType
        },
        success: () => {
          this.handleRefresh()
        }
      })
    },
    async handleStatement(row) {
      try {
        await this.$confirm('是否确认已结单？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
      } catch (error) {
        return error
      }

      const res = await request({
        url: `/risk/report/task/confirmationStatement`,
        method: 'post',
        data: {
          id: row.id,
          taskType: row.taskType,
          taskStatus: 4
        }
      })

      if (res.code === 200) {
        this.$message.success(res.msg)
        this.handleRefresh()
      } else {
        this.$message.warning(res.msg)
      }
    },
    handleDownload(row) {
      if (!row.uploadFileUrl) return false

      window.open(row.uploadFileUrl)
    },

    async onVoidClick(ids, selection) {
      const disabledRow = selection.find(item => ['4'].includes(String(item.taskStatus)))

      if (disabledRow) {
        this.$message.warning(`任务名称：${disabledRow.taskName} 的任务类型不支持作废`)
        return false
      }

      try {
        await this.$confirm('是否确认作废？', '提示', {
          type: 'warning'
        })
      } catch (error) {
        return error
      }

      const res = await request({
        url: `/risk/report/task/invalidate`,
        method: 'post',
        params: {
          taskId: ids[0]
        }
      })

      if (res.code === 200) {
        this.$message.success(res.msg)
        this.handleRefresh()
      }
    },
    handleInfo(row) {
      this.$refs.infoDialogRef.open({
        params: {
          taskId: row.id
        }
      })
    },
    handleReport(row) {
      this.download('/risk/detail/secure/downloadReport',
        {
          taskId: row.id
        },
        `导出_安全巡检报告_${Date.now()}.xlsx`
      )
    },
    async handleStop(row) {
      await this.$confirm('是否确认结束任务？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })

      const res = await request({
        url: '/risk/report/task/endTask',
        method: 'post',
        data: {
          ids: String(row.id)
        }
      })

      if (res.code === 200) {
        this.$message.success(res.msg)
        this.handleRefresh()
      } else {
        this.$message.warning(res.msg)
      }
    }
  }
}
</script>

<style></style>
