<template>
  <div class="page-main">
    <el-form
      v-show="showSearch"
      ref="queryForm"
      class="sa-query"
      :model="queryParams"
      size="small"
      :inline="true"
      label-width="68px"
    >
      <el-form-item :label="fileColumnName" prop="fileName">
        <el-input
          v-model="queryParams.fileName"
          placeholder="请输入"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item class="query-handle">
        <el-button type="primary" plain icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh-left" @click="resetQuery">重置</el-button>
        <EleSheetSearchMore :parent-ref="()=> $refs.queryForm" />
      </el-form-item>
    </el-form>

    <el-table v-loading="loading" class="sa-table" :data="urlList">
      <el-table-column :label="fileColumnName" align="left" prop="fileName" min-width="200" show-overflow-tooltip />

      <el-table-column v-if="includeColumns.includes('auditReportType')" label="附件类型" align="center" show-overflow-tooltip>
        <template slot-scope="scope">
          <EleTagDict :options="dict.type.selfAudit_attachment_type" :value="scope.row.auditReportType" class="inline" />
        </template>
      </el-table-column>

      <el-table-column v-if="!excludeColumns.includes('reportFileType')" label="文件类型" align="center" show-overflow-tooltip>
        <template slot-scope="scope">
          <EleTagDict :options="dict.type.report_file_type" :value="scope.row.reportFileType" class="inline" />
        </template>
      </el-table-column>
      <el-table-column label="上传时间" align="center" prop="createTime" min-width="200" />
      <!-- <el-table-column
        v-if="urlDetail.taskType == 1 || urlDetail.taskType == 4"
        label="基线检测进度"
        align="center"
        min-width="150"
      >
        <template slot-scope="scope">
          <div>{{ checkStatus(scope.row.lineCheckStatus) }}</div>
          <div v-if="scope.row.lineCheckStatus == 4">{{ scope.row.lineCheckMsg }}</div>
        </template>
      </el-table-column>
      <el-table-column
        v-if="urlDetail.taskType == 1 || urlDetail.taskType == 5"
        label="弱口令检测进度"
        align="center"
        min-width="200"
      >
        <template slot-scope="scope">
          <div
            v-if="scope.row.pwdCheckDataList.length == 0"
          >{{ checkStatus(scope.row.pwdCheckStatus) }}
          </div>
          <template v-else>
            <div v-for="item in scope.row.pwdCheckDataList">
              {{ checkStatus(item.pwdFileCheckStatus) }}
              <span v-if="item.pwdFileCheckStatus == 2">({{ item.pwdCheckProgress }})</span>
            </div>
          </template>
          <div v-if="scope.row.pwdCheckStatus == 4">{{ scope.row.pwdCheckMsg }}</div>
        </template>
      </el-table-column>
      <el-table-column
        v-if="urlDetail.taskType == 1 || urlDetail.taskType == 3"
        label="系统漏洞检测进度"
        align="center"
        min-width="150"
      >
        <template slot-scope="scope">
          <div v-if="scope.row.reportFileType == 3">{{ checkStatus(scope.row.status) }}</div>
          <template v-else>
            <div>{{ checkStatus(scope.row.systemCheckStatus) }}</div>
            <div v-if="scope.row.systemCheckStatus == 4">{{ scope.row.systemCheckMsg }}</div>
          </template>
        </template>
      </el-table-column> -->
      <template v-if="!excludeColumns.includes('status')">
        <el-table-column v-if="['6'].includes(urlDetail.taskType)" label="解析进度" align="center" width="150">
          <template #default="{ row }">
            <EleTagDict :options="dict.type.task_file_status_image" :value="row.status" />
          </template>
        </el-table-column>

        <el-table-column v-else label="检测进度" align="center" width="150">
          <template #default="{ row }">
            <EleTagDict :options="dict.type.task_file_status" :value="row.status" />
          </template>
        </el-table-column>
      </template>

      <el-table-column v-if="!excludeColumns.includes('remarks')" label="备注" align="center" prop="remarks" width="200" show-overflow-tooltip />
      <el-table-column v-if="!excludeColumns.includes('updateTime')" label="修改时间" width="200" align="center">
        <template #default="{ row }">
          {{ row.updateTime }}
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" align="center" width="200">
        <template slot-scope="scope">
          <el-button v-if="scope.row.scaDownload && $checkPermi(['risk:report:urlInfo:image:downloadReport'])" size="mini" type="text" @click="download(scope.row.scaDownload)">检测报告</el-button>
          <el-button v-if="scope.row.imaReportUrl" size="mini" type="text" @click="onDown(scope.row.imaReportUrl,scope.row.fileName)">下载原报告</el-button>
          <el-button v-if="$checkPermi(['risk:report:url:info:download'])" size="mini" type="text" @click="onDown(scope.row.riskReportUrl,scope.row.fileName)">下载</el-button>
          <el-button v-if="!disabledRemove" v-hasPermi="['risk:report:url:remove']" size="mini" type="text" @click="onDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <div class="sa-footer sa-row-center">
      <sa-pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </div>
  </div>
</template>

<script>
import { listUrl, delUrl } from '@/api/risk/report/url'

export default {
  name: 'Url',
  dicts: ['report_file_type', 'task_file_status', 'task_file_status_image', 'selfAudit_attachment_type'],
  props: {
    urlDetail: {
      type: Object,
      default: () => ({})
    },
    excludeColumns: {
      type: Array,
      default: () => []
    },
    includeColumns: {
      type: Array,
      default: () => []
    },
    disabledRemove: {
      type: Boolean,
      default: false
    },
    fileColumnName: {
      type: String,
      default: '报告文件名'
    }
  },
  data() {
    return {
      // 遮罩层
      loading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 异常漏洞的附件地址关联表格数据
      urlList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        fileName: null
      },

      urlTimeId: null
    }
  },
  created() {
    // 1基础检测任务，2黄金镜像任务，3系统漏扫工单任务，4基线工单任务，5弱口令工单任务，6镜像扫描工单任务，7上云工单任务
    this.queryParams = { ...this.queryParams, ...this.$props.urlDetail }
    this.getList()

    this.urlTimeId = setInterval(() => {
      setTimeout(this.getList(0), 0)
    }, 10000)
  },
  destroyed() {
    clearInterval(this.urlTimeId)
  },
  methods: {
    /** 查询异常漏洞的附件地址关联列表 */
    getList(loading) {
      this.loading = loading !== 0
      listUrl(this.queryParams).then((response) => {
        this.urlList = response.rows
        this.total = response.total
        this.loading = false
        this.$emit('list-success', this.urlList)
      })
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 下载
    /*      onDown(downloadPath) {
        // var downloadPath = row.riskReportUrl;
        var downloadLink = document.createElement('a');
        downloadLink.style.display = 'none'; // 使其隐藏
        downloadLink.href = downloadPath;
        downloadLink.download = '';
        downloadLink.click();
      },*/
    // 下载
    onDown(downloadPath, fileName) {
      this.download(
        `${downloadPath}`,
        {},
        fileName
      )
    },

    onDelete(row) {
      // 删除
      const ids = row.id
      this.$modal
        .confirm('是否确认删除当前所选数据项？')
        .then(function() {
          return delUrl(ids)
        })
        .then(() => {
          this.getList()
          this.$modal.msgSuccess('删除成功')
        })
        .catch(() => {})
    },
    checkStatus(type) {
      switch (type) {
        case '1':
          return '未开始'
        case '2':
          return '进行中'
        case '3':
          return '已完成'
        case '4':
          return '失败'
      }
    }
  }
}
</script>
