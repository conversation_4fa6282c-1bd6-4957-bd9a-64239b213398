<template>
  <EleSheet ref="sheetRef" v-bind="sheetProps" class="page-main h-full overflow-hidden" :class="['task'].includes(source) ? 'page-main--full' : ''" @query-success="onQuerySuccess" @reset-success="onQuerySuccess" @form-closed="onFormClosed">
    <template #before>
      <StatsAppTest
        v-if="['task'].includes(source)"
        ref="statsRef"
        v-bind="{
          lazy: false,
          params: searchParams
        }"
      />
    </template>

    <template #toolbar:after>
      <el-button v-if="['default'].includes(source) && $checkPermi(['risk:middle:app:synchronous'])" type="warning" icon="el-icon-refresh" @click="onSyncClick">同步APP安全检测系统全量任务</el-button>
    </template>

    <template #table:action:after="{ row }">
      <el-button v-if="row.assetId && ['1'].includes(row.dataSource) && $checkPermi(['risk:middle:app:downloadScanWrap'])" type="text" size="mini" @click="onAssetClick(row)">下载检测包</el-button>

      <el-button v-if="['2'].includes(row.taskStatus) && $checkPermi(['risk:middle:app:scanStop'])" type="text" size="mini" @click="onStopClick(row)">终止</el-button>

      <el-button v-if="['3'].includes(row.taskStatus) && $checkPermi(['risk:middle:app:again:detection'])" type="text" size="mini" @click="onRetestClick(row)">重新检测</el-button>

      <el-dropdown v-if="['4'].includes(row.taskStatus) && $checkPermi(['risk:middle:app:downloadReport'])" :key="row.id" @command="onDownloadClick(row, $event)">
        <el-button class="" type="text" size="mini">
          下载报告<i class="el-icon-arrow-down el-icon--right"></i>
        </el-button>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="1">Word</el-dropdown-item>
            <el-dropdown-item command="2">PDF</el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </template>

    <template #table:risksNum:simple="{ row }">
      <div v-if="row.risksNum" class="">
        <span
          v-for="(item, index) of row.risksNum.split('/')"
          :key="index"
        >
          <span v-if="index > 0" class="px-1 text-gray-500">/</span>
          <span
            :class="{
              'text-red-500': index === 0,
              'text-yellow-500': index === 1,
              'text-green-500': index === 2
            }"
          >{{ item }}</span>
        </span>
      </div>
    </template>

    <template #search:deptName:simple="{ model }">
      <CommonDepartmentSelect
        v-model="model.deptName"
        placeholder="请输入"
        clearable
        return-name
        @change="() => (model.systemName = void 0)"
      />
    </template>

    <template #search:systemName:simple="{ model }">
      <CommonSystemSelect
        v-model="model.systemName"
        placeholder="请输入"
        return-name
        clearable
        :params="{
          deptName: model.deptName,
        }"
      />
    </template>

    <template #form:deptName:simple="{ model }">
      <CommonDepartmentSelect
        v-model="model.deptName"
        placeholder="请输入"
        clearable
        return-name
        @change="() => (model.systemName = void 0)"
      />
    </template>

    <template #form:systemName:simple="{ model }">
      <CommonSystemSelect
        v-model="model.systemName"
        placeholder="请输入"
        return-name
        clearable
        :params="{
          deptName: model.deptName,
        }"
      />
    </template>

    <template #search:relTaskId:simple="{ model }">
      <AppTaskSelect
        v-model="model.relTaskId"
        :params="{
          detectTool: activeDetectTool
        }"
      />
    </template>

    <template #form:relTaskId:simple="{ model }">
      <AppTaskSelect
        v-model="model.relTaskId"
        :label.sync="model.relTaskName"
        :params="{
          detectTool: activeDetectTool
        }"
      />
    </template>

    <template #info:risksNum:simple="{ data: row }">
      <div v-if="row.risksNum" class="">
        <span
          v-for="(item, index) of row.risksNum.split('/')"
          :key="index"
        >
          <span v-if="index > 0" class="px-1 text-gray-500">/</span>
          <span
            :class="{
              'text-red-500': index === 0,
              'text-yellow-500': index === 1,
              'text-green-500': index === 2
            }"
          >{{ item }}</span>
        </span>
      </div>
    </template>

    <template #form:actionList:simple="{ model }">
      <el-button type="default" icon="" @click="handleMenu(model, 'actionList')">
        <i v-if="(model.actionList || []).length" class="el-icon-success mr-1 text-green-500"></i>
        <span class="">导入 / 配置</span>
      </el-button>
    </template>

    <template #info:assetDownUrl:simple="{ data }">
      <div v-if="['1'].includes(data.dataSource)" class="truncate text-primary-500 hover:underline cursor-pointer" @click="download(data.assetDownUrl)"> {{ data.assetDownUrl }}</div>
      <EleFloating v-else class="" :text="data.assetDownUrl">
      </EleFloating>
    </template>

    <template #info:iconUrl:simple="{ data }">
      <div class="truncate text-primary-500 hover:underline cursor-pointer" @click="onIconClick"> {{ data.iconUrl }}</div>
      <el-image v-show="false" ref="iconImageRef" :key="data.iconUrl" :src="data.iconUrl" :preview-src-list="[data.iconUrl]" class=""></el-image>
    </template>

    <template #info:after="{ data }">
      <div class="overflow-hidden">
        <EleTitle class="pb-6">明细列表</EleTitle>

        <template v-if="data.id">
          <DetailSoftwareSheet
            v-if="['1'].includes(data.detectTool)"
            v-bind="{
              params: {
                taskId: data.id,
              },
            }"
          />
          <DetailSheet
            v-else
            v-bind="{
              params: {
                taskId: data.id,
              },
            }"
          />
        </template>
      </div>
    </template>

    <template #after>
      <MenuConfigDialog ref="menuConfigDialogRef" />
    </template>
  </EleSheet>
</template>

<script>
import {
  addAppTest,
  delAppTest,
  getAppTest,
  listAppTest,
  updateAppTest
} from '@/api/risk/resource/app.js'

import request from '@/utils/request.js'

import MenuConfigDialog from './components/MenuConfigDialog/index.vue'
import DetailSheet from './components/DetailSheet/index.vue'
import DetailSoftwareSheet from './components/DetailSoftwareSheet/index.vue'
import path from 'path'
import StatsAppTest from '@/views/risk/warn/middleHandleStatistics/StatsAppTest/index.vue'
import AppTaskSelect from './components/AppTaskSelect/index.vue'

export default {
  dicts: ['app_detection_task_status', 'app_detection_data_sources', 'app_detection_type', 'app_code_detect_tool', 'app_code_detect_tool_insert'],
  components: {
    MenuConfigDialog,
    DetailSheet,
    DetailSoftwareSheet,
    StatsAppTest,
    AppTaskSelect
  },
  data() {
    return {
      searchParams: {},

      activeDetectTool: '0'
    }
  },
  computed: {
    source() {
      return this.$route.query.source || 'default'
    },
    sheetProps() {
      const value = {
        title: 'APP安全检测',

        lazy: false,

        api: {
          add: (params) => addAppTest({ ...params }),
          edit: (params) => updateAppTest({ ...params }),
          list: async(params) => listAppTest({ ...params }),
          info: getAppTest,
          remove: delAppTest,
          export: '/risk/app/export',
          // import: '/risk/app/importTemplate',
          template: '/risk/app/importAppTemplate'
        },

        tableProps: {
          height: '100%'
        },

        tableActionProps: {
          width: 250
        },

        infoProps: {
          title: true
        },

        hiddenActions: {
          add: !['task'].includes(this.source) || !this.$checkPermi(['risk:middle:app:add']),
          remove: !['task'].includes(this.source) || !this.$checkPermi(['risk:middle:app:remove']),
          edit: true,
          export: !this.$checkPermi(['risk:middle:app:export'])
        },

        model: {
          taskCode: {
            label: '任务编号',
            search: {
              hidden: true
            },
            form: {
              hidden: true
            },
            table: {
              align: 'left',
              width: 200
            }
          },
          taskName: {
            label: '任务名称',
            search: {
            },
            form: {
              rules: true
            },
            table: {
              width: 200
            }
          },
          detectTool: {
            label: '检测工具',
            type: 'select',
            form: {
              rules: true,
              type: 'radio',
              value: '0',
              options: this.dict.type.app_code_detect_tool_insert,
              fieldProps: {
                on: {
                  change: (value, ctx) => {
                    this.activeDetectTool = value
                    ctx.model.taskType = '1'
                  }
                }
              }
            },
            options: this.dict.type.app_code_detect_tool,
            table: {
              width: 150
            }
          },
          taskType: {
            type: 'select',
            label: '应用类型',
            rules: true,
            table: {
              width: 100
            },
            search: {},
            form: {
              type: 'radio',
              value: '1'
            },
            options: [
              { label: '软件包/H5', value: '1' },
              { label: '公众号', value: '2', disabled: ['1'].includes(this.activeDetectTool) },
              { label: '小程序', value: '3', disabled: ['1'].includes(this.activeDetectTool) }
            ]
          },

          taskStatus: {
            type: 'select',
            label: '检测进度',
            rules: true,
            table: {
              width: 100
            },
            search: {},
            form: {
              hidden: true
            },
            options: this.dict.type.app_detection_task_status
          },
          checkSendTime: {
            type: 'text',
            label: '检测发起时间',
            rules: false,
            table: {
              width: 200
            },
            search: {
              hidden: true
            },
            form: {
              hidden: true
            }
          },
          checkEndTime: {
            type: 'text',
            label: '检测结束时间',
            rules: false,
            table: {
              width: 200
            },
            search: {
              hidden: true
            },
            form: {
              hidden: true
            }
          },
          checkScore: {
            label: '检测分数',
            rules: false,
            table: {
              width: 90
            },
            search: {
              hidden: true
            },
            form: {
              hidden: true
            }
          },
          grade: {
            label: '危险等级',
            rules: false,
            table: {
              width: 100
            },
            search: {},
            form: {
              hidden: true
            }
          },
          risksNum: {
            type: 'text',
            label: '安全风险数量',
            rules: false,
            table: {
              align: 'left',
              width: 200
            },
            search: {
              hidden: true
            },
            form: {
              hidden: true
            }
          },
          dataSource: {
            type: 'select',
            label: '数据来源',
            rules: false,
            table: {
              width: 150
            },
            search: {
              value: ['task'].includes(this.source) ? '1' : void 0
            },
            form: {
              hidden: true
            },
            options: this.dict.type.app_detection_data_sources
          },

          detectionType: {
            type: 'select',
            label: '检测类型',
            search: {
              hidden: true
            },
            table: {
              hidden: true
            },
            form: {
              rules: true,
              type: 'radio',
              value: '1'
            },
            hidden: (data) => !['1'].includes(data.taskType),
            options: this.dict.type.app_detection_type,
            group: 'detail'
          },
          assetDownUrl: {
            label: '检测应用',
            search: {
              hidden: true
            },
            table: {
              hidden: true
            },
            form: {
              rules: true,
              type: 'upload',
              fieldProps: {
                action: '/minIOController/upload',
                data: {
                  bucketName: 'app-asset'
                },
                formatter: (data) => ({ ...data, name: data?.response?.data?.originalFilename, url: data?.response?.data?.url }),
                uploadText: '上传应用'
              },
              parameter: (data) => data?.[0]?.url
            },
            hidden: (data) => !['1'].includes(data.taskType),
            group: 'detail'
          },
          wechatName: {
            label: '公众号名称',
            search: {
              hidden: true
            },
            table: {
              hidden: true
            },
            form: {
              rules: true
            },
            hidden: (data) => !['2'].includes(data.taskType),
            group: 'detail'
          },
          wechatNo: {
            label: '微信号',
            search: {
              hidden: true
            },
            table: {
              hidden: true
            },
            form: {
              rules: true
            },
            hidden: (data) => !['2'].includes(data.taskType),
            group: 'detail'
          },
          iconUrl: {
            label: '图标',
            search: {
              hidden: true
            },
            table: {
              hidden: true
            },
            form: {
              rules: true,
              type: 'upload',
              fieldProps: {
                uploadText: '点击上传',
                listType: 'picture',
                accept: '.png,.jpg,.jpeg'
              },
              parameter: (data) => data?.[0]?.url
            },
            hidden: (data) => !['2', '3'].includes(data.taskType),
            group: 'detail'
          },
          registerBody: {
            label: '注册主体',
            search: {
              hidden: true
            },
            table: {
              hidden: true
            },
            form: {
              rules: true
            },
            hidden: (data) => !['2'].includes(data.taskType),
            group: 'detail'
          },
          urlAddress: {
            label: 'URL地址',
            search: {
              hidden: true
            },
            table: {
              hidden: true
            },
            form: {
              type: 'textarea',
              fieldProps: {
                placeholder: '请输入URL地址\n注意：如果有多个链接时请换行输入'
              },
              parameter: (value) => (value || '').split('\n').map(item => item.trim()).join(','),
              formatter: (data) => (data.urlAddress || '').replaceAll(',', '\n')
            },
            hidden: (data) => !['2', '3'].includes(data.taskType),
            group: 'detail'
          },
          actionList: {
            label: '功能菜单',
            search: {
              hidden: true
            },
            table: {
              hidden: true
            },
            form: {
            },
            hidden: (data) => !['2', '3'].includes(data.taskType),
            group: 'detail'
          },
          wechatIndex: {
            label: '搜索索引字段',
            search: {
              hidden: true
            },
            table: {
              hidden: true
            },
            form: {
            },
            hidden: (data) => !['2', '3'].includes(data.taskType),
            group: 'detail'
          },
          wechatMiniProgramName: {
            label: '小程序名称',
            search: {
              hidden: true
            },
            table: {
              hidden: true
            },
            form: {
              rules: true
            },
            hidden: (data) => !['3'].includes(data.taskType),
            group: 'detail'
          },
          appId: {
            label: 'AppId',
            search: {
              hidden: true
            },
            table: {
              hidden: true
            },
            form: {
              rules: true
            },
            hidden: (data) => !['3'].includes(data.taskType),
            group: 'detail'
          },
          signature: {
            label: '签名',
            search: {
              hidden: true
            },
            table: {
              hidden: true
            },
            form: {
              rules: true
            },
            hidden: (data) => !['3'].includes(data.taskType),
            group: 'detail'
          },
          deptName: {
            label: '所属部门',
            search: {},
            table: {},
            form: {
              rules: true
            }
          },
          systemName: {
            label: '所属系统',
            search: {},
            table: {},
            form: {
              rules: true
            }
          },
          relTaskId: {
            label: '关联任务',
            search: {},
            table: {
              formatter: (data) => data.relTaskName
            },
            info: {
              formatter: (data) => data.relTaskName
            },
            form: {}
          },
          relTaskName: {
            hidden: true
          },
          createTime: {
            type: 'text',
            label: '创建时间',
            search: {
              label: '创建时间',
              type: 'date-time-range',
              parameter: (data) => {
                return {
                  startTime: data?.[0],
                  endTime: data?.[1]
                }
              }
            },
            form: {
              hidden: true
            },
            width: 200
          },
          remarks: {
            label: '备注',
            search: {
              hidden: true
            },
            table: {
              hidden: true
            },
            form: {
              type: 'textarea'
            },
            group: 'detail'
          }
        }
      }

      return value
    }
  },
  created() {
    if (['task'].includes(this.source)) {
      this.$set(this.searchParams, 'dataSource', '1')
    }
  },
  methods: {
    async handleMenu(model, field) {
      this.$refs.menuConfigDialogRef.open({
        data: model[field],
        success: (data) => {
          this.$set(model, field, data)
        }
      })
    },
    onIconClick() {
      this.$refs.iconImageRef.clickHandler()
    },
    async onDownloadClick(row, type) {
      this.download(
        '/risk/app/downloadReport',
        {
          id: row.id,
          type,
          detectTool: row.detectTool
        },
        `APP安全检报告_${Date.now()}.${type == 1 ? 'docx' : 'pdf'}`,
        {
          method: 'post',
          headers: { 'Content-Type': 'application/json;charset=utf-8' }
        }
      )
    },
    async onStopClick(row) {
      try {
        await this.$confirm('是否确定要终止吗?', '提示', {
          type: 'warning'
        })
      } catch (error) {
        return error
      }

      const res = await request({
        url: `/risk/app/stop/${row.id}`,
        method: 'get'
      })

      if (res.code == 200) {
        this.$message.success(res.msg)
        this.$refs.sheetRef.getTableData()
      }
    },
    async onRetestClick(row) {
      try {
        await this.$confirm('是否确定重新检测?', '提示', {
          type: 'warning'
        })
      } catch (error) {
        return error
      }

      const res = await request({
        url: `/risk/app/restart/${row.id}`,
        method: 'get'
      })

      if (res.code == 200) {
        this.$message.success(res.msg)
        this.$refs.sheetRef.getTableData()
      }
    },
    async onSyncClick() {
      try {
        await this.$confirm('是否确定同步?', '提示', {
          type: 'warning'
        })
      } catch (error) {
        return error
      }

      const res = await request({
        url: '/risk/app/syncNow',
        method: 'get'
      })

      if (res.code == 200) {
        this.$message.success(res.msg)
        this.$refs.sheetRef.getTableData()
      }
    },
    async onQuerySuccess(ctx) {
      this.searchParams = ctx.parameter()

      this.$emit('query-change', this.searchParams)

      await this.$nextTick()
      if (this.$refs.statsRef) {
        await this.$refs.statsRef.load()
      }
    },
    onAssetClick(row) {
      this.download(`risk/app/downloadAssets/${row.assetId}`, {}, decodeURIComponent(path.basename(row.assetDownUrl)))
    },
    onFormClosed() {
      this.activeDetectTool = this.$options.data().activeDetectTool
    }
  }
}
</script>

<style></style>
