<template>
  <EleSheet ref="sheetRef" v-bind="sheetProps" class="page-main h-full overflow-hidden" :class="['task'].includes(source) ? 'page-main--full' : ''" v-on="$listeners" @query-success="onQuerySuccess" @reset-success="onQuerySuccess" @form-closed="onFormClosed">
    <template #before>
      <StatsSourceCode
        v-if="['task'].includes(source)"
        ref="statsRef"
        v-bind="{
          lazy: false,
          params: searchParams
        }"
      />
    </template>

    <template #toolbar:after>
      <el-button v-if="['default'].includes(source) && $checkPermi(['risk:middle:sourceCode:synchronous'])" type="warning" icon="el-icon-refresh" @click="onSyncClick">同步源代码审计平台全量任务</el-button>
    </template>

    <template #table:qualityStatistics.riskNum:simple="{ row }">
      <div class="space-x-1 truncate">
        <span class="text-purple-500">严重: {{ row.qualityStatistics.serious }}</span>
        <span class="">/</span>
        <span class="text-red-500">高危: {{ row.qualityStatistics.high }}</span>
        <span class="">/</span>
        <span class="text-yellow-500">中危: {{ row.qualityStatistics.middle }}</span>
        <span class="">/</span>
        <span class="text-green-500">低危:{{ row.qualityStatistics.low }}</span>
        <span class="">/</span>
        <span class="text-primary-500">警告:{{ row.qualityStatistics.waring }}</span>
      </div>
    </template>
    <template #table:securityStatistics.riskNum:simple="{ row }">
      <div class="space-x-1 truncate">
        <span class="text-purple-500">严重: {{ row.securityStatistics.serious }}</span>
        <span class="">/</span>
        <span class="text-red-500">高危: {{ row.securityStatistics.high }}</span>
        <span class="">/</span>
        <span class="text-orange-500">中危: {{ row.securityStatistics.middle }}</span>
        <span class="">/</span>
        <span class="text-green-500">低危:{{ row.securityStatistics.low }}</span>
        <span class="">/</span>
        <span class="text-primary-500">警告:{{ row.securityStatistics.waring }}</span>
      </div>
    </template>

    <template #info:qualityStatistics.riskNum:simple="{ data: row }">
      <div class="space-x-1 truncate">
        <span class="text-purple-500">严重: {{ row.qualityStatistics.serious }}</span>
        <span class="">/</span>
        <span class="text-red-500">高危: {{ row.qualityStatistics.high }}</span>
        <span class="">/</span>
        <span class="text-yellow-500">中危: {{ row.qualityStatistics.middle }}</span>
        <span class="">/</span>
        <span class="text-green-500">低危:{{ row.qualityStatistics.low }}</span>
        <span class="">/</span>
        <span class="text-primary-500">警告:{{ row.qualityStatistics.waring }}</span>
      </div>
    </template>

    <template #info:securityStatistics.riskNum:simple="{ data: row }">
      <div class="space-x-1 truncate">
        <span class="text-purple-500">严重: {{ row.securityStatistics.serious }}</span>
        <span class="">/</span>
        <span class="text-red-500">高危: {{ row.securityStatistics.high }}</span>
        <span class="">/</span>
        <span class="text-yellow-500">中危: {{ row.securityStatistics.middle }}</span>
        <span class="">/</span>
        <span class="text-green-500">低危:{{ row.securityStatistics.low }}</span>
        <span class="">/</span>
        <span class="text-primary-500">警告:{{ row.securityStatistics.waring }}</span>
      </div>
    </template>

    <template #table:action:after="{ row }">
      <el-button
        v-if="['3'].includes(row.taskStatus) && $checkPermi(['risk:middle:sourceCode:detection'])"
        type="text"
        size="mini"
        @click="onRetestClick(row)"
      >重新检测</el-button>

      <el-dropdown v-if="['3'].includes(row.taskStatus) && $checkPermi(['risk:middle:sourceCode:downloadReport'])" @command="onDownloadClick(row, $event)">
        <el-button class="" type="text" size="mini">
          下载报告<i class="el-icon-arrow-down el-icon--right"></i>
        </el-button>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="word">Word</el-dropdown-item>
            <el-dropdown-item command="pdf">PDF</el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </template>

    <template #search:deptName:simple="{ model }">
      <CommonDepartmentSelect
        v-model="model.deptName"
        placeholder="请输入"
        clearable
        return-name
        @change="() => (model.systemName = void 0)"
      />
    </template>

    <template #search:systemName:simple="{ model }">
      <CommonSystemSelect
        v-model="model.systemName"
        placeholder="请输入"
        return-name
        clearable
        :params="{
          deptName: model.deptName,
        }"
      />
    </template>

    <template #form:deptName:simple="{ model }">
      <CommonDepartmentSelect
        v-model="model.deptName"
        placeholder="请输入"
        clearable
        return-name
        @change="() => (model.systemName = void 0)"
      />
    </template>

    <template #form:systemName:simple="{ model }">
      <CommonSystemSelect
        v-model="model.systemName"
        placeholder="请输入"
        return-name
        clearable
        :params="{
          deptName: model.deptName,
        }"
      />
    </template>

    <template #search:relTaskId:simple="{ model }">
      <SourceCodeSelect
        v-model="model.relTaskId"
        :params="{
          detectTool: activeDetectTool
        }"
      />
    </template>

    <template #form:relTaskId:simple="{ model }">
      <SourceCodeSelect
        v-model="model.relTaskId"
        :label.sync="model.relTaskName"
        :params="{
          detectTool: activeDetectTool
        }"
      />
    </template>

    <template #info:after="{ data }">
      <div class="overflow-hidden">
        <EleTitle class="pb-6">明细列表</EleTitle>

        <template v-if="data.id">
          <DetailSoftwareSheet
            v-if="['1'].includes(data.detectTool)"
            v-bind="{
              params: {
                taskId: data.id,
              },
            }"
          />
          <DetailSheet
            v-else
            v-bind="{
              params: {
                taskId: data.id,
              },
            }"
          />
        </template>

      </div>
    </template>

    <template #after> </template>
  </EleSheet>
</template>

<script>
import {
  addSourceCodeAudit,
  delSourceCodeAudit,
  getSourceCodeAudit,
  listSourceCodeAudit,
  updateSourceCodeAudit
} from '@/api/risk/resource/source.js'

import request from '@/utils/request.js'

import DetailSheet from './components/DetailSheet/index.vue'
import DetailSoftwareSheet from './components/DetailSoftwareSheet/index.vue'

import StatsSourceCode from '@/views/risk/warn/middleHandleStatistics/StatsSourceCode/index.vue'

import SourceCodeSelect from './components/SourceCodeSelect/index.vue'

export default {
  dicts: ['source_code_audit_language_type', 'source_code_audit_status', 'source_code_data_source', 'app_code_detect_tool', 'app_code_detect_tool_insert'],
  components: {
    DetailSheet,
    DetailSoftwareSheet,
    StatsSourceCode,
    SourceCodeSelect
  },
  data() {
    return {
      statsModel: () => ({
        status: 0,
        taskId: '',
        serious: 0,
        high: 0,
        middle: 0,
        low: 0,
        waring: 0,
        density: '0%',
        timeString: 0,
        timeStart: 0,
        timeEnd: 0,
        lines: '0',
        allLines: '0',
        passed: 'yes'
      }),
      searchParams: {},
      activeDetectTool: '0'
    }
  },
  computed: {
    source() {
      return this.$route.query.source || 'default'
    },
    sheetProps() {
      const value = {
        title: '源代码检测',

        lazy: false,

        api: {
          add: (params) => addSourceCodeAudit({ ...params }),
          edit: (params) => updateSourceCodeAudit({ ...params }),
          list: async(params) => {
            const res = await listSourceCodeAudit({ ...params })

            const rows = res.rows.map((item) => ({
              ...item,
              securityStatistics: item.securityStatistics
                ? JSON.parse(item.securityStatistics)
                : this.statsModel(),
              qualityStatistics: item.qualityStatistics ? JSON.parse(item.qualityStatistics) : this.statsModel()
            }))

            Object.assign(res, {
              rows
            })

            return res
          },
          info: async(...args) => {
            const res = await getSourceCodeAudit(...args)

            const data = res.data

            Object.assign(res.data, {
              qualityStatistics: data.qualityStatistics ? JSON.parse(data.qualityStatistics) : this.statsModel(),
              securityStatistics: data.securityStatistics ? JSON.parse(data.securityStatistics) : this.statsModel()
            })

            return res
          },
          remove: delSourceCodeAudit,
          export: '/risk/sourceCodeAuditTask/export',
          // import: '/risk/sourceCodeAuditTask/importTemplate',
          template: '/risk/sourceCodeAuditTask/importAppTemplate'
        },

        tableProps: {
          height: '100%'
        },

        infoProps: {
          title: true
        },

        hiddenActions: {
          add: !['task'].includes(this.source) || !this.$checkPermi(['risk:middle:sourceCode:add']),
          remove: !['task'].includes(this.source) || !this.$checkPermi(['risk:middle:sourceCode:remove']),
          edit: true,
          export: !this.$checkPermi(['risk:middle:sourceCode:export'])
        },

        tableActionProps: {
          width: 250
        },

        model: {
          taskCode: {
            label: '任务编号',
            search: {
              hidden: true
            },
            form: {
              hidden: true
            },
            table: {
              align: 'left',
              width: 200
            }
          },
          taskName: {
            label: '任务名称',
            search: {},
            form: {
              rules: true
            },
            table: {
              align: 'left',
              width: 200
            }
          },
          detectTool: {
            label: '检测工具',
            type: 'select',
            form: {
              rules: true,
              type: 'radio',
              value: '0',
              fieldProps: {
                on: {
                  change: (value, ctx) => {
                    this.activeDetectTool = value
                    if (ctx.model.taskType === 'svn') {
                      ctx.model.taskType = 'file'
                    }
                  }
                }
              },
              options: this.dict.type.app_code_detect_tool_insert
            },
            options: this.dict.type.app_code_detect_tool,
            table: {
              width: 150
            }
          },
          taskType: {
            type: 'select',
            label: '任务类型',
            table: {
            },
            form: {
              type: 'radio',
              value: 'file',
              rules: true
            },
            options: [
              { label: '源码包', value: 'file' },
              { label: 'GIT', value: 'git' },
              { label: 'SVN', value: 'svn', disabled: ['1'].includes(this.activeDetectTool) }
            ]
          },
          langType: {
            type: 'select',
            label: '语言类型',
            table: {
            },
            form: {
              rules: true
            },
            width: 150,
            options: this.dict.type.source_code_audit_language_type
          },
          projectGroupName: {
            type: 'text',
            label: '项目名称',
            table: {
            },
            form: {
              hidden: true,
              rules: true
            }
          },
          taskStatus: {
            type: 'select',
            label: '检测状态',
            table: {
              width: 150,
              showOverflowTooltip: true
            },
            search: {
              hidden: false
            },
            form: {
              hidden: true,
              rules: false
            },
            options: this.dict.type.source_code_audit_status
          },
          'securityStatistics.riskNum': {
            type: 'text',
            label: '安全风险数量',
            align: 'left',
            table: {
              width: 400
            },
            form: {
              hidden: true,
              rules: false
            },
            search: {
              hidden: true
            },
            info: {
              lg: 24
            }
          },
          checkSendTime: {
            type: 'text',
            label: '检测发起时间',
            rules: false,
            table: {
              width: 200
            },
            search: {
              hidden: true
            },
            form: {
              hidden: true
            }
          },
          checkEndTime: {
            type: 'text',
            label: '检测结束时间',
            rules: false,
            table: {
              width: 200
            },
            search: {
              hidden: true
            },
            form: {
              hidden: true
            }
          },
          'securityStatistics.allLines': {
            label: '安全代码总行数',
            table: {
            },
            form: {
              hidden: true,
              rules: false
            },
            search: {
              hidden: true
            }
          },
          'securityStatistics.lines': {
            label: '安全有效代码行数',
            table: {
            },
            form: {
              hidden: true,
              rules: false
            },
            search: {
              hidden: true
            }
          },
          'securityStatistics.density': {
            type: 'text',
            label: '安全代码缺陷密度',
            table: {
            },
            form: {
              hidden: true,
              rules: false
            },
            search: {
              hidden: true
            }
          },
          'qualityStatistics.riskNum': {
            type: 'text',
            label: '质量风险数量',
            align: 'left',
            table: {
              width: 400
            },
            form: {
              hidden: true,
              rules: false
            },
            search: {
              hidden: true
            },
            info: {
              lg: 24
            }
          },
          'qualityStatistics.allLines': {
            label: '质量代码总行数',
            table: {
            },
            form: {
              hidden: true,
              rules: false
            },
            search: {
              hidden: true
            }
          },
          'qualityStatistics.lines': {
            label: '质量有效代码行数',
            table: {
            },
            form: {
              hidden: true,
              rules: false
            },
            search: {
              hidden: true
            }
          },
          'qualityStatistics.density': {
            type: 'text',
            label: '质量代码缺陷密度',
            table: {
            },
            form: {
              hidden: true,
              rules: false
            },
            search: {
              hidden: true
            }
          },
          dataSource: {
            type: 'select',
            label: '数据来源',
            table: {
              width: 160
            },
            search: {
              value: ['task'].includes(this.source) ? '1' : void 0
            },
            form: {
              hidden: true,
              rules: false
            },
            options: this.dict.type.source_code_data_source
          },
          fileUrl: {
            label: '源码包文件',
            type: 'text',
            search: {
              hidden: true
            },
            table: {
              hidden: true
            },
            form: {
              rules: true,
              type: 'upload',
              fieldProps: {
                accept: '.zip',
                action: '/minIOController/upload',
                data: {
                  bucketName: 'app-asset'
                },
                formatter: (data) => ({ ...data, name: data?.response?.data?.originalFilename, url: data?.response?.data?.url })
              },
              parameter: (data) => data?.[0]?.url
            },
            hidden: (data = {}) => !['file'].includes(data.taskType)
          },
          svngitUrl: {
            label: '代码下载地址',
            type: 'text',
            search: {
              hidden: true
            },
            table: {
              hidden: true
            },
            form: {
              rules: true
            },
            hidden: (data = {}) => !['svn', 'git'].includes(data.taskType)
          },
          authType: {
            label: '认证类型',
            type: 'select',
            search: {
              hidden: true
            },
            table: {
              hidden: true
            },
            form: {
              type: 'radio',
              value: '1',
              rules: true
            },
            options: [
              {
                label: '用户名/密码',
                value: '1'
              },
              {
                label: 'SSH密钥文件',
                value: '2',
                disabled: ['1'].includes(this.activeDetectTool)
              },
              {
                label: 'RSA私钥串',
                value: '3',
                disabled: ['1'].includes(this.activeDetectTool)
              }
            ],
            hidden: (data = {}) => !['svn', 'git'].includes(data.taskType)
          },
          gitBranchName: {
            label: '代码分支',
            type: 'text',
            search: {
              hidden: true
            },
            table: {
              hidden: true
            },
            form: {
              rules: true,
              value: 'master'
            },
            hidden: (data = {}) => !['svn', 'git'].includes(data.taskType)
          },
          svngitUserName: {
            label: '用户名',
            type: 'text',
            search: {
              hidden: true
            },
            table: {
              hidden: true
            },
            form: {
              rules: true
            },
            hidden: (data = {}) =>
              !['svn', 'git'].includes(data.taskType) || !['1'].includes(data.authType)
          },
          svngitPassword: {
            label: '密码',
            type: 'text',
            search: {
              hidden: true
            },
            table: {
              hidden: true
            },
            form: {
              rules: true
            },
            hidden: (data = {}) =>
              !['svn', 'git'].includes(data.taskType) || !['1'].includes(data.authType)
          },
          sshkeyFileUrl: {
            label: 'SSH密匙文件',
            type: 'text',
            search: {
              hidden: true
            },
            table: {
              hidden: true
            },
            form: {
              type: 'upload',
              parameter: (data) => data?.[0]?.url,
              rules: true
            },
            hidden: (data = {}) =>
              !['svn', 'git'].includes(data.taskType) || !['2'].includes(data.authType)
          },
          idRsa: {
            label: 'RSA私钥串',
            search: {
              hidden: true
            },
            table: {
              hidden: true
            },
            form: {
              type: 'textarea',
              rules: true
            },
            hidden: (data = {}) =>
              !['svn', 'git'].includes(data.taskType) || !['3'].includes(data.authType)
          },
          deptName: {
            label: '所属部门',
            search: {},
            table: {},
            form: {
              rules: true
            }
          },
          systemName: {
            label: '所属系统',
            search: {},
            table: {},
            form: {
              rules: true
            }
          },
          relTaskId: {
            label: '关联任务',
            search: {},
            table: {
              formatter: (data) => data.relTaskName
            },
            info: {
              formatter: (data) => data.relTaskName
            },
            form: {}
          },
          relTaskName: {
            hidden: true
          },
          createTime: {
            type: 'text',
            label: '创建时间',
            search: {
              label: '创建时间',
              type: 'date-time-range',
              parameter: (data) => {
                return {
                  startTime: data?.[0],
                  endTime: data?.[1]
                }
              }
            },
            table: {
              width: 200
            },
            form: {
              hidden: true
            }
          },
          remarks: {
            label: '备注',
            search: {
              hidden: true
            },
            table: {
              hidden: true
            },
            form: {
              type: 'textarea'
            }
          }
        }
      }

      return value
    }
  },
  created() {
    if (['task'].includes(this.source)) {
      this.$set(this.searchParams, 'dataSource', '1')
    }
  },
  methods: {
    async handleMenu(model, field) {
      this.$refs.menuConfigDialogRef.open({
        data: model[field],
        success: (data) => {
          this.$set(model, field, data)
        }
      })
    },
    async onRetestClick(row) {
      try {
        await this.$confirm('是否确定重新检测?', '提示', {
          type: 'warning'
        })
      } catch (error) {
        return error
      }

      const res = await request({
        url: '/risk/sourceCodeAuditTask/restartCheck',
        method: 'post',
        data: {
          id: row.id
        }
      })

      if (res.code == 200) {
        this.$message.success(res.msg)
        this.$refs.sheetRef.getTableData()
      }
    },
    async onDownloadClick(row, reportType) {
      if (['word'].includes(reportType) && row.wordReportUrl) {
        this.download(row.wordReportUrl)
        return false
      }

      if (['pdf'].includes(reportType) && row.pdfReportUrl) {
        this.download(row.pdfReportUrl)
        return false
      }

      const res = await request({
        url: '/risk/sourceCodeAuditTask/getPdfOrWordReport',
        method: 'post',
        data: {
          taskId: row.taskId,
          reportType,
          detectTool: row.detectTool
        }
      })

      if (res.code == 200) {
        this.download(res.msg)
      }
    },
    async onSyncClick() {
      try {
        await this.$confirm('是否确定同步?', '提示', {
          type: 'warning'
        })
      } catch (error) {
        return error
      }

      const res = await request({
        url: '/risk/sourceCodeAuditTask/queryTaskList',
        method: 'post',
        data: {}
      })

      if (res.code == 200) {
        this.$message.success(res.msg)
        this.$refs.sheetRef.getTableData()
      }
    },
    async onQuerySuccess(ctx) {
      this.searchParams = ctx.parameter()

      this.$emit('query-change', this.searchParams)

      await this.$nextTick()
      if (this.$refs.statsRef) {
        await this.$refs.statsRef.load()
      }
    },
    onFormClosed() {
      this.activeDetectTool = this.$options.data().activeDetectTool
    }
  }
}
</script>

<style></style>
