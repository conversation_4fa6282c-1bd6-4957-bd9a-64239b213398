<template>
  <EleSheet ref="sheetRef" v-bind="sheetProps" class="page-main page-main--flat !h-auto">
    <template #before> </template>

    <template #toolbar:after="{ selected, selectionIds, selection }">
      <el-button
        v-hasPermi="['risk:middle:sourceCode:loophole:dispose']"
        type="primary"
        :disabled="!selected"
        @click="onDisposeClick(selectionIds, selection)"
      ><IconHandyman class="" />处置</el-button>

      <el-button
        v-if="$checkPermi(['risk:middle:sourceCode:loophole:assign'])"
        type="warning"
        icon="iconfont icon-piliangfenpei1"
        :disabled="!selected"
        @click="onAssignClick(selectionIds)"
      >分配</el-button>

    </template>

    <template #table:action:after="{ row }">
      <el-button v-if="$checkPermi(['risk:middle:sourceCode:loophole:dispose'])" type="text" size="mini" @click="onDisposeClick([row.id], [row])">处置</el-button>
    </template>

    <!-- <template #info:after="{ data }">
      <EleTitle class="pb-4">详细信息</EleTitle>
      <el-table class="sa-table" :data="data.traceBlockList ? JSON.parse(data.traceBlockList) : []">
        <el-table-column type="expand">
          <template #default="{ row }">
            <div class="px-4">
              <el-descriptions v-for="(item, index) of row.traceLine" :key="index" title="">
                <el-descriptions-item label="追踪行数">{{ item.line }}</el-descriptions-item>
                <el-descriptions-item label="缺陷迫总描活信息">text</el-descriptions-item>
              </el-descriptions>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="缺陷追踪文件名" prop="file" align="left" show-overflow-tooltip />
        <el-table-column label="缺陷方法名" prop="method" align="center" show-overflow-tooltip />
        <el-table-column label="缺陷结构区块分号" prop="serialNumId" align="center" show-overflow-tooltip />
      </el-table>
    </template> -->

    <template #after>
      <AssignDialog ref="assignDialogRef" />
      <DisposeDialog ref="disposeDialogRef" />
    </template>
  </EleSheet>
</template>

<script>
import {
  addSourceCodeAudit,
  delSourceCodeAudit,
  getSourceCodeAudit,
  listSourceCodeAudit,
  updateSourceCodeAudit
} from '@/api/risk/resource/sourceDetail.js'
import DisposeDialog from '@/views/risk/basic/components/DisposeDialog/index.vue'
import AssignDialog from '@/views/risk/basic/components/AssignDialog/index.vue'

import { middleSourceCodeType } from '@/dicts/basic/index.js'

import request from '@/utils/request.js'

export default {
  components: {
    DisposeDialog,
    AssignDialog
  },
  props: {
    params: {
      type: Object,
      default: () => ({})
    }
  },
  dicts: ['problem_severity_level', 'allocation_status', 'dispose_status_source_code', 'loophole_risk_level'],
  data() {
    return {
      riskType: middleSourceCodeType
    }
  },
  computed: {
    sheetProps() {
      const value = {
        title: '源代码审计明细',

        lazy: false,

        api: {
          // add: (params) => addSourceCodeAudit({ ...params }),
          // edit: (params) => updateSourceCodeAudit({ ...params }),
          list: async(params) => listSourceCodeAudit({ ...params }),
          info: getSourceCodeAudit,
          remove: delSourceCodeAudit,
          export: '/risk/sourceCodeAuditProblemDetail/export',
          import: '',
          template: ''
        },

        infoProps: {
          title: true
        },

        flowProps: {
          preset: 'disposal',
          params: {
            riskType: this.riskType
          }
        },

        model: {
          taskId: {
            hidden: true,
            search: {
              value: this.params.taskId
            }
          },
          problemId: {
            label: 'CVE编号',
            table: {
              align: 'left',
              width: 150
            }
          },
          filePath: {
            label: '漏洞类型',
            table: {
              width: 150
            },
            search: {
              hidden: false
            },
            form: {
              rules: true
            }
          },
          problemFunction: {
            label: '漏洞标题',
            table: {
              width: 120
            },
            search: {
              hidden: false
            },
            form: {
              rules: true
            }
          },

          problemLine: {
            type: 'select',
            label: '风险等级',
            table: {
            },
            search: {
              hidden: false
            },
            form: {
              rules: true
            },
            options: this.dict.type.loophole_risk_level
          },
          problemCol: {
            label: '影响分数',
            table: {
            },
            search: {
              hidden: true
            },
            form: {
              rules: true
            }
          },
          errorCode: {
            type: 'text',
            label: '检测目的',
            table: {
              width: 150
            },
            search: {
              hidden: true
            },
            form: {
              rules: true
            },
            group: 'detail'
          },
          problemMessage: {
            label: '漏洞描述',
            table: {
              width: 180
            },
            search: {
              hidden: true
            },
            form: {
              rules: true
            }
          },
          severityLevel: {
            label: '缺陷级别',
            table: {
              width: 120
            },
            search: {
              hidden: true
            },
            form: {
              rules: true
            }
          },
          cnname: {
            label: '漏洞参考链接名称',
            table: {
              width: 150
            },
            search: {
              hidden: true
            },
            form: {
              rules: true
            }
          },

          traceBlockList: {
            label: '漏洞参考URL',
            table: {
              hidden: true
            },
            search: {
              hidden: true
            },
            form: {
              rules: false
            }
          },

          disposeStatus: {
            label: '处置状态',
            type: 'select',
            options: this.dict.type.dispose_status_source_code
          },
          dispatchStatus: {
            label: '分配状态',
            type: 'select',
            group: 'flow',
            options: this.dict.type.allocation_status
          },
          riskOperatorGroupName: {
            label: '处置分组',
            group: 'flow'
          }
        }
      }

      return value
    }
  },
  methods: {
    getTableData() {
      this.$refs.sheetRef.getTableData()
    },
    onAssignClick(ids, args = {}) {
      const params = {
        ids,
        riskType: this.riskType,
        groupKey: 'basic_type_middle_dim',
        disposeType: '源代码审计'
      }

      this.$refs.assignDialogRef.open({
        ...args,
        params,
        success: () => {
          this.getTableData()
        }
      })
    },
    onDisposeClick(ids, selection) {
      let params = {}

      if (Array.isArray(ids)) {
        params = {
          ids,
          selection
        }
      } else {
        params = {
          id: ids[0]
        }
      }

      Object.assign(params, {
        riskType: this.riskType
      })

      this.$refs.disposeDialogRef.open({
        params,
        success: () => {
          this.getTableData()
        }
      })
    }
  }
}
</script>

<style></style>
