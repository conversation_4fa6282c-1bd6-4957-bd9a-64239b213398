<template>
  <div class="h-full" :class="['detail', 'allocation', 'basicOverview'].includes(sourceType) ? 'page-main' : ''">
    <StatsPanel
      v-if="sourceType == 'index' && lazyQueryParams"
      ref="statsPanelRef"
      :key="JSON.stringify(lazyQueryParams)"
      :params="{
        type: '4',
        queryType: 3,
        ...lazyQueryParams,
      }"
      class="!overflow-hidden !transition-all"
      :class="statsFlag ? '!h-[96px]' : '!h-0'"
    />

    <el-form
      v-show="showSearch"
      ref="queryForm"
      class="sa-query"
      :model="queryParams"
      size="small"
      :inline="true"
      label-width="68px"
    >
      <!-- <el-form-item class="" label="数据类别" prop="dataSourceType">
          <DictSelect v-model="queryParams.dataSourceType" dict-type="tenantDataType" />
        </el-form-item>
        <el-form-item class="" label="所属租户" prop="tenId">
          <TenantSelect
            ref="tenantSelectRef"
            v-model="queryParams.tenId"
            @change="handleQuery"
          />
        </el-form-item> -->

      <el-form-item class="task-name" label="任务名称" prop="taskName">
        <el-input
          v-model="queryParams.taskName"
          placeholder="请输入任务名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="部门名称" prop="deptName">
        <CommonDepartmentSelect
          v-model="queryParams.deptName"
          placeholder="请输入"
          clearable
          return-name
          @change="()=> queryParams.systemName = void 0"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="业务系统" prop="systemName">
        <CommonSystemSelect
          v-model="queryParams.systemName"
          placeholder="请输入"
          return-name
          clearable
          :params="{
            deptName: queryParams.deptName
          }"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <!-- <el-form-item label="业务系统" prop="systemName">
        <SystemWebSelect v-model="queryParams.systemName" />
      </el-form-item> -->

      <!-- <el-form-item class="task-type" label="任务类型" prop="taskType">
        <el-select
          v-model="queryParams.taskType"
          placeholder="请选择任务类型"
          clearable
          @change="handleQuery"
        >
          <el-option
            v-for="dict in dict.type.main_task_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item> -->
      <!-- <div class="sa-query-item sa-flex sa-flex-wrap" :class="searchMore ? 'sa-query-item1' : 'sa-query-item2'"> -->
      <el-form-item class="repeat-flag" label="复现标记" prop="repeatFlag">
        <el-select
          v-model="queryParams.repeatFlag"
          placeholder="请选择重复异常"
          clearable
          @change="handleQuery"
        >
          <el-option
            v-for="dict in dict.type.risk_repeatFlag"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="超期标记" prop="overdueStatus">
        <EleSelectDict v-model="queryParams.overdueStatus" dict-type="whether" @change="handleQuery" />
      </el-form-item>

      <el-form-item label="处置状态" prop="statusList">
        <el-select
          v-model="queryParams.statusList"
          multiple
          collapse-tags
          placeholder="请选择处置状态"
          clearable
          @change="handleQuery"
        >
          <el-option
            v-for="dict in dict.type.risk_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item
        v-if="(['report'].includes(sourceType) && ['detail', 'allocation'].includes(operType)) || ['index', 'basicOverview'].includes(sourceType)"
        label="分配状态"
        prop="dispatchStatus"
      >
        <el-select
          v-model="queryParams.dispatchStatus"
          placeholder="请选择分配状态"
          clearable
          filterable
          @change="handleQuery"
        >
          <el-option
            v-for="dict in dict.type.allocation_status_web"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="漏洞名称" prop="leakName">
        <div class="prompt">
          <el-input
            slot="reference"
            v-model="queryParams.leakName"
            placeholder="请输入漏洞名称"
            clearable
            @blur="onBlurLeak"
            @input="onChangeLeak"
            @keyup.enter.native="handleQuery"
          />
          <div v-if="leakData.length > 0 && isLeak" class="prompt-box">
            <div class="prompt-box-sanjiao">
              <div class="sanjiao" />
            </div>
            <div class="prompt-box-content" :class="{ 'ovflow-y': leakData.length > 6 }">
              <div
                v-for="(item, index) in leakData"
                :key="index"
                class="input-msg-item"
                @click="onLeakMsg(item)"
              >
                {{ item }}
              </div>
            </div>
          </div>
        </div>
      </el-form-item>
      <el-form-item label="是否为互联网暴露面" prop="internetExpose">
        <el-select
          v-model="queryParams.internetExpose"
          placeholder="请选择"
          clearable
        >
          <el-option
            v-for="dict in dict.type.risk_repeatFlag"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="风险等级" prop="riskLevel">
        <el-select
          v-model="queryParams.riskLevel"
          placeholder="请选择风险等级"
          clearable
          @change="handleQuery"
        >
          <el-option
            v-for="dict in dict.type.risk_level_web"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="复测状态" prop="retestStatus">
        <el-select
          v-model="queryParams.retestStatus"
          placeholder="请选择复测状态"
          clearable
          @change="handleQuery"
        >
          <template v-for="dict in dict.type.risk_basic_web_retest_status">
            <el-option
              :label="dict.label"
              :value="dict.value"
            />
          </template>
        </el-select>
      </el-form-item>

      <el-form-item
        v-if="['index', 'basicOverview'].includes(sourceType) || (['report'].includes(sourceType) && operType == 'detail')"
        label="处置分组"
        prop="riskOperatorGroupId"
      >
        <el-select
          v-model="queryParams.riskOperatorGroupId"
          placeholder="请选择处置分组"
          clearable
          filterable
          multiple
        >
          <el-option
            v-for="item in riskOperatorGroupIdList"
            :key="item.id"
            :label="item.groupName"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item class="daterange" label="创建时间" prop="daterange">
        <el-date-picker
          v-model="daterange"
          type="daterange"
          value-format="yyyy-MM-dd"
          format="yyyy-MM-dd"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="onChangeDaterange"
        />
      </el-form-item>

      <el-form-item label="资源IP/域名" prop="resourceIp" class="ip-item">
        <el-input
          v-model="queryParams.resourceIp"
          placeholder="资源IP/域名(多IP请用&quot;,&quot;分开)"
          clearable
          @input="inputHandler"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="IP区间" class="ip-item">
        <ip-input ref="ipRef" @startIp="getStartIp" @endIp="getEndIp" />
      </el-form-item>

      <!-- </div> -->
      <el-form-item class="query-handle">
        <el-button type="primary" plain icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh-left" @click="resetQuery">重置</el-button>
        <EleSheetSearchMore :parent-ref="()=> $refs.queryForm" />
      </el-form-item>
    </el-form>

    <div class="sa-title flex items-center">
      <div class="space-x-2 flex-1 w-0">

        <template v-if="taskDetail.riskOperatorGroupId != 0">
          <template v-if="sourceType != 'check'">
            <el-button
              v-if="taskDetail.taskType != 2 && ['report'].includes(sourceType) && operType == 'detail'"
              v-hasPermi="['risk:basic:web:add']"
              type="primary"
              icon="el-icon-plus"
              @click="handleAdd"
            >新增
            </el-button>
            <el-button
              v-if="source.exportUrl.includes('export')"
              v-hasPermi="['risk:basic:web:export']"
              type="primary"
              icon="el-icon-download"
              @click="handleExport"
            >导出
            </el-button>
            <el-button
              v-if="$checkPermi(['risk:basic:web:infiltration:export'])"
              :loading="buttonLoading"
              type="primary"
              icon="el-icon-download"
              :disabled="multiple"
              @click="onExportWord"
            >批量导出报告
            </el-button>
            <el-button
              v-if="source.exportUrl.includes('adminAndTenantWebListExport')"
              v-hasPermi="['risk:basic:web:adminAndTenantWebListExport']"
              type="primary"
              icon="el-icon-download"
              @click="handleExport"
            >导出
            </el-button>

            <el-button
              v-if="layout.includes('approve')"
              type="success"
              icon="el-icon-s-check"
              :disabled="multiple"
              @click="handleApprove()"
            >审批</el-button>

            <el-button
              v-if="taskDetail.taskType != 2 && layout.includes('all')"
              v-hasPermi="['risk:warn:web:dispose']"
              type="primary"
              :disabled="multiple"
              @click="onOper"
            ><IconHandyman class="" />处置
            </el-button>
          <!-- 暂时没加权限 -->
          <!-- <el-button
              v-if="!['index'].includes(sourceType) && taskDetail.taskType != 2"
              type="danger"
              plain
              :disabled="multiple"
              @click="handleDelete"
              v-hasPermi="['risk:basic:web:remove']"
              ><i class="iconfont icon-piliangshanchu"></i>批量删除</el-button
            > -->
          </template>
        </template>

        <template v-if="taskDetail.riskOperatorGroupId == 0 && layout.includes('all')" class="">
          <el-popover v-if="$checkPermi(['risk:task:security:issue:batch:distribution'])" placement="top-start" trigger="hover" content="列表所选中的进行分配">
            <el-button
              slot="reference"
              type="warning"
              :disabled="ids.length == 0"
              @click="onAllocationDetail(2)"
            ><i class="iconfont icon-piliangfenpei1" />批量分配
            </el-button>
          </el-popover>
          <el-popover v-if="$checkPermi(['risk:task:security:issue:full:allocation']) && ['report'].includes(sourceType)" placement="top-start" trigger="hover" content="当前条件查询的进行分配">
            <el-button
              slot="reference"
              class=""
              type="warning"
              :disabled="webList.length == 0"
              @click="onAllocationDetail(1)"
            ><i class="iconfont icon-piliangfenpei" />全量分配
            </el-button>
          </el-popover>
        </template>
        <template v-else-if="layout.includes('all')">
          <el-popover v-if="$checkPermi(['risk:basic:web:batch:distribution'])" placement="top-start" trigger="hover" content="列表所选中的进行分配">
            <el-button
              slot="reference"
              type="warning"
              :disabled="ids.length == 0"
              @click="onAllocationDetail(2)"
            ><i class="iconfont icon-piliangfenpei1" />批量分配
            </el-button>
          </el-popover>
          <el-popover v-if="$checkPermi(['risk:basic:web:full:allocation']) && ['report'].includes(sourceType)" placement="top-start" trigger="hover" content="当前条件查询的进行分配">
            <el-button
              slot="reference"
              class=""
              type="warning"
              :disabled="webList.length == 0"
              @click="onAllocationDetail(1)"
            ><i class="iconfont icon-piliangfenpei" />全量分配
            </el-button>
          </el-popover>
        </template>

        <el-button v-if="taskDetail.riskOperatorGroupId != 0 && $checkPermi(['risk:turnoverRecord:web:goBackRiskData']) && layout.includes('all')" type="warning" :disabled="multiple" @click="goBackRiskData"><icon-undo class="el-icon" />退回
        </el-button>
        <template v-if="['index', 'basicOverview'].includes(sourceType) && layout.includes('all') && $checkPermi(['risk:turnoverRecord:web:transferRiskData'])">
          <el-button
            type="warning"
            :disabled="multiple"
            @click="onAllocationDetail(2, '', '转派')"
          ><IconForward class="" />转派
          </el-button>
        </template>

        <div v-if="ids.length > 0" class="change-input inline-block">共选中 {{ ids.length }} 项</div>
      </div>

      <sa-toolbar :show-search.sync="showSearch" class="flex-none" @queryTable="getList" />
    </div>

    <div v-full:height="autoHeight" class="">
      <el-table
        ref="multipleTableRef"
        v-loading="loading"
        class="sa-table"
        v-bind="{
          ...(autoHeight ? {
            height:'100%'
          }:{})
        }"
        :data="webList"
        @select="selectRow"
        @select-all="selectAll"
        @selection-change="onSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center" :selectable="selectable" />

        <!--
      <el-table-column label="任务类型" align="center" v-if="taskType">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.main_task_type" :value="scope.row.taskType" />
        </template>
      </el-table-column>-->
        <el-table-column label="任务名称" align="center" prop="taskName" min-width="120" :show-overflow-tooltip="true" />

        <el-table-column v-if="layout.includes('approve')" label="修复凭证" align="center">
          <template #default="{ row }">
            <el-link type="primary" @click="$openURL(row.disposalVoucher)"> {{ row.disposalVoucherName }} </el-link>
          </template>
        </el-table-column>

        <el-table-column label="处置状态" align="center">
          <template slot-scope="scope">
            <dict-tag :options="dict.type.risk_status" :value="scope.row.status" />
          </template>
        </el-table-column>

        <el-table-column label="复测状态" align="center" width="140">
          <template slot-scope="scope">
            <dict-tag :options="dict.type.risk_basic_web_retest_status" :value="scope.row.retestStatus" />
          </template>
        </el-table-column>
        <el-table-column
          v-if="(['report'].includes(sourceType) && operType == 'detail') || ['index', 'basicOverview'].includes(sourceType)"
          label="分配状态"
          align="center"
        >
          <template slot-scope="scope">
            <dict-tag :options="dict.type.allocation_status_web" :value="scope.row.dispatchStatus" />
          </template>
        </el-table-column>

        <el-table-column label="部门名称" align="center" prop="deptName" width="120" :show-overflow-tooltip="true" />
        <el-table-column label="业务系统" align="center" prop="systemName" width="200" :show-overflow-tooltip="true" />
        <el-table-column label="漏洞名称" align="center" prop="leakName" :show-overflow-tooltip="true" />
        <el-table-column label="是否为互联网暴露面" align="center" width="150">
          <template #default="scope">
            <dict-tag :options="dict.type.risk_repeatFlag" :value="scope.row.internetExpose" />
          </template>
        </el-table-column>
        <el-table-column label="风险等级" align="center" width="140">
          <template slot-scope="scope">
            <dict-tag :options="dict.type.risk_level_web" :value="scope.row.riskLevel" />
          </template>
        </el-table-column>

        <el-table-column label="资源IP/域名" align="center" width="180">
          <template slot-scope="scope">
            <sa-tooltip :content="scope.row.resourceIp" :is-copy="true" />
          </template>
        </el-table-column>

        <el-table-column label="复现标记" align="center" prop="repeatFlag" width="120">
          <template slot-scope="scope">
            <dict-tag :options="dict.type.risk_repeatFlag" :value="scope.row.repeatFlag" />
          </template>
        </el-table-column>

        <el-table-column label="超期标记" align="center" width="120">
          <template #default="{ row }">
            <EleTagDict :value="row.overdueStatus" dict-type="whether" />
          </template>
        </el-table-column>

        <el-table-column label="超期天数" align="center" width="120" prop="overdueDays">
        </el-table-column>

        <el-table-column label="URL" align="center" width="180">
          <template slot-scope="scope">
            <sa-tooltip :content="scope.row.url" />
          </template>
        </el-table-column>

        <el-table-column
          label="测试报告编号"
          align="center"
          prop="testReportNum"
          width="120"
          :show-overflow-tooltip="true"
        />

        <!-- <el-table-column label="数据类别" align="center" width="100" prop="dataSourceType">
          <template #default="{ row }">
            <DictTagV2 :value="row.dataSourceType" dict-type="tenantDataType" />
          </template>
        </el-table-column>
        <el-table-column label="所属租户" align="center" width="150" prop="tenName" show-overflow-tooltip /> -->

        <!-- <el-table-column label="测试过程" align="center" prop="dataPacketScreenshots" width="120" :show-overflow-tooltip="true" /> -->
        <el-table-column
          v-if="['index', 'basicOverview'].includes(sourceType) || (['report'].includes(sourceType) && operType == 'detail')"
          label="处置分组"
          align="center"
          prop="groupName"
          :show-overflow-tooltip="true"
        >
          <template slot-scope="scope">
            {{ scope.row.dispatchStatus == 1 ? '-' : scope.row.groupName }}
          </template>
        </el-table-column>

        <el-table-column label="审批状态" align="center">
          <template #default="{ row }">
            <dict-tag :options="dict.type.risk_approval_status" :value="row.approvalStatus" />
          </template>
        </el-table-column>

        <el-table-column label="处置时间" align="center" prop="disposeTime" width="200" />

        <el-table-column label="分配时间" align="center" prop="dispatchTime" width="200" />

        <el-table-column label="处置维度" align="center" width="100">
          <template #default="{ row }">
            <EleTagDict :options="dict.type.basic_type_base_dim" :value="row.disposeType" />
          </template>
        </el-table-column>
        <el-table-column label="规则编号" prop="ruleNo" align="center" width="100" show-overflow-tooltip>
        </el-table-column>

        <el-table-column label="创建时间" align="center" prop="createTime" width="200" />
        <el-table-column fixed="right" label="操作" align="center" width="220">
          <template slot-scope="scope">
            <el-button v-if="$checkPermi(['risk:basic:web:query'])" size="mini" type="text" @click="onDetail(scope.row)">查看</el-button>

            <el-button
              v-if="layout.includes('approve')"
              type="text"
              @click="handleApprove(scope.row)"
            >审批</el-button>

            <template v-if="sourceType != 'check' && ![3, 4].includes(Number(scope.row.dataSourceType)) && layout.includes('all')">
              <el-button
                v-if="taskDetail.riskOperatorGroupId == 0"
                size="mini"
                type="text"
                @click="onAllocationDetail(2, scope.row)"
              >分配
              </el-button>
              <el-button
                v-if="showHistory || scope.row.repeatFlag == 1 && checkPermi(['risk:basic:web:view:history'])"
                v-hasPermi="['risk:basic:web:view:history']"
                size="mini"
                type="text"
                @click="onHistoryDetail(scope.row)"
              >查看历史
              </el-button>

              <!-- <el-button size="mini" type="text" @click="onExportWord(scope.row)"
                >导出报告</el-button
              > -->
              <el-button
                v-if="!['index'].includes(sourceType) && layout.includes('all')"
                v-hasPermi="['risk:basic:web:remove', 'risk:basic:web:del']"
                size="mini"
                type="text"
                @click="handleDelete(scope.row)"
              >删除
              </el-button>
              <template v-if="taskDetail.riskOperatorGroupId != 0 && layout.includes('all')">
                <template v-if="scope.row.status != 2">
                  <el-button
                    v-hasPermi="['risk:warn:web:dispose']"
                    size="mini"
                    type="text"
                    @click="onOper(scope.row)"
                  >处置
                  </el-button>
                  <template v-if="['index', 'basicOverview'].includes(sourceType)">
                    <el-button
                      v-if="$checkPermi(['risk:turnoverRecord:web:goBackRiskData'])"
                      size="mini"
                      type="text"
                      @click="goBackRiskData(scope.row)"
                    >退回
                    </el-button>
                    <el-button
                      v-if="$checkPermi(['risk:turnoverRecord:web:transferRiskData'])"
                      size="mini"
                      type="text"
                      @click="onAllocationDetail(2, scope.row, '转派')"
                    >转派
                    </el-button>
                  </template>
                </template>
              </template>
            </template>
            <el-button
              v-if="sourceType == 'report' && taskDetail.riskOperatorGroupId != 0 && scope.row.status == 2 && scope.row.retestStatus != 2 && layout.includes('all')"
              v-hasPermi="['risk:basic:web:retest']"
              size="mini"
              type="text"
              @click="onOpenRetest(scope.row)"
            >复测
            </el-button>
            <el-button
              v-if="scope.row.retestStatus == 2"
              v-hasPermi="['risk:warn:web:retest:record']"
              size="mini"
              type="text"
              @click="onOpenResMsgList(scope.row)"
            >复测记录
            </el-button>
            <el-button
              v-if="layout.includes('all')"
              v-hasPermi="['risk:basic:web:edit']"
              size="mini"
              type="text"
              @click="handleUpdate(scope.row)"
            >修改
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="sa-footer sa-row-center">

      <sa-pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </div>

    <!-- 详情 -->
    <sa-detail ref="detail" :detail-data="detail" />

    <!-- 处置 -->
    <oper-detail ref="oper" :oper-data="oper" :risk-status="[{label:'未修复',value:'1'}, {label:'已修复',value:'2'}]" @ok="onOperDetail" />

    <!-- 导出报告预览 -->
    <el-dialog
      class="preview-dialog"
      title="预览报告"
      :visible.sync="openWord"
      width="800px"
      append-to-body
      @close="onCloseWord"
    >
      <div class="docx-btn-box">
        <el-button type="primary" @click="onDownWord(htmlData.url, htmlData.fileName)">下载</el-button>
      </div>
      <!-- <vue-office-docx :src="docx" style="height: 600px" @rendered="rendered" /> -->
      <!-- <vue-office-pdf :src="pdf" style="height: 600px" @rendered="rendered" /> -->
      <div class="preview-dialog-content">
        <div class="title sa-m-b-20">{{ htmlData.title }}</div>
        <div class="subtitle sa-m-b-20 text-center">{{ htmlData.title1 }}</div>

        <div class="subtitle sa-m-b-20">{{ htmlData.subtitle1 }}</div>
        <div class="content indent sa-m-b-20">
          <div>{{ htmlData.firstParagraphContent1 }}</div>

          <div class="pt-4">
            <el-table class="el-table-simple" :data="[htmlData.firstParagraphContent2]" size="mini">
              <el-table-column align="center" label="序号" type="index" />
              <el-table-column align="center" label="系统名称" prop="systemName" />
              <el-table-column align="center" label="URL地址" prop="url" />
              <el-table-column align="center" label="测试时间" prop="findTime" :formatter="formatterTime" />
              <el-table-column align="center" label="测试IP地址" prop="resourceIp" />
              <el-table-column align="center" label="测试人员" prop="findPeople" />
            </el-table>
          </div>
        </div>

        <div class="subtitle sa-m-b-20">{{ htmlData.subtitle2 }}</div>
        <div class="content sa-m-b-20">
          <div class="sa-m-b-10">{{ htmlData.secondParagraphContent1 }}</div>
          <div class="sa-flex sa-row-center sa-m-b-10">
            <table cellspacing="0" border="1px" class="!bg-[#8AB3E0] !text-black">
              <thead>
                <tr class="">
                  <th colspan="3" align="center" class="">渗透安全检查</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="(item, index) in htmlData.tableData1">
                  <td
                    v-if="index === 0"
                    :rowspan="htmlData.tableData1.length"
                    align="center"
                  >应用安全评估
                  </td>
                  <td align="center">{{ item.name }}</td>
                  <td align="center">{{ item.num }}</td>
                </tr>
              </tbody>
            </table>
          </div>

          <div class="sa-m-b-10">{{ htmlData.secondParagraphContent2 }}</div>
          <div class="sa-flex sa-row-center">
            <table cellspacing="0" border="1px">
              <tr v-for="(item, index) in htmlData.tableData2" :class="index === 0 ? '!bg-[#8AB3E0] !text-black':''">
                <td align="center">{{ item.num }}</td>
                <td align="center">{{ item.name }}</td>
                <td align="center">{{ item.desc }}</td>
                <td align="center">{{ item.status }}</td>
              </tr>
            </table>
          </div>
        </div>

        <div class="subtitle2_1 sa-m-b-20">{{ htmlData.subtitle2_1 }}</div>
        <div class="content sa-m-b-20">
          <div class="sa-m-b-10 indent-xl">{{ htmlData.subtitle2_1_1 }}</div>
          <div class="">
            <PenetrationTestTable :data="htmlData.subtitle2_1List" previewed />
          </div>
        </div>

        <div class="subtitle2_1 sa-m-b-20">{{ htmlData.subtitle2_2 }}</div>
        <div v-for="(item, index) in htmlData.descContent">
          <div class="subtitle2_1_ sa-m-b-10"> 2.2.{{
            index + 1
          }}.{{ item.deptName }}-{{ item.systemName }}-{{ item.desc }}
          </div>
          <div>
            <div class="key sa-m-b-16">问题描述:</div>
            <div class="keyValue sa-m-b-20">{{ item.questionDesc }}</div>
          </div>
          <div>
            <div class="key sa-m-b-16">测试url:</div>
            <div class="keyValue testUrl sa-m-b-20">{{ item.testUrl }}</div>
          </div>
          <div>
            <div class="key sa-m-b-16">测试过程:</div>
            <div class="keyValue sa-m-b-20" v-html="item.dataPacketScreenshots" />
          </div>
          <div>
            <div class="key sa-m-b-16">数据包截图及说明:</div>
            <div class="keyValue sa-m-b-20" v-html="item.testProcess" />
          </div>
          <div>
            <div class="key sa-m-b-16">风险程度:</div>
            <div
              class="keyValue sa-m-b-20"
              :class="item.riskLevel == '高危' ? 'gao' : item.riskLevel == '中危' ? 'zhong' : ''"
            >{{ item.riskLevel }}
            </div>
          </div>
          <div>
            <div class="key sa-m-b-16">对应漏洞清单版本及漏洞序号:</div>
            <div class="keyValue sa-m-b-20">{{ item.leakVersion }}</div>
          </div>
          <div>
            <div class="key sa-m-b-16">风险分析:</div>
            <div class="keyValue sa-m-b-20">{{ item.riskAnalysis }}</div>
          </div>
          <div>
            <div class="key sa-m-b-16">整体建议:</div>
            <div class="keyValue sa-m-b-20">{{ item.recommend }}</div>
          </div>
          <div v-if="item.riskRetest">
            <div class="key sa-m-b-16">漏洞复测:</div>
            <div class="keyValue sa-m-b-20">
              <div v-html="item.riskRetest" />
            </div>
          </div>
        </div>
        <!-- <div v-for="(value, key) in htmlData.descNameMap">
          <div class="key sa-m-b-16">{{ key }}:</div>
          <div class="keyValue sa-m-b-20">{{ htmlData.descContent[value] }}</div>
        </div> -->
      </div>
    </el-dialog>

    <history-index
      ref="historyRef"
      :source="source"
      :task-detail="taskDetail"
      :task-type="taskType"
      :basic-type="basicType"
      @close="onClose"
    />

    <!-- 新增或修改应用漏洞对话框 -->
    <el-dialog
      class="add-edit-dialog"
      :title="title"
      :visible.sync="open"
      width="800px"
      append-to-body
    >
      <el-form ref="form" class="app-loophole-form" :model="form" :rules="rules" label-width="auto" inline>
        <el-form-item v-if="!['report'].includes(sourceType) && type !== 'edit'" label="任务" prop="taskId">
          <el-select
            ref="taskIdRef"
            v-model="form.taskId"
            placeholder="请选择任务"
            filterable
            remote
            reserve-keyword
            :remote-method="remoteMethod"
            @change="onChangeTaskId"
          >
            <el-option
              v-for="dict in taskIdList"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item v-if="!['report'].includes(sourceType) && type === 'edit'" label="任务名称" prop="taskName">
          <el-input v-model="form.taskName" disabled placeholder="请输入任务名称" />
        </el-form-item>
        <el-form-item label="部门名称" prop="deptName">
          <CommonDepartmentSelect
            v-model="form.deptName"
            clearable
            return-name
            @change="()=> form.systemName = void 0"
          />
        </el-form-item>
        <el-form-item label="业务系统" prop="systemName">
          <CommonSystemSelect
            v-model="form.systemName"
            return-name
            clearable
            :params="{
              deptName: form.deptName
            }"
          />
        </el-form-item>
        <el-form-item label="资源IP/域名" prop="resourceIp">
          <div style="display: flex;">
            <el-input v-model="form.resourceIp" style="width: auto;" placeholder="请输入资源IP/域名" />
            <span style="font-size: 12px;color: #ff0000;margin-left: 10px">示例：************或xx.10086.cn</span>
          </div>
        </el-form-item>
        <el-form-item label="URL" prop="url">
          <el-input v-model="form.url" placeholder="请输入URL" />
        </el-form-item>
        <el-form-item label="是否为互联网暴露面" prop="internetExpose">
          <el-radio-group v-model="form.internetExpose" style="width: 200px">
            <el-radio v-for="dict in dict.type.risk_repeatFlag" :key="dict.value" :label="dict.value">
              {{ dict.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="漏洞名称" prop="leakName">
          <el-input v-model="form.leakName" placeholder="请输入漏洞名称" />
        </el-form-item>
        <el-form-item label="风险等级" prop="riskLevel">
          <!--          <el-input v-model="form.riskLevel" placeholder="请选择风险等级" />-->
          <el-select
            v-model="form.riskLevel"
            placeholder="请选择风险等级"
            clearable
          >
            <el-option
              v-for="dict in dict.type.risk_level_web"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="是否登录测试" prop="login">
          <el-radio-group v-model="form.login">
            <el-radio v-for="dict in dict.type.risk_repeatFlag" :key="dict.value" :label="dict.value">
              {{ dict.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="测试报告编号" prop="testReportNum">
          <el-input v-model="form.testReportNum" placeholder="请输入测试报告编号" />
        </el-form-item>
        <el-form-item label="发现人" prop="findPeople">
          <el-input v-model="form.findPeople" placeholder="请输入发现人" />
        </el-form-item>
        <el-form-item label="测试时间" prop="findTime">
          <!--          <el-input v-model="form.findTime" placeholder="请输入测试时间" />-->
          <el-date-picker
            v-model="form.findTime"
            clearable
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择时间"
          />
        </el-form-item>
        <el-form-item label="对应漏洞清单版本及漏洞序号" prop="leakVersion">
          <el-input v-model="form.leakVersion" placeholder="请输入对应漏洞清单版本及漏洞序号" />
        </el-form-item>
        <el-form-item class="sa-w-full" label="问题描述" prop="questionDesc">
          <el-input
            v-model="form.questionDesc"
            type="textarea"
            :rows="2"
            placeholder="请输入问题描述"
          />
        </el-form-item>
        <el-form-item label="测试url" prop="testUrl">
          <el-input v-model="form.testUrl" placeholder="请输入测试url" />
        </el-form-item>
        <el-form-item class="sa-w-full" label="整改建议" prop="recommend">
          <el-input
            v-model="form.recommend"
            type="textarea"
            :rows="2"
            placeholder="请输入整改建议"
          />
        </el-form-item>
        <el-form-item class="sa-w-full" label="测试过程" prop="dataPacketScreenshots">
          <el-input
            v-model="form.dataPacketScreenshots"
            type="textarea"
            :rows="2"
            placeholder="请输入测试过程"
          />
        </el-form-item>
        <el-form-item class="sa-w-full" label="数据包截图及说明" prop="testProcess">
          <editor v-model="form.testProcess" :min-height="192" />
        </el-form-item>
        <el-form-item class="sa-w-full" label="风险分析" prop="riskAnalysis">
          <el-input
            v-model="form.riskAnalysis"
            type="textarea"
            :rows="2"
            placeholder="请输入风险分析"
          />
        </el-form-item>

        <el-form-item v-if="form.testingItemList && form.testingItemList.length" class="sa-w-full" label="">
          <template #label>
            <div class="whitespace-nowrap">
              测试内容
            </div>
          </template>
          <PenetrationTestTable :data="form.testingItemList" />
        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
        <el-button type="primary" @click="submitForm">确 定</el-button>
      </div>
    </el-dialog>

    <allocation-detail
      ref="allocationDetailRef"
      :allocation-detail="allocationDetail"
      :select-other-tenant="!!taskDetail.taskId"
      @ok="onAllocation"
    />

    <el-dialog
      title="复测"
      :visible.sync="retestData.visible"
      width="800px"
      append-to-body
    >
      <el-form ref="retestFormRef" :model="retestData.form" :rules="retestData.rules" label-width="120px">
        <el-form-item label="复测过程" prop="testProcess">
          <editor v-model="retestData.form.testProcess" :min-height="192" />
        </el-form-item>
        <el-form-item label="复测结果" prop="status">
          <el-select
            v-model="retestData.form.status"
            placeholder="请选择复测结果"
            clearable
          >
            <template v-for="dict in dict.type.risk_basic_web_retest_status">
              <el-option
                v-if="dict.value != 1"
                :label="dict.label"
                :value="dict.value"
              />
            </template>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="retestData.visible = false">取 消</el-button>
        <el-button type="primary" @click="onConfirmRetest">确 定</el-button>
      </div>
    </el-dialog>

    <res-msg-list ref="resMsgListRef" :res-msg-list-data="resMsgListData" />

    <el-dialog
      title="批量导出报告确认"
      :visible.sync="exportRiskWordData.visible"
      width="800px"
      append-to-body
    >
      <el-alert
        class="sa-m-b-20"
        :title="exportRiskWordData.msg"
        type="warning"
      />
      <el-table
        class="sa-table"
        :data="exportRiskWordData.list"
      >
        <el-table-column label="报告编号" align="center" prop="testReportNum" min-width="120" />
        <el-table-column label="部门名称" align="center" prop="deptName" min-width="200" />
        <el-table-column label="业务系统" align="center" prop="systemName" min-width="200" />
        <el-table-column label="漏洞名称" align="center" prop="leakName" min-width="200" />
      </el-table>
      <div slot="footer" class="dialog-footer">
        <el-button @click="exportRiskWordData.visible = false">取 消</el-button>
        <el-button type="primary" @click="onConfirmExportRiskWord">确 定</el-button>
      </div>
    </el-dialog>

    <!--批量导出报告 flag为1时，弹出此dialog-->
    <el-dialog
      title="批量导出报告确认"
      :visible.sync="exportReportDialog.visible"
      width="800px"
      append-to-body
    >
      <el-alert
        class="sa-m-b-20"
        :title="exportReportDialog.msg"
        type="warning"
      />
      <el-table
        class="sa-table"
        :data="exportReportDialog.list"
      >
        <el-table-column label="报告编号" align="center" prop="reportNum" min-width="120" />
        <el-table-column label="部门名称" align="center" prop="deptName" min-width="200" />
        <el-table-column label="业务系统" align="center" prop="systemName" min-width="200" />
        <el-table-column label="是否为互联网暴露面" align="center" width="150">
          <template #default="scope">
            <dict-tag :options="dict.type.risk_repeatFlag" :value="scope.row.internetExpose" />
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center">
          <template #default="scope">
            <el-button size="mini" type="text" icon="el-icon-view" @click="handleExportReport('view', scope.row.ids)">
              查看
            </el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-download"
              @click="handleExportReport('download',scope.row.ids)"
            >下载
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <div slot="footer" class="dialog-footer">
        <el-button @click="exportReportDialog.visible = false">关 闭</el-button>
      </div>
    </el-dialog>

    <ApproveDialog ref="approveDialogRef" />

    <InfoDialog ref="infoDialogRef" />
  </div>
</template>

<script>
const currentDate = new Date()
import { listWeb, getWeb, delWeb, addWeb, updateWeb } from '@/api/risk/basic/web'
import { tansParams, blobValidate } from '@/utils/ruoyi'
import request from '@/utils/request'
import OperDetail from '../../components/oper.vue'
import AllocationDetail from '../AllocationDetail.vue'
import IpInput from '@/views/components/ipInput/ipInput.vue'
import HistoryIndex from './history-index.vue'
import { checkPermi, checkRole } from '@/utils/permission'

// 引入VueOfficePdf组件
import VueOfficePdf from '@vue-office/pdf'
// 引入相关样式
import '@vue-office/docx/lib/index.css'
import { updateGroupUser, removeMinioFile } from '@/api/risk/operator/groupUser'
import { minIOControllerDownload } from '@/api/risk/basic/data'

import ResMsgList from '@/views/res/msg/index.vue'

import PenetrationTestTable from './components/PenetrationTestTable/index.vue'
import dayjs from 'dayjs'

import ApproveDialog from '@/views/risk/basic/components/ApproveDialog/index.vue'

import StatsPanel from '@/views/risk/basic/components/StatsPanel/index.vue'

import InfoDialog from './components/InfoDialog/index.vue'
import { dataMixin } from '@/mixins'
import { cloneDeep } from 'lodash-es'

export default {
  name: 'Web',
  dicts: [
    'risk_status',
    'risk_level_web',
    'risk_repeatFlag',
    'dispose_dimension',
    'main_task_type',
    'risk_basic_web_retest_status',
    'allocation_status_web',
    'risk_approval_status',
    'basic_type_base_dim'
  ],
  mixins: [
    dataMixin({
      defaultTestingItemList: {
        default: [],
        async load() {
          const res = await request({
            url: '/risk/basic/web/listWebTestingItemInfo',
            method: 'get'
          })

          return res.data
        }
      }
    })
  ],
  components: {
    HistoryIndex,
    IpInput,
    OperDetail,
    AllocationDetail,
    VueOfficePdf,
    ResMsgList,
    PenetrationTestTable,
    ApproveDialog,
    StatsPanel,
    InfoDialog
  },
  props: {
    layout: {
      type: [Array, String],
      default: 'all'
    },
    sourceType: {
      type: String,
      default: 'index' // index|basicOverview|report|check
    },
    operType: {
      type: String,
      default: '' // allocation|detail
    },
    search: {
      type: Object,
      default: () => ({}) // 搜素条件
    },
    source: {
      type: Object,
      default: () => ({
        listUrl: `/risk/basic/web/list`,
        exportUrl: `/risk/basic/web/adminAndTenantWebListExport`
      })
    },
    taskDetail: {
      type: Object,
      default: () => ({
        riskOperatorGroupId: null
      })
    },
    taskType: {
      type: Boolean,
      default: true
    },
    showHistory: {
      type: Boolean,
      default: false
    },
    statsFlag: {
      type: Boolean,
      default: false
    },
    autoHeight: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      buttonLoading: false,
      yearData: [
        {
          label: currentDate.getFullYear()
        },
        {
          label: currentDate.getFullYear() - 1
        }
      ],
      yearExport: '2024',
      type: '', // 类别
      multipleLength: 0,
      isLeak: true,
      isSystem: true,
      // 提示输入框数据
      systemData: [],
      leakData: [],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      selectedList: [],
      // 选中业务系统
      systemName: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 选中的行
      selection: [],
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 应用漏洞表格数据
      webList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 是否显示预览
      openWord: false,
      // 文档地址
      docx: '',
      // pdf地址
      pdf: '',

      // 查询参数
      searchMore: false,
      lazyQueryParams: void 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        taskName: null,
        taskType: null,
        deptName: void 0,
        systemName: void 0,
        leakName: null,
        repeatFlag: null,
        riskLevel: null,
        status: null,
        resourceIp: null,
        startIp: null,
        endIp: null,
        retestStatus: null,
        riskOperatorGroupId: null,
        dispatchStatus: null,
        statusList: [],
        overdueStatus: void 0
      },
      daterange: [],
      // 表单参数
      form: {},
      removeMinioFileForm: {
        // 导出word文档
        fileName: '',
        bucketName: ''
      },
      // 表单校验
      rules: {
        taskType: [{ required: true, message: '请选择任务类型', trigger: 'blur' }],
        taskId: [{ required: true, message: '请选择任务', trigger: 'blur' }],
        taskName: [{ required: true, message: '请输入任务名称', trigger: 'blur' }],
        deptName: [{ required: true, message: '请输入部门名称', trigger: 'blur' }],
        systemName: [{ required: true, message: '请输入业务系统', trigger: 'blur' }],
        resourceIp: [{ required: true, message: '请输入资源IP/域名', trigger: 'blur' }, {
          validator: (rule, value, callback) => {
            const IP_REGEX = /^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/
            const DOMAIN_REGEX = /^(?:[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?\.)+[a-z0-9][a-z0-9-]{0,61}[a-z0-9]$/i
            if (value && !IP_REGEX.test(value) && !DOMAIN_REGEX.test(value)) {
              callback(new Error('请输入正确格式的IP或域名'))
            } else {
              callback()
            }
          },
          trigger: 'blur'
        }],
        url: [{ required: true, message: '请输入URL', trigger: 'blur' }],
        internetExpose: [{ required: true, message: '请输入是否为互联网暴露面', trigger: 'blur' }],
        leakName: [{ required: true, message: '请输入漏洞名称', trigger: 'blur' }],
        riskLevel: [{ required: true, message: '请选择风险等级', trigger: 'blur' }],
        login: [{ required: true, message: '请输入是否登录测试', trigger: 'blur' }],
        testReportNum: [{ required: true, message: '请输入测试报告编号', trigger: 'blur' }],
        findPeople: [{ required: true, message: '请输入发现人', trigger: 'blur' }],
        findTime: [{ required: true, message: '请输入测试时间', trigger: 'blur' }]
      },

      detail: {
        data: {},
        formLabel: [
          {
            label: '任务名称',
            field: 'taskName',
            row: 6
          },
          {
            label: '任务类型',
            field: 'taskType',
            type: 'main_task_type',
            row: 6
          },
          {
            label: '部门名称',
            field: 'deptName',
            row: 6
          },
          {
            label: '业务系统',
            field: 'systemName',
            row: 6
          },
          {
            label: '漏洞名称',
            field: 'leakName',
            row: 6
          },
          {
            label: '风险等级',
            field: 'riskLevel',
            type: 'risk_level_web',
            row: 6
          },
          {
            label: '处置状态',
            field: 'status',
            type: 'risk_status',
            row: 6
          },
          {
            label: '复现标记',
            field: 'repeatFlag',
            type: 'risk_repeatFlag',
            row: 6
          },
          {
            label: '资源IP/域名',
            field: 'resourceIp',
            row: 6
          },
          {
            label: '测试报告编号',
            field: 'testReportNum',
            row: 6
          },
          {
            label: '创建时间',
            field: 'createTime',
            row: 12
          },
          {
            label: 'URL',
            field: 'url',
            row: 12
          },
          {
            label: '测试url',
            field: 'testUrl',
            row: 12
          },
          {
            label: '问题描述',
            field: 'questionDesc',
            row: 12
          },
          {
            label: '风险分析',
            field: 'riskAnalysis',
            row: 12
          },
          {
            label: '整改建议',
            field: 'recommend',
            row: 12
          },
          {
            label: '测试过程',
            field: 'dataPacketScreenshots',
            row: 12
          },
          {
            label: '数据包截图及说明',
            field: 'testProcess',
            row: 24
          }
        ],
        isShowOper: true,
        riskType: '4',
        isRecord: true
      },
      oper: {
        data: {},
        isRecord: true
      },

      taskIdList: [],
      taskName: null,

      allocationDetail: {},
      taskId: 0,
      repeatFlag: 0,

      htmlData: {},

      statisticsList: [
        {
          type: '1',
          title: '未修复',
          num: 0,
          img: require('@/assets/images/task-processed.png')
        },
        {
          type: '2',
          title: '已修复',
          num: 0,
          img: require('@/assets/images/task-unprocessed.png')
        },
        {
          type: '3',
          title: '暂不处理',
          num: 0,
          img: require('@/assets/images/task-leave-aside.png')
        },
        {
          type: '4',
          title: '误报',
          num: 0,
          img: require('@/assets/images/task-white-list.png')
        },
        {
          type: '5',
          title: '待确认',
          num: 0,
          img: require('@/assets/images/task-repeat.png')
        }
      ],

      basicType: {
        queryType: null,
        queryBasicType: null
      },

      retestData: {
        visible: false,
        form: {},
        rules: {
          status: [{ required: true, message: '请选择复测结果', trigger: 'blur' }]
        }
      },

      resMsgListData: {
        search: {}
      },

      exportRiskWordData: {
        visible: false,
        list: [],
        msg: ''
      },

      exportReportDialog: {
        visible: false,
        list: [],
        msg: ''
      },

      riskOperatorGroupIdList: []
    }
  },
  created() {
    this.$on('on-stats-click', () => {
      this.$emit('update:stats-flag', !this.statsFlag)
    })

    if (['index'].includes(this.$props.sourceType)) {
      this.queryParams.statusList = ['1', '5']
    }

    this.queryParams = { ...this.queryParams, ...this.$props.taskDetail }

    if (this.$props.sourceType === 'index') {
      this.getBasicAlarm()
      this.basicType = {
        queryType: 3,
        queryBasicType: 4
      }
    }
    if (['basicOverview', 'check'].includes(this.$props.sourceType)) {
      this.basicType = {
        queryType: 1,
        queryBasicType: 4
      }
      if (this.$route.path.includes('/tso/')) {
        this.basicType = {
          queryType: 2,
          queryBasicType: 4
        }
      }
    }

    if (['basicOverview', 'check', 'index', 'report'].includes(this.$props.sourceType)) {
      this.queryParams = { ...this.queryParams, ...this.$props.search }
    }

    this.getQueryAllGroupNameList()

    this.getList()

    this.initSearch()
  },
  methods: {
    validateSource() {
      for (let index = 0; index < this.selection.length; index++) {
        const item = this.selection[index]
        if ([3, 4].includes(Number(item.dataSourceType))) {
          throw new Error(`安全问题：${item.taskName} 中包含租户转出或租户授权的数据，暂无法操作`)
        }
      }
    },
    formatterTime(row) {
      return dayjs(row.findTime).format('YYYY-MM-DD')
    },
    initSearch() {
      this.$nextTick(() => {
        if (!this.searchMore) {
          let clientWidth = this.$refs.queryForm.$el.clientWidth
          let idx1
          let idx2
          for (const i in this.$refs.queryForm.$children) {
            if (clientWidth - this.$refs.queryForm.$children[i].$el.clientWidth - 24 < 0) {
              idx1 = i - 1
              break
            }
            clientWidth = clientWidth - this.$refs.queryForm.$children[i].$el.clientWidth - 24
          }
          clientWidth = this.$refs.queryForm.$el.clientWidth - 200
          for (const i in this.$refs.queryForm.$children) {
            if (i > idx1) {
              if (clientWidth - this.$refs.queryForm.$children[i].$el.clientWidth - 24 < 0) {
                idx2 = i - 1
                break
              }
              clientWidth = clientWidth - this.$refs.queryForm.$children[i].$el.clientWidth - 24
            }
          }
          for (const i in this.$refs.queryForm.$children) {
            if (i > idx2 && i != this.$refs.queryForm.$children.length - 1) {
              this.$refs.queryForm.$children[i].$el.style.display = 'none'
            }
          }
        } else {
          for (const i in this.$refs.queryForm.$children) {
            this.$refs.queryForm.$children[i].$el.style.display = 'flex'
          }
        }
      })
    },
    checkPermi,
    checkRole,
    // 关闭报告预览弹框
    onCloseWord() {
      this.openWord = false
      // removeMinioFile(this.removeMinioFileForm).then((response) => {});
    },

    // 多资源IP/域名
    inputHandler() {
      this.queryParams.resourceIp = this.queryParams.resourceIp.replace(/，/, ',')
    },

    // 下载报告
    onDownWord(downloadPath, fileName) {
      this.download(
        `${downloadPath}`,
        {},
        fileName
      )
    },

    // 导出报告
    async onExportWord(row) {
      try {
        await this.validateSource()
      } catch (error) {
        this.$message.warning(error.message)
        return false
      }

      const id = row.id || this.ids

      this.exportRiskWordData.list = []
      this.exportRiskWordData.msg = ''

      this.buttonLoading = true

      const response = await request({
        url: `/risk/basic/web/exportRiskWord/${id}`,
        method: 'get'
      }).catch(e => console.warn(e))

      this.buttonLoading = false

      if (response.code == 200) {
        if (response.data.flag == 1) {
          this.exportReportDialog.list = response.data.list
          this.exportReportDialog.msg = response.data.msg
          this.exportReportDialog.visible = true
        }
        if (response.data.flag == 2) {
          this.exportRiskWordData.list = response.data.list
          this.exportRiskWordData.msg = response.data.msg
          this.exportRiskWordData.visible = true
        }
        if (response.data.flag == 3) {
          this.openWord = true
          this.docx = response.data.url
          this.pdf = response.data.pdfUrl
          this.htmlData = {
            fileName: response.data.fileName,
            url: response.data.url,
            ...response.data.htmlData
          }
          this.removeMinioFileForm.bucketName = response.data.bucketName
          this.removeMinioFileForm.fileName = response.data.fileName
        }
      }
    },

    /* 单独查看或者导出报告 */
    handleExportReport(type, ids) {
      request({
        url: `/risk/basic/web/exportRiskWord/${ids}`,
        method: 'get'
      }).then((response) => {
        if (response.code === 200) {
          if (type === 'view') {
            this.openWord = true
            this.docx = response.data.url
            this.htmlData = {
              fileName: response.data.fileName,
              url: response.data.url,
              ...response.data.htmlData
            }
            this.removeMinioFileForm.bucketName = response.data.bucketName
            this.removeMinioFileForm.fileName = response.data.fileName
          } else {
            const { url, fileName } = response.data
            this.onDownWord(url, fileName)
          }
        }
      })
    },

    onConfirmExportRiskWord() {
      this.exportRiskWordData.visible = false
      const ids = []
      this.exportRiskWordData.list.forEach(item => {
        ids.push(item.id)
      })
      request({
        url: `/risk/basic/web/exportRiskWord/${ids}`,
        method: 'get'
      }).then((response) => {
        if (response.code == 200) {
          if (response.data.flag == 3) {
            this.openWord = true
            this.docx = response.data.url
            this.pdf = response.data.pdfUrl
            this.htmlData = {
              fileName: response.data.fileName,
              url: response.data.url,
              ...response.data.htmlData
            }
            this.removeMinioFileForm.bucketName = response.data.bucketName
            this.removeMinioFileForm.fileName = response.data.fileName
          }
        }
      })
    },
    rendered() {
    },
    /** 查询应用漏洞列表 */
    getList() {
      this.loading = true
      this.queryParams = {
        ...this.queryParams,
        ...this.basicType
      }
      // statusList 会自动转所以为了不影响其他数据做了一个深拷贝
      const paramsCopy = JSON.parse(JSON.stringify(this.queryParams))
      paramsCopy.statusList = paramsCopy.statusList.join(',')
      paramsCopy.riskOperatorGroupId = (paramsCopy.riskOperatorGroupId || []).join(',')
      if (paramsCopy.statusList == '') {
        paramsCopy.statusList = null
      }
      this.lazyQueryParams = { ...paramsCopy }
      request({
        url: this.$props.source.listUrl,
        method: 'get',
        params: paramsCopy
      }).then((response) => {
        this.webList = response.rows
        this.total = response.total
        this.loading = false
        this.$nextTick(() => {
          this.webList.forEach((l) => {
            if (this.ids?.includes(l.id)) {
              this.$refs.multipleTableRef.toggleRowSelection(l, true)
            }
          })
        })
      })

      this.$nextTick(() => {
        this.$refs.statsPanelRef?.reload?.()
      })
    },
    // 多选框选中
    selectRow(selection, row) {
      if (this.ids.includes(row.id)) {
        const index = this.ids.findIndex((id) => id == row.id)
        this.ids.splice(index, 1)
        this.selectedList.splice(index, 1)
      } else {
        this.ids.push(row.id)
        this.selectedList.push(row)
      }
      if (this.systemName.includes(row.systemName)) {
        const index = this.systemName.findIndex((systemName) => systemName == row.systemName)
        this.systemName.splice(index, 1)
      } else {
        this.systemName.push(row.systemName)
      }
      this.multipleLength = this.ids.length
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    // 选中当前页面所有数据
    selectAll(selection) {
      if (selection.length == 0) {
        this.webList.forEach((l) => {
          if (this.ids.includes(l.id)) {
            const index = this.ids.findIndex((id) => id == l.id)
            this.ids.splice(index, 1)
            this.selectedList.splice(index, 1)
          }
          if (this.systemName.includes(l.systemName)) {
            const index = this.ids.findIndex((systemName) => systemName == l.systemName)
            this.systemName.splice(index, 1)
          }
        })
      } else {
        this.webList.forEach((l) => {
          if (!this.ids.includes(l.id)) {
            this.ids.push(l.id)
            this.selectedList.push(l)
          }
          if (!this.systemName.includes(l.systemName)) {
            this.systemName.push(l.systemName)
          }
        })
      }
      this.multipleLength = this.ids.length
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    onSelectionChange(selection) {
      this.selection = selection
    },
    // 分配
    onAllocation() {
      this.onClearSelection()
      this.getList()
      if (this.$props.sourceType === 'index') {
        this.getBasicAlarm()
      }
      this.$emit('confirm')
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        taskId: null,
        taskName: null,
        taskType: null,
        deptId: null,
        deptName: null,
        systemId: null,
        systemName: null,
        resourceIp: null,
        url: null,
        internetExpose: null,
        leakName: null,
        riskLevel: null,
        login: null,
        testReportNum: null,
        findPeople: null,
        findTime: null,
        repeatFlag: null,
        riskOperatorGroupId: null,
        disposeId: null,
        disposeName: null,
        status: null,
        remark: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        leakVersion: null,
        dataPacketScreenshots: null
      }
      if (this.$props.sourceType === 'report') {
        this.form = {
          ...this.form,
          taskId: this.$props.taskDetail.taskId,
          taskName: this.$props.taskDetail.taskName,
          taskType: this.$props.taskDetail.taskType
        }
      }
      this.resetForm('form')
    },
    // 失去焦点触发
    onBlurSystem() {
      setTimeout(() => {
        this.isSystem = false
      }, 500)
    },
    // 输入框改变
    onChangeSystem(val) {
      this.isSystem = true
      request({
        url: `/risk/warn/hint/search`,
        method: 'get',
        params: {
          riskType: 1,
          riskWarnType: 20007,
          field: 'systemName',
          value: val
        }
      }).then((response) => {
        if (!response) {
          this.systemData = []
        } else {
          this.systemData = response.data
        }
      })
    },
    onSystemMsg(val) {
      this.isSystem = false
      this.queryParams.systemName = val
    },
    // 失去焦点触发
    onBlurLeak() {
      setTimeout(() => {
        this.isLeak = false
      }, 500)
    },
    // 输入框改变
    onChangeLeak(val) {
      this.isLeak = true
      request({
        url: `/risk/warn/hint/search`,
        method: 'get',
        params: {
          riskType: 1,
          riskWarnType: 20007,
          field: 'leakName',
          value: val
        }
      }).then((response) => {
        if (!response) {
          this.leakData = []
        } else {
          this.leakData = response.data
        }
      })
    },
    onLeakMsg(val) {
      this.isLeak = false
      this.queryParams.leakName = val
    },
    // 查看历史
    onHistoryDetail(row) {
      this.taskDetail.resourceIp = row.resourceIp
      this.taskDetail.systemName = row.systemName
      this.taskDetail.leakName = row.leakName
      this.taskId = this.taskDetail.taskId
      this.repeatFlag = this.taskDetail.repeatFlag
      delete this.taskDetail.taskId
      delete this.taskDetail.repeatFlag
      delete this.taskDetail.statusList
      this.$refs.historyRef.show()
    },
    // 关闭查看历史
    onClose() {
      this.taskDetail.leakName = ''
      this.taskDetail.systemName = ''
      this.taskDetail.resourceIp = ''
      this.taskDetail.taskId = this.taskId
      this.taskDetail.repeatFlag = this.repeatFlag
    },

    // 获取开始ip
    getStartIp(val) {
      this.queryParams.startIp = val
    },
    // 获取结束ip
    getEndIp(val) {
      this.queryParams.endIp = val
      this.getList()
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.resetInput = ''
      this.ids = []
      this.getList()
      if (this.$props.sourceType === 'index') {
        this.getBasicAlarm()
      }
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.queryParams.startIp = ''
      this.queryParams.endIp = ''
      this.daterange = []
      this.onChangeDaterange()
      this.handleQuery()
      this.$refs.ipRef.reset()
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.type = 'add'
      this.title = `新增${this.$route.meta.title}`
      this.form.testingItemList = cloneDeep(this.dataMixin.defaultTestingItemList)
      this.getTaskListByType()
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const id = row.id || this.ids
      getWeb(id).then((response) => {
        this.form = response.data
        this.open = true
        this.type = 'edit'
        this.title = `修改${this.$route.meta.title}`
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateWeb(this.form).then((response) => {
              this.$modal.msgSuccess('修改成功')
              this.open = false
              this.getList()
              if (this.$props.sourceType === 'index') {
                this.getBasicAlarm()
              }
            })
          } else {
            addWeb(this.form).then((response) => {
              this.$modal.msgSuccess('新增成功')
              this.open = false
              this.getList()
              if (this.$props.sourceType === 'index') {
                this.getBasicAlarm()
              }
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids

      this.$modal
        .confirm(`是否确认删除${row.taskName || '当前所选'}数据项？`)
        .then(function() {
          return delWeb(ids)
        })
        .then(() => {
          this.onClearSelection()
          this.getList()
          if (this.$props.sourceType === 'index') {
            this.getBasicAlarm()
          }
          this.$modal.msgSuccess('删除成功')
          this.$emit('confirm')
        })
        .catch(() => {
        })
    },
    /** 导出按钮操作 */
    handleExport() {
      const year = new Date().getFullYear()
      const value = `${year}年中移（动）信息技术有限公司平台能力中心互联网暴露面漏洞汇总`
      this.$prompt(`请您核实即将导出的文件名称,如需调整,您可在下方输入框中进行自定义.`, '下载文件', {
        customClass: 'export-xlsx',
        confirmButtonText: '确定下载',
        cancelButtonText: '取消下载',
        inputValue: value
      }
      ).then(({ value }) => {
        this.exportXlsx(value)
      }).catch(() => {

      })
    },
    exportXlsx(value) {
      if (this.total > 200000) {
        this.$modal.msgWarning('抱歉，导出数据量过大，请选择筛选条件后再进行导出')
        return false
      }
      const submitAuery = JSON.parse(JSON.stringify(this.queryParams))
      delete submitAuery.pageNum
      delete submitAuery.pageSize
      submitAuery.statusList = submitAuery.statusList.join()

      if (submitAuery.riskOperatorGroupId) {
        submitAuery.riskOperatorGroupId = submitAuery.riskOperatorGroupId.join()
      }

      this.download(
        this.$props.source.exportUrl,
        {
          ...submitAuery,
          ids: this.ids
        },
        `${value}.xlsx`
      )
    },
    onDetail(row) {
      this.$refs.infoDialogRef.open(row)
    },
    // onDetail(row) {
    //   this.reset()
    //   const id = row.id || this.ids
    //   getWeb(id).then((response) => {
    //     this.detail.data = response.data

    //     if (this.detail?.data?.disposalVoucherName) {
    //       this.detail.formLabel.push({
    //         label: '修复凭证',
    //         field: 'disposalVoucherName',
    //         isTooltip: true
    //       })
    //     }

    //     this.$refs.detail.show()
    //   })
    // },
    onOper(row) {
      const data = {
        ids: row.id?.toString().split(',') || this.calcSelectedIds(),
        disposeStatus: row.status || '',
        remarks: row.remarks || ''
      }
      if (!data.ids || data.ids.length == 0) {
        this.$modal.msgWarning('已处置不可操作')
        return false
      }
      this.oper.data = {
        riskType: '1',
        riskWarnType: 20007,
        riskContainerSafetyType: '',
        ...data,
        riskType2: '4'
      }
      this.oper.isRecord = !!row.id
      this.$refs.oper.show()
    },
    onOperDetail() {
      this.onClearSelection()
      this.getList()
      if (this.$props.sourceType === 'index') {
        this.getBasicAlarm()
      }
      this.$emit('confirm')
    },
    getTaskListByType() {
      if (this.form.taskType) {
        request({
          url: '/risk/report/task/getTaskListByType',
          method: 'get',
          params: {
            taskType: this.form.taskType,
            taskName: this.taskName
          }
        }).then((response) => {
          this.taskIdList = response.data
        })
      }
    },
    onChangeTaskId() {
      this.$nextTick(() => {
        this.form.taskName = this.$refs.taskIdRef.selectedLabel
      })
    },
    remoteMethod(val) {
      this.taskName = val
      this.getTaskListByType()
    },
    selectable(row) {
      // if([null, void 0].includes(Number(row.dataSourceType))) {
      //   return true
      // }

      // if(this.$props.taskDetail.taskId) {
      //   return [1].includes(Number(row.dataSourceType))
      // }

      return true

      // return [1, 2].includes(Number(row.dataSourceType))
    },
    onChangeDaterange() {
      if (this.daterange && this.daterange.length > 0) {
        this.queryParams.startTime = this.daterange[0]
        this.queryParams.endTime = this.daterange[1]
      } else {
        delete this.queryParams.startTime
        delete this.queryParams.endTime
      }
    },
    async onAllocationDetail(type, row, title) {
      try {
        await this.validateSource()
      } catch (error) {
        this.$message.warning(error.message)
        return false
      }

      const queryBo = JSON.parse(JSON.stringify(this.queryParams))
      delete queryBo.pageNum
      delete queryBo.pageSize

      let ids = []
      if (type == 2) {
        ids = row ? [row.id] : this.calcSelectedIds()
      }
      if (type != 1 && ids.length == 0) {
        this.$modal.msgWarning('已处置不可操作')
        return false
      }
      this.allocationDetail = {
        riskWarnType: '20007',
        type: type,
        taskId: this.$props.taskDetail.taskId,
        ids,
        title: title || '分配',
        riskType: '4',
        webQueryBo: queryBo
      }
      this.$refs.allocationDetailRef.show()
    },
    async goBackRiskData(row) {
      try {
        await this.validateSource()
      } catch (error) {
        this.$message.warning(error.message)
        return false
      }

      const ids = row.id || this.calcSelectedIds().join(',')
      if (!ids) {
        this.$modal.msgWarning('已处置不可操作')
        return false
      }
      this.$modal.confirm(`是否确认退回`).then(() => {
        request({
          url: '/risk/turnoverRecord/goBackRiskData',
          method: 'post',
          data: {
            riskType: '4',
            ids
          }
        }).then((response) => {
          this.$modal.msgSuccess(response.msg)
          this.onClearSelection()
          this.getList()
          if (this.$props.sourceType === 'index') {
            this.getBasicAlarm()
          }
        })
      })
    },
    getBasicAlarm() {
      // const params = {
      //   type: '4',
      //   queryType: 3,
      //   ...this.queryParams
      // }
      // delete params.pageNum
      // delete params.pageSize

      // request({
      //   url: '/risk/census/basicAlarm',
      //   method: 'post',
      //   data: params
      // }).then((response) => {
      //   this.statisticsList.forEach((item) => {
      //     item.num = response.data.find((i) => i.status == item.type)?.num || 0
      //   })
      // })
    },
    onOpenRetest(row) {
      this.retestData.form = {
        riskBasicWebId: row.id,
        leakName: row.leakName,
        riskLevel: row.riskLevel,
        systemName: row.systemName,
        testProcess: '',
        status: ''
      }
      this.retestData.visible = true
      this.$refs.retestFormRef?.clearValidate()
    },
    onConfirmRetest() {
      this.$refs.retestFormRef.validate((valid) => {
        if (valid) {
          request({
            url: '/risk/basic/web/retest',
            method: 'post',
            data: this.retestData.form
          }).then((response) => {
            if (response.code == 200) {
              this.$modal.msgSuccess(response.msg)
              setTimeout(() => {
                this.retestData.visible = false
                this.getList()
                if (this.$props.sourceType === 'index') {
                  this.getBasicAlarm()
                }
              }, 500)
            }
          })
        }
      })
    },
    onClearSelection() {
      this.$refs.multipleTableRef.clearSelection()
      this.multiple = true
      this.queryParams.pageNum = 1
      this.multipleLength = 0
      this.ids = []
      this.selectedList = []
    },
    calcSelectedIds() {
      const ids = []
      for (const item of this.selectedList) {
        if (item.status != 2) {
          ids.push(item.id)
        }
      }
      return ids
    },
    onOpenResMsgList(row) {
      this.resMsgListData.search = {
        riskBasicWebId: row.id,
        type: 2
      }
      this.$refs.resMsgListRef.show()
    },
    getQueryAllGroupNameList() {
      request({
        url: '/risk/operator/groupUser/queryAllGroupNameList',
        method: 'get'
      }).then((response) => {
        this.riskOperatorGroupIdList = response.data
      })
    },
    handleApprove(row) {
      const ids = row?.id ? [row.id] : this.ids

      this.$refs.approveDialogRef.open({
        params: {
          riskWarnType: '20007',
          ids
        }
      })
    }
  }
}
</script>

<style scoped>
.keyValue >>> img {
  width: 600px !important;
}

</style>

<style lang="scss">
.export-xlsx {
  width: 540px !important;
}

.preview-dialog {
  .el-dialog__body {
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .docx-btn-box {
    width: 100%;
    display: flex;
    justify-content: flex-end;
    margin-bottom: 10px;
  }

  .preview-dialog-content {
    width: 560px;
  }

  .title {
    color: #000;
    font-size: 28px;
    text-align: center;
  }

  .subtitle {
    color: #000;
    font-size: 28px;
  }

  .subtitle2_1 {
    color: #000;
    font-size: 20px;
    font-weight: 700;
  }

  .subtitle2_1_ {
    color: #000;
    font-size: 18px;
    font-weight: 700;
  }

  .content {
    line-height: 32px;
    color: #595959;
    font-size: 20px;

    &.indent {
      text-indent: 2em;
    }
  }

  .key {
    color: #000;
    font-size: 18px;
    text-decoration: underline;
    font-weight: 700;
  }

  .keyValue.testUrl {
    color: #00f;
    text-decoration: underline;
  }

  .keyValue.gao {
    color: #f00;
  }

  .keyValue.zhong {
    color: #e6a23c;
  }

  table {
    width: 560px;
    font-size: 12px;
    border-color: #595959;
    border-collapse: collapse;
    border-style: solid;
    border-width: 1px;
    line-height: 20px;
  }
}
</style>
<style lang="scss" scoped>
.change-input {
  margin-left: 12px;
}

.app-loophole-form {
  ::v-deep {
    .el-form-item {
      margin-bottom: 24px !important;
    }
  }
}

.prompt {
  position: relative;

  .prompt-box {
    position: absolute;
    left: -28px;
    top: 40px;
  }

  .prompt-box-content {
    position: relative;
    width: 200px;
    height: auto;
    z-index: 100;
    border: solid 1px #dfe4ed;
    border-radius: 4px;
    background-color: #ffffff;
    padding: 8px;
    white-space: nowrap;
    overflow-x: scroll;
  }

  .ovflow-y {
    height: 260px;
    overflow-y: scroll;
  }

  .prompt-box-sanjiao {
    position: absolute;
    top: -9px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 101;
  }

  .sanjiao {
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 0 10px 10px;
    border-color: transparent transparent #dfe4ed;
    position: relative;
  }

  .sanjiao::after {
    content: '';
    border-style: solid;
    border-width: 0 9px 9px;
    border-color: transparent transparent #fff;
    position: absolute;
    top: 1px;
    left: -9px;
  }

  .input-msg-item {
    padding-bottom: 6px;
    // border-bottom: 1px solid #f0f0f0;
    cursor: pointer;
  }
}

.statistics-item {
  height: 80px;
  border-radius: 8px;
  background: #ffffff;
  box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.04);
  margin: 0 0 16px;
  padding: 10px 20px 20px;

  .num {
    font-family: OPPOSans;
    font-size: 24px;
    font-weight: 900;
    line-height: 36px;
    color: #3e4040;
  }

  .title {
    font-size: 14px;
    font-weight: 400;
    line-height: 18px;
    color: #636466;

    img {
      flex-shrink: 0;
      width: 16px;
      height: 16px;
      margin-right: 4px;
    }
  }
}

@media only screen and (min-width: 992px) {
  .el-col-md-6 {
    width: 20%;
  }
}
</style>
