<template>
  <EleSheet ref="sheetRef" v-bind="sheetProps" class="page-main page-main--flat">
    <template #toolbar:after="scope">
      <el-button
        v-if="$checkPermi(['risk:taskSendInfo:image:assign'])"
        type="warning"
        icon="iconfont icon-piliangfenpei1"
        :disabled="!scope.selected"
        @click="handleAssign(scope)"
      >分配</el-button>
      <el-button
        v-if="$checkPermi(['risk:taskSendInfo:image:assign']) && ['safeTask'].includes(source.type)"
        type="warning"
        icon="iconfont icon-piliangfenpei1"
        @click="handleAssign(scope, 'all')"
      >全量分配</el-button>
      <el-button
        v-if="$checkPermi(['risk:taskSendInfo:image:dispose'])"
        type="primary"
        :disabled="!scope.selected"
        @click="handleDispose(scope)"
      ><IconHandyman class="" />处置</el-button>
    </template>

    <template v-if="$checkPermi(['risk:taskSendInfo:image:download'])" #table:fileUrl:simple="{ row }">
      <el-link v-if="row.fileUrl" type="primary" @click="onDownload(row.fileUrl, row.fileUrlName)">
        下载
      </el-link>
      <template v-else>无</template>
    </template>

    <template #table:taskFileList:simple="{ row }">
      <template v-if="row.taskFileList && row.taskFileList.length > 0">
        <div v-for="(item, index) in row.taskFileList" :key="index" class="">
          <el-link type="primary" @click="onDownload(item.url, item.name)">
            <sa-tooltip class="name" :content="item.name" />
          </el-link>
        </div>
      </template>
      <template v-else>-</template>
    </template>

    <template #info:fileUrl:simple="{ model: row }">
      <el-link v-if="row.fileUrl" type="primary" @click="onDownload(row.fileUrl, row.fileUrlName)">
        下载
      </el-link>
      <template v-else>无</template>
    </template>

    <template #info:taskFileList:simple="{ model: row }">
      <template v-if="row.taskFileList && row.taskFileList.length > 0">
        <div v-for="(item, index) in row.taskFileList" :key="index" class="">
          <el-link type="primary" @click="onDownload(item.url, item.name)">
            <sa-tooltip class="name" :content="item.name" />
          </el-link>
        </div>
      </template>
      <template v-else>-</template>
    </template>

    <template #search:deptName:simple="{ model }">
      <DeptTreeSelect
        v-model="model.deptId"
        placeholder="请选择"
        @input="(value) => onDepTreeInput(value, model)"
        @select="(event) => onDeptTreeSelect(event, model)"
      />
    </template>
    <template #search:systemName:simple="{ model }">
      <SystemSelect
        v-model="model.systemId"
        :dept-id="model.deptId"
        placeholder="请选择"
        @change="(value, label) => onSystemSelect(value, label, model)"
      />
    </template>

    <template #table:action:after="{ row }">
      <el-button
        v-if="$checkPermi(['risk:taskSendInfo:image:dispose'])"
        size="mini"
        type="text"
        @click="handleDispose(row)"
      >处置</el-button>
    </template>

    <template #info:after="{ data }">
      <TaskFlowProfile
        v-if="data.id"
        v-bind="{
          taskInfo: data,
          params: {
            riskType: data.riskType,
            id: data.id,
          },
          disposeResultDict: dict.type.dispose_result_jxpc,
          hiddenDispose: [].includes(data.disposeResult),
        }"
      />
    </template>

    <template #after>
      <AssignDialog ref="assignDialogRef" />
      <DisposeDialog ref="disposeDialogRef" />
      <ReturnDialog ref="returnDialogRef" />
    </template>
  </EleSheet>
</template>

<script>
import request from '@/utils/request.js'

import AssignDialog from '@/views/risk/basic/components/AssignDialog/index.vue'
import DisposeDialog from '@/views/risk/basic/components/DisposeDialog/index.vue'
import ReturnDialog from '@/views/risk/basic/components/ReturnDialog/index.vue'

import TaskFlowProfile from '@/components/business/TaskFlowProfile/index.vue'

import { mirrorCheckType } from '@/dicts/basic/index.js'

export default {
  dicts: ['dispose_status_jxpc', 'dispose_result_jxpc', 'allocation_status', 'risk_level'],
  components: {
    TaskFlowProfile,
    AssignDialog,
    DisposeDialog,
    ReturnDialog
  },
  props: {
    source: {
      type: Object,
      default: () => ({
        type: 'default'
      })
    },
    params: {
      type: Object,
      default: () => ({})
    },
    autoHeight: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      mirrorCheckType,
      defaultParams: {
        taskType: '2'
      }
    }
  },
  computed: {
    sheetProps() {
      const value = {
        title: '镜像排查风险',

        lazy: false,

        hiddenActions: {
          export: !this.$checkPermi(['risk:taskSendInfo:image:export'])
        },

        api: {
          // add: (params) => addController({ ...params }),
          // edit: (params) => updateController({ ...params }),
          // remove: delController,
          list: async(params) => {
            const res = await request({
              url: `/risk/taskSendInfo/list`,
              method: 'get',
              params: {
                ...this.defaultParams,
                ...params,
                ...this.params
              }
            })

            return {
              ...res,
              rows: res.rows.map((item) => ({
                ...item
                // fileUrl: '111',
                // taskFileList: [
                //   { url: '111', name: '2222' },
                //   { url: '111', name: '2222' },
                // ],
              }))
            }
          },
          info: async(id) =>
            request({
              url: `/risk/taskSendInfo/${id}`,
              method: 'get',
              params: {
                ...this.defaultParams
              }
            }),
          export: '/controller/export',
          import: '',
          template: ''
        },

        infoProps: {
          title: true
        },

        tableProps: {
          selection: 'multiple',
          selectionRowKeys: this.selectionRowKeys,
          ...(this.autoHeight ? {
            height: '100%'
          } : {})
        },

        model: {
          taskId: {
            type: 'text',
            label: '任务编码',
            width: 150,
            hidden: true
          },
          taskName: {
            type: 'text',
            label: '任务名称',
            width: 150,
            search: {
              hidden: ['safeTask'].includes(this.source.type)
            },
            table: {
              hidden: ['safeTask'].includes(this.source.type)
            }
          },
          deptName: {
            type: 'text',
            label: '业务部门',
            width: 150,
            form: {
              rules: true
            },
            search: {
              hidden: !['safeTask'].includes(this.source.type)
            },
            table: {
              hidden: !['safeTask'].includes(this.source.type)
            }
          },
          systemName: {
            type: 'text',
            label: '业务系统',
            width: 150,
            form: {
              rules: true
            },
            search: {
              hidden: !['safeTask'].includes(this.source.type)
            },
            table: {
              hidden: !['safeTask'].includes(this.source.type)
            }
          },
          deptId: {
            hidden: true
          },
          systemId: {
            hidden: true
          },
          compName: { type: 'text', label: '组件名称', width: 150 },
          compVersion: { type: 'text', label: '组件版本', width: 150 },
          loopholeName: { type: 'text', label: '漏洞名称', width: 150 },
          loopholeInfo: {
            type: 'text',
            label: '漏洞信息',
            width: 200,
            search: {
              hidden: true
            }
          },
          disposeStatus: {
            type: 'select',
            label: '处置状态',
            options: this.dict.type.dispose_status_jxpc
          },
          disposeName: {
            type: 'text',
            label: '处置人',
            width: 100,
            search: {
              hidden: true
            },
            table: {
              hidden: true
            }
          },
          disposeTime: {
            type: 'date',
            label: '处置时间',
            width: 150,
            search: {
              hidden: true
            },
            table: {
              hidden: true
            }
          },
          dispatchStatus: {
            type: 'select',
            label: '分配状态',
            options: this.dict.type.allocation_status,
            form: {
              hidden: true
            },
            width: 100
          },
          dispatchName: {
            type: 'text',
            label: '分配人',
            width: 100,
            search: {
              hidden: true
            },
            table: {
              hidden: true
            }
          },
          dispatchTime: {
            type: 'date',
            label: '分配时间',
            width: 150,
            table: {
              hidden: true
            }
          },
          riskOperatorGroupName: {
            type: 'text',
            label: '处置分组',
            width: 150,
            search: {
              hidden: true
            }
          },
          taskFileList: {
            type: 'text',
            label: '任务附件',
            width: 200
          },
          remarks: {
            type: 'text',
            label: '备注',
            width: 200,
            search: {
              hidden: true
            }
          }
          // fileUrl: {
          //   type: 'text',
          //   label: '附件',
          // },

          // createTime: {
          //   type: 'text',
          //   label: '创建时间',
          //   search: {
          //     type: 'date-range',
          //     parameter: (data) => {
          //       return {
          //         startTime: data?.[0],
          //         endTime: data?.[1],
          //       };
          //     },
          //   },
          //   form: {
          //     hidden: true,
          //   },
          //   width: 200,
          // },
        }
      }

      return value
    }
  },
  methods: {
    onDownload(downloadPath, fileName) {
      this.download(`${downloadPath}`, {}, fileName)
    },
    handleDispose(event) {
      const params = ['multiple'].includes(event.selectionType)
        ? {
          ids: event.selectionIds,
          selection: event.selection
        }
        : {
          id: event.id
        }

      Object.assign(params, {
        riskType: this.mirrorCheckType,
        riskCategory: '1'
      })

      this.$refs.disposeDialogRef.open({
        params,
        success: () => {
          this.$refs.sheetRef.getTableData()
        }
      })
    },
    handleAssign(event, handleType) {
      const params = ['multiple'].includes(event.selectionType)
        ? {
          ids: event.selectionIds,
          selection: event.selection
        }
        : {
          id: event.id
        }

      Object.assign(params, {
        riskType: this.mirrorCheckType,
        riskCategory: '1',
        groupKey: 'basic_type_base_dim'
      })

      if (handleType === 'all') {
        delete params.id
        delete params.ids

        Object.assign(params, {
          taskId: this.params.taskId
        })
      }

      this.$refs.assignDialogRef.open({
        params,
        success: () => {
          this.$refs.sheetRef.getTableData()
        }
      })
    },
    onDepTreeInput(value, model) {
      if (!value) {
        model.sysId = ''
        model.deptName = ''
      }
    },
    onDeptTreeSelect(node, model) {
      model.systemId = ''
      model.deptName = node.label
    },
    onSystemSelect(value, label, model) {
      model.systemName = label
    }
  }
}
</script>

<style></style>
