<template>
  <div class="h-full" :class="['detail', 'allocation', 'basicOverview'].includes(sourceType) ? 'page-main' : ''">
    <StatsPanel
      v-if="sourceType == 'index' && lazyQueryParams"
      ref="statsPanelRef"
      :key="JSON.stringify(lazyQueryParams)"
      :params="{
        type: '2',
        queryType: 3,
        ...lazyQueryParams,
      }"
      class="!overflow-hidden !transition-all"
      :class="statsFlag ? '!h-[96px]' : '!h-0'"
    />

    <el-form
      v-if="!['statistics'].includes(sourceType)"
      v-show="showSearch"
      ref="queryForm"
      class="sa-query"
      :model="queryParams"
      size="small"
      :inline="true"
      label-width="68px"
    >
      <!-- <div class="sa-query-item sa-flex sa-flex-wrap" :class="searchMore ? 'sa-query-item1' : 'sa-query-item2'"> -->

      <!-- <el-form-item class="" label="数据类别" prop="dataSourceType">
          <DictSelect v-model="queryParams.dataSourceType" dict-type="tenantDataType" />
        </el-form-item>
        <el-form-item class="" label="所属租户" prop="tenId">
          <TenantSelect
            ref="tenantSelectRef"
            v-model="queryParams.tenId"
            @change="handleQuery"
          />
        </el-form-item> -->

      <template v-if="['index', 'basicOverview'].includes(sourceType)">
        <el-form-item label="任务名称" prop="taskName">
          <el-input
            v-model="queryParams.taskName"
            placeholder="请输入任务名称"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="任务类型" prop="taskType">
          <el-select
            v-model="queryParams.taskType"
            placeholder="请选择任务类型"
            clearable
            @change="handleQuery"
          >
            <template v-for="dict in dict.type.main_task_type">
              <el-option
                v-if="dict.value == 1 || dict.value == 4"
                :label="dict.label"
                :value="dict.value"
              />
            </template>
          </el-select>
        </el-form-item>
      </template>

      <el-form-item label="部门名称" prop="deptName">
        <CommonDepartmentSelect
          v-model="queryParams.deptName"
          placeholder="请输入"
          clearable
          return-name
          @change="()=> queryParams.standardName = void 0"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="业务系统" prop="standardName">
        <CommonSystemSelect
          v-model="queryParams.standardName"
          placeholder="请输入"
          return-name
          clearable
          :params="{
            deptName: queryParams.deptName
          }"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="处置状态" prop="statusList">
        <el-select
          v-model="queryParams.statusList"
          multiple
          collapse-tags
          placeholder="请选择处置状态"
          clearable
          @change="handleQuery"
        >
          <el-option
            v-for="dict in dict.type.risk_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item
        v-if="
          (['report'].includes(sourceType) && ['detail', 'allocation'].includes(operType)) ||
            ['index', 'basicOverview'].includes(sourceType)
        "
        label="分配状态"
        prop="dispatchStatus"
      >
        <el-select
          v-model="queryParams.dispatchStatus"
          placeholder="请选择分配状态"
          clearable
          filterable
          @change="handleQuery"
        >
          <el-option
            v-for="dict in dict.type.allocation_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="风险等级" prop="riskLevel">
        <el-select
          v-model="queryParams.riskLevel"
          placeholder="请选择风险等级"
          clearable
          @change="handleQuery"
        >
          <el-option
            v-for="dict in dict.type.risk_level"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="基线要求" prop="requireContent">
        <div class="prompt">
          <el-input
            slot="reference"
            v-model="queryParams.requireContent"
            placeholder="请输入基线要求"
            clearable
            @blur="onBlurRequire"
            @input="onChangeRequire"
            @keyup.enter.native="handleQuery"
          />
          <div v-if="requireData.length > 0 && isRequire" class="prompt-box">
            <div class="prompt-box-sanjiao">
              <div class="sanjiao" />
            </div>
            <div class="prompt-box-content" :class="{ 'ovflow-y': requireData.length > 6 }">
              <div
                v-for="(item, index) in requireData"
                :key="index"
                class="input-msg-item"
                @click="onRequireMsg(item)"
              >
                {{ item }}
              </div>
            </div>
          </div>
        </div>
      </el-form-item>

      <el-form-item class="task-name" label="全量重复标记" prop="historyRepeatFlag">
        <el-select
          v-model="queryParams.historyRepeatFlag"
          placeholder="请选择全量重复标记"
          clearable
          class="el-select-highlight"
          @change="handleQuery"
        >
          <el-option
            v-for="dict in dict.type.risk_repeatFlag"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="复现标记" prop="repeatFlag">
        <el-select
          v-model="queryParams.repeatFlag"
          placeholder="请选择复现标记"
          clearable
          class="el-select-highlight"
          @change="handleQuery"
        >
          <el-option
            v-for="dict in dict.type.risk_repeatFlag"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="超期标记" prop="overdueStatus">
        <EleSelectDict v-model="queryParams.overdueStatus" dict-type="whether" @change="handleQuery" />
      </el-form-item>

      <el-form-item label="资产IP" prop="resourceIp" class="">
        <el-input
          v-model="queryParams.resourceIp"
          placeholder="资产IP(多IP请用&quot;,&quot;分开)"
          clearable
          @input="inputHandler"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item class="daterange" label="创建时间" prop="daterange">
        <el-date-picker
          v-model="daterange"
          type="daterange"
          value-format="yyyy-MM-dd"
          format="yyyy-MM-dd"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="onChangeDaterange"
        />
      </el-form-item>

      <el-form-item label="IP区间" class="ip-item">
        <ip-input ref="ipRef" @startIp="getStartIp" @endIp="getEndIp" />
      </el-form-item>

      <!-- <el-form-item label="原报告系统名称" prop="systemName">
          <sa-search :searchValue="queryParams.systemName" :url="searchUrl" filed="systemName" @confirm="(val)=>queryParams.systemName = val" />
        </el-form-item> -->
      <el-form-item
        v-if="
          ['index', 'basicOverview'].includes(sourceType) ||
            (['report'].includes(sourceType) && operType == 'detail')
        "
        label="处置分组"
        prop="riskOperatorGroupId"
      >
        <el-select
          v-model="queryParams.riskOperatorGroupId"
          placeholder="请选择处置分组"
          clearable
          filterable
          multiple
        >
          <el-option
            v-for="item in riskOperatorGroupIdList"
            :key="item.id"
            :label="item.groupName"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="所属资产类型" prop="resourceType">
        <el-input
          v-model="queryParams.resourceType"
          placeholder="请输入所属资产类型"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <!-- </div> -->
      <el-form-item class="query-handle">
        <el-button type="primary" plain icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh-left" @click="resetQuery">重置</el-button>
        <EleSheetSearchMore :parent-ref="()=> $refs.queryForm" />
      </el-form-item>
    </el-form>

    <div class="sa-title flex items-center">

      <div class="flex-1 w-0 space-x-2">
        <template v-if="taskDetail.riskOperatorGroupId != 0">
          <template v-if="sourceType != 'check'">
            <!-- <el-button
              v-if="taskDetail.taskType != 2"
              type="primary"
              icon="el-icon-plus"
              @click="handleAdd"
              v-hasPermi="['risk:basic:line:add']"
              >新增</el-button
            > -->

            <!-- <el-button
              v-if="source.exportUrl.includes('export')"
              type="primary"
              icon="el-icon-download"
              @click="handleExport"
              v-hasPermi="['risk:basic:line:export']"
              >导出
            </el-button>
            <el-button
              v-if="source.exportUrl.includes('adminAndTenantLineListExport')"
              type="primary"
              icon="el-icon-download"
              @click="handleExport"
              v-hasPermi="['risk:basic:line:adminAndTenantLineListExport']"
              >导出
            </el-button> -->

            <ExportActions
              v-bind="{
                queryParams,
                source,
                sourceType,
                basicType: 'line'
              }"
            />

            <el-button
              v-if="layout.includes('approve')"
              type="success"
              icon="el-icon-s-check"
              :disabled="multiple"
              @click="handleApprove()"
            >审批</el-button>

            <el-button
              v-if="taskDetail.taskType != 2 && !['statistics'].includes(sourceType) && layout.includes('all')"
              v-hasPermi="['risk:warn:line:dispose']"
              type="primary"
              :disabled="multiple"
              @click="onOper"
            ><IconHandyman class="" />处置
            </el-button>
          <!-- <el-button
              v-if="!['index'].includes(sourceType) && taskDetail.taskType != 2"
              type="danger"
              plain
              :disabled="multiple"
              @click="handleDelete"
              v-hasPermi="['risk:basic:line:remove']"
              ><i class="iconfont icon-piliangshanchu"></i>批量删除</el-button
            > -->
          </template>
          <el-button
            v-if="$checkPermi(['risk:baseLineXmlFileInfo:list']) && ['report'].includes(sourceType) && layout.includes('all')"
            type="success"
            icon="el-icon-view"
            @click="onOpenBaseLineXmlFileInfo"
          >查看基线文件明细
          </el-button>

          <LineCheckPreview
            v-if="$checkPermi(['line:template:check:item']) && queryParams.baseLineTemplateId && queryParams.platformType"
            v-slot="{ trigger }"
            :params="{
              id: queryParams.baseLineTemplateId,
              platformType: queryParams.platformType,
            }"
          >
            <el-button
              type="primary"
              icon="el-icon-view"

              @click="trigger"
            >查看检查项</el-button>
          </LineCheckPreview>
        </template>

        <template v-if="!['statistics'].includes(sourceType) && layout.includes('all')">
          <template v-if="taskDetail.riskOperatorGroupId == 0" class="">
            <el-popover v-if="$checkPermi(['risk:task:security:issue:batch:distribution'])" placement="top-start" trigger="hover" content="列表所选中的进行分配">
              <el-button
                slot="reference"
                type="warning"
                :disabled="ids.length == 0"
                @click="onAllocationDetail(2)"
              ><i class="iconfont icon-piliangfenpei1" />批量分配
              </el-button>
            </el-popover>
            <el-popover v-if="$checkPermi(['risk:task:security:issue:full:allocation']) && ['report'].includes(sourceType)" placement="top-start" trigger="hover" content="当前条件查询的进行分配">
              <el-button
                slot="reference"
                class=""
                type="warning"
                plain
                :disabled="lineList.length == 0"
                @click="onAllocationDetail(1)"
              ><i class="iconfont icon-piliangfenpei" />全量分配
              </el-button>
            </el-popover>
          </template>
          <template v-else>
            <el-popover v-if="$checkPermi(['risk:basic:line:batch:distribution'])" placement="top-start" trigger="hover" content="列表所选中的进行分配">
              <el-button
                slot="reference"
                type="warning"
                :disabled="ids.length == 0"
                @click="onAllocationDetail(2)"
              ><i class="iconfont icon-piliangfenpei1 !w-[1.2em] !h-[1.2em]" />批量分配
              </el-button>
            </el-popover>
            <el-popover v-if="$checkPermi(['risk:basic:line:full:allocation']) && ['report'].includes(sourceType)" placement="top-start" trigger="hover" content="当前条件查询的进行分配">
              <el-button
                slot="reference"
                type="warning"
                :disabled="lineList.length == 0"
                @click="onAllocationDetail(1)"
              ><i class="iconfont icon-piliangfenpei" />全量分配
              </el-button>
            </el-popover>
          </template>
        </template>

        <el-button
          v-if="$checkPermi(['risk:turnoverRecord:line:goBackRiskData']) && taskDetail.riskOperatorGroupId != 0 && !['statistics'].includes(sourceType) && layout.includes('all')"
          type="warning"
          :disabled="multiple"
          @click="goBackRiskData"
        ><icon-undo class="el-icon" />退回
        </el-button>
        <template v-if="['index', 'basicOverview'].includes(sourceType) && layout.includes('all')">
          <el-button
            v-if="$checkPermi(['risk:turnoverRecord:line:transferRiskData'])"
            type="warning"
            :disabled="multiple"
            @click="onAllocationDetail(2, '', '转派')"
          ><IconForward class="" />转派
          </el-button>
          <el-button
            v-hasPermi="['risk:basic:line:group:export']"
            type="primary"
            icon="el-icon-download"
            @click="handleExportSystem(1)"
          >导出汇总明细
          </el-button>
          <el-button
            v-hasPermi="['risk:basic:line:group:importData']"
            type="primary"
            icon="el-icon-upload2"
            @click="handleImport"
          >导入处置结果
          </el-button>
        </template>
        <el-button
          v-if="
            $checkPermi(['risk:basic:line:export:details']) &&
              ['report'].includes(sourceType) &&
              operType == 'detail'
          "
          type="success"
          icon="el-icon-download"
          @click="handleExportSystem(2)"
        >导出汇总明细
        </el-button>
        <template v-if="taskDetail.riskOperatorGroupId != 0">
          <template v-if="sourceType != 'check'">
          <!-- <el-button
              v-if="source.exportUrl.includes('export')"
              type="success"
              icon="el-icon-download"
              @click="handleExport"
              v-hasPermi="['risk:basic:line:export']"
              >导出当前结果({{ total }})</el-button
            > -->
          <!-- <el-button
              v-if="source.exportUrl.includes('export')"
              type="success"
              icon="el-icon-download"
              @click="handleExport"
              v-hasPermi="['risk:basic:line:export']"
              >导出当前结果({{ total }})</el-button
            >
            <el-button
              v-if="source.exportUrl.includes('adminAndTenantLineListExport')"
              type="success"
              icon="el-icon-download"
              @click="handleExport"
              v-hasPermi="['risk:basic:line:adminAndTenantLineListExport']"
              >导出当前结果({{ total }})</el-button
            > -->
          </template>
        </template>

        <div v-if="ids.length > 0" class="change-input inline-block">共选中 {{ ids.length }} 项</div>
      </div>

      <sa-toolbar class="flex-none" :show-search.sync="showSearch" @queryTable="getList" />
    </div>

    <!-- <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['risk/basic:line:edit']"
          >修改</el-button
        >
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row> -->
    <div v-full:height="autoHeight" class="">
      <el-table
        ref="multipleTableRef"
        v-loading="loading"
        class="sa-table"
        :data="lineList"
        v-bind="{
          ...(autoHeight ? {
            height:'100%'
          }:{})
        }"
        @select="selectRow"
        @select-all="selectAll"
        @selection-change="onSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center" :selectable="selectable" fixed="left" />

        <template v-if="['index', 'basicOverview'].includes(sourceType)">
          <el-table-column
            label="任务名称"
            align="left"
            prop="taskName"
            min-width="120"
            :show-overflow-tooltip="true"
          />

        </template>

        <el-table-column label="处置状态" align="center">
          <template slot-scope="scope">
            <dict-tag :options="dict.type.risk_status" :value="scope.row.status" />
          </template>
        </el-table-column>

        <el-table-column
          v-if="
            (['report'].includes(sourceType) && operType == 'detail') ||
              ['index', 'basicOverview'].includes(sourceType)
          "
          label="分配状态"
          align="center"
        >
          <template slot-scope="scope">
            <dict-tag :options="dict.type.allocation_status" :value="scope.row.dispatchStatus" />
          </template>
        </el-table-column>

        <el-table-column v-if="['index', 'basicOverview'].includes(sourceType)" label="任务类型" align="center" min-width="120">
          <template slot-scope="scope">
            <dict-tag :options="dict.type.main_task_type" :value="scope.row.taskType" />
          </template>
        </el-table-column>

        <el-table-column v-if="layout.includes('approve')" label="修复凭证" align="center">
          <template #default="{ row }">
            <el-link type="primary" @click="$openURL(row.disposalVoucher)"> {{ row.disposalVoucherName }} </el-link>
          </template>
        </el-table-column>

        <el-table-column
          v-if="
            ['index', 'basicOverview'].includes(sourceType) ||
              (['report'].includes(sourceType) && operType == 'detail')
          "
          label="处置分组"
          align="center"
          prop="groupName"
          :show-overflow-tooltip="true"
        >
          <template slot-scope="scope">
            {{ scope.row.dispatchStatus == 1 ? '-' : scope.row.groupName }}
          </template>
        </el-table-column>

        <el-table-column
          label="部门名称"
          align="center"
          prop="deptName"
          width="120"
          :show-overflow-tooltip="true"
        />

        <el-table-column
          label="业务系统"
          align="center"
          prop="standardName"
          width="120"
          :show-overflow-tooltip="true"
        />

        <el-table-column label="资产IP" align="center" width="180">
          <template slot-scope="scope">
            <sa-tooltip :content="scope.row.resourceIp" :is-copy="true" />
          </template>
        </el-table-column>
        <el-table-column
          label="所属资产类型"
          align="center"
          prop="resourceType"
          width="180"
          :show-overflow-tooltip="true"
        />
        <!--      <el-table-column label="检查方法" align="center" min-width="420">
        <template slot-scope="scope">
          <sa-tooltip :content="scope.row.checkMethod" />
        </template>
      </el-table-column>
      <el-table-column label="操作指南" align="center" prop="operateName" min-width="300" />-->
        <el-table-column label="基线要求" align="center" width="180">
          <template slot-scope="scope">
            <sa-tooltip :content="scope.row.requireContent" />
          </template>
        </el-table-column>
        <el-table-column label="检测结果" align="center" min-width="300">
          <template slot-scope="scope">
            <sa-tooltip :content="scope.row.liveNetworkResult" />
          </template>
        </el-table-column>

        <el-table-column label="整改建议" align="center" min-width="300">
          <template slot-scope="scope">
            <sa-tooltip :content="scope.row.repairSug" />
          </template>
        </el-table-column>

        <el-table-column label="风险等级" align="center">
          <template slot-scope="scope">
            <dict-tag :options="dict.type.risk_level" :value="scope.row.riskLevel" />
          </template>
        </el-table-column>

        <el-table-column label="全量重复标记" align="center" width="120">
          <template slot-scope="scope">
            <dict-tag :options="dict.type.risk_repeatFlag" :value="scope.row.historyRepeatFlag" />
          </template>
        </el-table-column>
        <el-table-column label="复现标记" align="center" width="120">
          <template slot-scope="scope">
            <dict-tag :options="dict.type.risk_repeatFlag" :value="scope.row.repeatFlag" />
          </template>
        </el-table-column>

        <el-table-column label="超期标记" align="center" width="120">
          <template #default="{ row }">
            <EleTagDict :value="row.overdueStatus" dict-type="whether" />
          </template>
        </el-table-column>

        <el-table-column label="超期天数" align="center" width="120" prop="overdueDays">
        </el-table-column>

        <!-- <el-table-column label="数据类别" align="center" width="100" prop="dataSourceType">
          <template #default="{ row }">
            <DictTagV2 :value="row.dataSourceType" dict-type="tenantDataType" />
          </template>
        </el-table-column> -->
        <!-- <el-table-column label="所属租户" align="center" width="150" prop="tenName" show-overflow-tooltip /> -->

        <el-table-column label="知识库申请次数" align="center" width="150" prop="knowApplyCount" show-overflow-tooltip />

        <el-table-column label="处置原因" align="center">
          <template #default="{ row }">
            <KnowDisposalTag :value="row.disposalType" :misinformation="['4'].includes(String(row.status))" />
          </template>
        </el-table-column>

        <el-table-column label="审批状态" align="center">
          <template #default="{ row }">
            <dict-tag :options="dict.type.risk_approval_status" :value="row.approvalStatus" />
          </template>
        </el-table-column>

        <el-table-column label="处置时间" align="center" prop="disposeTime" width="200" />

        <el-table-column label="分配时间" align="center" prop="dispatchTime" width="200" />

        <el-table-column label="处置维度" align="center" width="100">
          <template #default="{ row }">
            <EleTagDict :options="dict.type.basic_type_base_dim" :value="row.disposeType" />
          </template>
        </el-table-column>
        <el-table-column label="规则编号" prop="ruleNo" align="center" width="100" show-overflow-tooltip>
        </el-table-column>

        <el-table-column label="业务维护分组名称" align="center" prop="serviceDefend" width="150" />
        <el-table-column label="OS维护分组名称" align="center" prop="osDefend" width="150" />

        <el-table-column label="创建时间" align="center" prop="createTime" width="200" />
        <!-- <el-table-column label="创建人" align="center" prop="createBy" width="120" /> -->
        <!-- <el-table-column label="原报告系统名称" align="center" prop="systemName" min-width="180" /> -->
        <el-table-column fixed="right" label="操作" align="center" width="220">
          <template slot-scope="scope">
            <el-button size="mini" type="text" @click="onDetail(scope.row)">查看</el-button>

            <el-button
              v-if="layout.includes('approve')"
              type="text"
              @click="handleApprove(scope.row)"
            >审批</el-button>

            <template v-if="!['statistics', 'check', 'dispose'].includes(sourceType) && ![3, 4].includes(Number(scope.row.dataSourceType)) && layout.includes('all')">
              <el-button
                v-if="taskDetail.riskOperatorGroupId == 0"
                size="mini"
                type="text"
                @click="onAllocationDetail(2, scope.row)"
              >分配
              </el-button>
              <el-button
                v-if="
                  showHistory ||
                    (scope.row.repeatFlag == 1 && $checkPermi(['risk:basic:line:view:history']) && !['statistics'].includes(sourceType))
                "
                size="mini"
                type="text"
                @click="onHistoryDetail(scope.row)"
              >查看历史
              </el-button>
              <el-button
                v-if="!['index','statistics'].includes(sourceType)"
                v-hasPermi="['risk:basic:line:remove']"
                size="mini"
                type="text"
                @click="handleDelete(scope.row)"
              >删除
              </el-button>
              <template v-if="taskDetail.riskOperatorGroupId != 0 && !['statistics'].includes(sourceType)">
                <template v-if="scope.row.status != 2">
                  <el-button
                    v-hasPermi="['risk:warn:line:dispose']"
                    size="mini"
                    type="text"
                    @click="onOper(scope.row)"
                  >处置
                  </el-button>
                  <template v-if="['index','basicOverview'].includes(sourceType)">
                    <el-button
                      v-if="$checkPermi(['risk:turnoverRecord:line:goBackRiskData'])"
                      size="mini"
                      type="text"
                      @click="goBackRiskData(scope.row)"
                    >退回
                    </el-button>
                    <el-button
                      v-if="$checkPermi(['risk:turnoverRecord:line:transferRiskData'])"
                      size="mini"
                      type="text"
                      @click="onAllocationDetail(2, scope.row, '转派')"
                    >转派
                    </el-button>
                  </template>
                </template>
              </template>
            </template>
            <slot name="table-action-after" v-bind="{ row: scope.row }" />
          <!-- <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['risk/basic:line:edit']"
            >修改</el-button
          >
           -->
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="sa-footer sa-row-center">

      <sa-pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </div>

    <!-- 详情 -->
    <sa-detail ref="detail" :detail-data="detail">
      <template #disposalType="{ data }">
        <KnowDisposalTag :value="data.disposalType" :misinformation="['4'].includes(String(data.status))" />
      </template>
    </sa-detail>

    <!-- 处置 -->
    <oper-detail ref="oper" :oper-data="{...oper, ...(taskDetail.taskId ? { taskId: taskDetail.taskId } : {}) }" @ok="onOperDetail" />

    <history-index
      ref="historyRef"
      :source="source"
      :task-detail="taskDetail"
      :task-type="taskType"
      :basic-type="basicType"
      @close="onClose"
    />

    <!-- 新增或修改基线漏洞对话框 -->
    <el-dialog
      class="add-edit-dialog"
      :title="title"
      :visible.sync="open"
      width="800px"
      append-to-body
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="120px" inline>
        <el-form-item v-if="!['report'].includes(sourceType)" label="任务" prop="taskId">
          <el-select
            ref="ddddddddddddd"
            v-model="form.taskId"
            placeholder="请选择任务"
            filterable
            remote
            reserve-keyword
            :remote-method="remoteMethod"
            @change="onChangeTaskId"
          >
            <el-option
              v-for="dict in taskIdList"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="资产IP" prop="resourceIp">
          <el-input v-model="form.resourceIp" placeholder="请输入资产IP" />
        </el-form-item>
        <el-form-item label="所属资产类型" prop="resourceType">
          <el-input v-model="form.resourceType" placeholder="请输入所属资产类型" />
        </el-form-item>
        <el-form-item label="适用对象类型" prop="objectType">
          <el-input v-model="form.objectType" placeholder="请输入适用对象类型" />
        </el-form-item>
        <el-form-item class="sa-w-full" label="基线要求" prop="requireContent">
          <el-input v-model="form.requireContent" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item class="sa-w-full" label="操作指南" prop="operateName">
          <el-input v-model="form.operateName" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="检测方法" prop="checkMethod">
          <el-input v-model="form.checkMethod" placeholder="请输入检测方法" />
        </el-form-item>
        <el-form-item label="检测结果" prop="liveNetworkResult">
          <el-input v-model="form.liveNetworkResult" placeholder="请输入检测结果" />
        </el-form-item>
        <el-form-item label="是否合规" prop="compliance">
          <el-input v-model="form.compliance" placeholder="请输入是否合规" />
        </el-form-item>
        <el-form-item class="sa-w-full" label="整改建议" prop="repairSug">
          <el-input v-model="form.repairSug" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="发现人" prop="findPeople">
          <el-input v-model="form.findPeople" placeholder="请输入发现人" />
        </el-form-item>
        <el-form-item label="发现时间" prop="findTime">
          <!--          <el-input v-model="form.findTime" placeholder="请输入发现时间" />-->
          <el-date-picker
            v-model="form.findTime"
            clearable
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择时间"
          />
        </el-form-item>
        <!-- <el-form-item label="原报告系统名称" prop="systemName">
            <sa-search
              :searchValue="form.systemName"
              :url="searchUrl"
              filed="systemName"
              @confirm="(val)=>{
                form.systemName = val;
                if(val) {
                  $refs.form.clearValidate('systemName');
                }
              }" />
          </el-form-item> -->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
        <el-button type="primary" @click="submitForm">确 定</el-button>
      </div>
    </el-dialog>

    <allocation-detail
      ref="allocationDetailRef"
      :allocation-detail="allocationDetail"
      :select-other-tenant="!!taskDetail.taskId"
      @ok="onAllocation"
    />

    <BaseLineXmlFileInfoOpen
      ref="baseLineXmlFileInfoRef"
      :base-line-xml-file-info-data="baseLineXmlFileInfoData"
    />

    <el-dialog
      :title="upload.title"
      :visible.sync="upload.open"
      width="400px"
      append-to-body
      @closed="onUploadClosed"
    >
      <div class="" style="padding-bottom: 12px">
        <el-radio-group v-model="upload.handleType">
          <el-radio-button label="fixed">已修复</el-radio-button>
          <el-radio-button label="ignore">暂不处理</el-radio-button>
        </el-radio-group>
      </div>
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        :data="upload.data"
        drag
      >
        <i class="el-icon-upload" />
        <div class="el-upload__text">
          将文件拖到此处，或
          <em>点击上传</em>
        </div>
        <!-- <div slot="tip" class="el-upload__tip">
              <el-link type="info" style="font-size:14px;color:green" @click="importTemplate">点击下载模板</el-link>
          </div> -->
        <div slot="tip" class="el-upload__tip" style="color: red">
          提示：在导出汇总明细数据基础上导入处置结果（编号不能改动并且操作修复结果为“已修复”，其他修复结果则手动操作）最大限制1000条安全问题。
        </div>
      </el-upload>

      <div v-if="upload.handleType === 'ignore'" class="">
        <el-divider content-position="left">附加参数</el-divider>
        <IgnoreForm ref="ignoreFormRef" risk-type="line" />
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="upload.open = false">取 消</el-button>
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
      </div>
    </el-dialog>

    <ApproveDialog ref="approveDialogRef" />

    <InfoDialog ref="infoDialogRef" />
  </div>
</template>

<script>
import { listLine, getLine, delLine, addLine, updateLine } from '@/api/risk/basic/line'
import request from '@/utils/request'
import OperDetail from '../../components/oper.vue'
import IpInput from '@/views/components/ipInput/ipInput.vue'
import AllocationDetail from '../AllocationDetail.vue'
import HistoryIndex from './history-index.vue'
import { checkPermi, checkRole } from '@/utils/permission'
import BaseLineXmlFileInfoOpen from '@/views/risk/baseLineXmlFileInfo/open.vue'
import { getToken } from '@/utils/auth'

import IgnoreForm from '@/views/risk/basic/components/IgnoreForm/index.vue'

import ExportActions from '@/views/risk/basic/components/ExportActions/index.vue'

import ApproveDialog from '@/views/risk/basic/components/ApproveDialog/index.vue'

import StatsPanel from '@/views/risk/basic/components/StatsPanel/index.vue'

import InfoDialog from './InfoDialog/index.vue'

import LineCheckPreview from '@/views/risk/report/task/components/LineCheckPreview/index.vue'

export default {
  name: 'Line',
  dicts: [
    'risk_status',
    'dispose_dimension',
    'risk_repeatFlag',
    'main_task_type',
    'allocation_status',
    'risk_level',
    'risk_approval_status',
    'basic_type_base_dim'
  ],
  components: {
    IpInput,
    OperDetail,
    AllocationDetail,
    HistoryIndex,
    BaseLineXmlFileInfoOpen,
    IgnoreForm,
    ExportActions,
    ApproveDialog,
    StatsPanel,
    InfoDialog,
    LineCheckPreview
  },
  props: {
    layout: {
      type: [Array, String],
      default: 'all'
    },
    sourceType: {
      type: String,
      default: 'index' // index|basicOverview|report|check
    },
    operType: {
      type: String,
      default: '' // allocation|detail
    },
    search: {
      type: Object,
      default: () => ({}) // 搜素条件
    },
    source: {
      type: Object,
      default: () => ({
        listUrl: `/risk/basic/line/list`,
        exportUrl: `/risk/basic/line/export`
      })
    },
    taskDetail: {
      type: Object,
      default: () => ({
        riskOperatorGroupId: null
      })
    },
    taskType: {
      type: Boolean,
      default: true
    },
    showHistory: {
      type: Boolean,
      default: false
    },
    statsFlag: {
      type: Boolean,
      default: false
    },
    autoHeight: {
      type: Boolean,
      default: false
    }
  },

  data() {
    return {
      multipleLength: 0,
      isSystem: true,
      isRequire: true,
      // 提示输入框数据
      systemData: [],
      requireData: [],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 选中的行
      selection: [],
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 基线漏洞表格数据
      lineList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 导入参数
      upload: {
        // 是否显示弹出层
        open: false,
        // 弹出层标题
        title: '',
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: { Authorization: 'Bearer ' + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + '/risk/basic/line/groupImportData',

        handleType: 'fixed',
        data: {}
      },
      searchMore: false,
      // 查询参数
      lazyQueryParams: void 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        taskName: null,
        taskType: null,
        resourceIp: null,
        systemName: null,
        deptName: void 0,
        standardName: void 0,
        resourceType: null,
        status: null,
        requireContent: null,
        startIp: null,
        endIp: null,
        repeatFlag: null,
        riskOperatorGroupId: null,
        dispatchStatus: null,
        riskLevel: null,
        historyRepeatFlag: void 0,
        statusList: [],
        overdueStatus: void 0
      },
      daterange: [],
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        taskType: [{ required: true, message: '请选择任务类型', trigger: 'blur' }],
        taskId: [{ required: true, message: '请选择任务', trigger: 'blur' }],
        taskName: [{ required: true, message: '请输入任务名称', trigger: 'blur' }],
        resourceIp: [{ required: true, message: '请输入资产IP', trigger: 'blur' }],
        // systemName: [{ required: true, message: '请输入原报告系统名称', trigger: 'blur' }],
        resourceType: [{ required: true, message: '请输入所属资产类型', trigger: 'blur' }],
        objectType: [{ required: true, message: '请输入适用对象类型', trigger: 'blur' }],
        liveNetworkResult: [{ required: true, message: '请输入检测结果', trigger: 'blur' }],
        requireContent: [{ required: true, message: '请输入内容', trigger: 'blur' }],
        operateName: [{ required: true, message: '请输入内容', trigger: 'blur' }],
        checkMethod: [{ required: true, message: '请输入检测方法', trigger: 'blur' }],
        compliance: [{ required: true, message: '请输入是否合规', trigger: 'blur' }],
        repairSug: [{ required: true, message: '请输入内容', trigger: 'blur' }],
        findPeople: [{ required: true, message: '请输入发现人', trigger: 'blur' }],
        findTime: [{ required: true, message: '请输入发现时间', trigger: 'blur' }]
      },

      detail: {
        data: {},
        formLabel: [
          {
            label: '任务类型',
            field: 'taskType',
            type: 'main_task_type',
            row: 6
          },
          {
            label: '任务名称',
            field: 'taskName',
            row: 6
          },
          {
            label: '资产IP',
            field: 'resourceIp',
            row: 6
          },
          {
            label: '所属资产类型',
            field: 'resourceType',
            row: 6
          },
          {
            label: '业务系统',
            field: 'standardName',
            row: 6
          },
          {
            label: '原报告系统名称',
            field: 'systemName',
            row: 6
          },
          {
            label: '部门名称',
            field: 'deptName',
            row: 12
          },
          {
            label: '处置状态',
            field: 'status',
            type: 'risk_status',
            row: 6
          },
          {
            label: '创建时间',
            field: 'createTime',
            row: 6
          },
          {
            label: '基线要求',
            field: 'requireContent',
            row: 12
          },
          {
            label: '检查方法',
            field: 'checkMethod',
            row: 12
          },
          {
            label: '操作指南',
            field: 'operateName',
            row: 12
          },
          {
            label: '检测结果',
            field: 'liveNetworkResult',
            row: 12
          },
          {
            label: '整改建议',
            field: 'repairSug',
            row: 12
          }
        ],
        isShowOper: true,
        riskType: '2',
        isRecord: true
      },
      oper: {
        data: {},
        isRecord: true
      },
      taskIdList: [],
      taskName: null,
      isShowOper: true,
      allocationDetail: {},
      taskId: 0,
      repeatFlag: 0,

      statisticsList: [
        {
          type: '1',
          title: '未修复',
          num: 0,
          img: require('@/assets/images/task-processed.png')
        },
        {
          type: '2',
          title: '已修复',
          num: 0,
          img: require('@/assets/images/task-unprocessed.png')
        },
        {
          type: '3',
          title: '暂不处理',
          num: 0,
          img: require('@/assets/images/task-leave-aside.png')
        },
        {
          type: '4',
          title: '误报',
          num: 0,
          img: require('@/assets/images/task-white-list.png')
        },
        {
          type: '5',
          title: '待确认',
          num: 0,
          img: require('@/assets/images/task-repeat.png')
        }
      ],

      basicType: {
        queryType: null,
        queryBasicType: null
      },

      baseLineXmlFileInfoData: {
        search: {}
      },

      riskOperatorGroupIdList: [],

      systemNameList: [],

      searchUrl: ''
    }
  },
  computed: {
    tableData() {
      return this.lineList
    }
  },
  created() {
    this.$on('on-stats-click', () => {
      this.$emit('update:stats-flag', !this.statsFlag)
    })

    if (['index'].includes(this.$props.sourceType)) {
      this.queryParams.statusList = ['1', '5']
    }

    this.queryParams = { ...this.queryParams, ...this.$props.taskDetail }

    if (this.$props.sourceType === 'index') {
      this.getBasicAlarm()
      this.basicType = {
        queryType: 3,
        queryBasicType: 2
      }
    }

    if (['basicOverview', 'check', 'report'].includes(this.$props.sourceType)) {
      this.basicType = {
        queryType: 1,
        queryBasicType: 2
      }
      if (this.$route.path.includes('/tso/')) {
        this.basicType = {
          queryType: 2,
          queryBasicType: 2
        }
      }
    }

    if (['basicOverview', 'check', 'index', 'statistics', 'report'].includes(this.$props.sourceType)) {
      this.queryParams = { ...this.queryParams, ...this.$props.search }
    }

    this.searchUrl = `/risk/basic/line/querySystemNameList?queryType=${this.basicType.queryType}&queryBasicType=2`

    this.getQueryAllGroupNameList()

    this.getSystemNameList()

    this.getList()

    this.initSearch()
  },

  methods: {
    validateSource() {
      for (let index = 0; index < this.selection.length; index++) {
        const item = this.selection[index]
        if ([3, 4].includes(Number(item.dataSourceType))) {
          throw new Error(`安全问题：${item.taskName} 中包含租户转出或租户授权的数据，暂无法操作`)
        }
      }
    },
    initSearch() {
      this.$nextTick(() => {
        if (!this.searchMore) {
          let clientWidth = this.$refs.queryForm.$el.clientWidth
          let idx1
          let idx2
          for (const i in this.$refs.queryForm.$children) {
            if (clientWidth - this.$refs.queryForm.$children[i].$el.clientWidth - 24 < 0) {
              idx1 = i - 1
              break
            }
            clientWidth = clientWidth - this.$refs.queryForm.$children[i].$el.clientWidth - 24
          }
          clientWidth = this.$refs.queryForm.$el.clientWidth - 200
          for (const i in this.$refs.queryForm.$children) {
            if (i > idx1) {
              if (clientWidth - this.$refs.queryForm.$children[i].$el.clientWidth - 24 < 0) {
                idx2 = i - 1
                break
              }
              clientWidth = clientWidth - this.$refs.queryForm.$children[i].$el.clientWidth - 24
            }
          }
          for (const i in this.$refs.queryForm.$children) {
            if (i > idx2 && i != this.$refs.queryForm.$children.length - 1) {
              this.$refs.queryForm.$children[i].$el.style.display = 'none'
            }
          }
        } else {
          for (const i in this.$refs.queryForm.$children) {
            this.$refs.queryForm.$children[i].$el.style.display = 'flex'
          }
        }
      })
    },
    checkPermi,
    checkRole,
    /** 查询基线漏洞列表 */
    getList() {
      this.loading = true
      this.queryParams = {
        ...this.queryParams,
        ...this.basicType
      }
      // statusList 会自动转所以为了不影响其他数据做了一个深拷贝
      const paramsCopy = JSON.parse(JSON.stringify(this.queryParams))
      paramsCopy.statusList = paramsCopy.statusList.join(',')
      paramsCopy.riskOperatorGroupId = (paramsCopy.riskOperatorGroupId || []).join(',')
      if (paramsCopy.statusList == '') {
        paramsCopy.statusList = null
      }
      this.lazyQueryParams = { ...paramsCopy }
      request({
        url: this.$props.source.listUrl,
        method: 'get',
        params: paramsCopy
      }).then((response) => {
        this.lineList = response.rows
        this.total = response.total
        this.loading = false
        this.$nextTick(() => {
          this.lineList.forEach((l) => {
            if (this.ids?.includes(l.id)) {
              this.$refs.multipleTableRef.toggleRowSelection(l, true)
            }
          })
        })
      })

      this.$nextTick(() => {
        this.$refs.statsPanelRef?.reload?.()
      })
    },

    // 多资源IP
    inputHandler() {
      this.queryParams.resourceIp = this.queryParams.resourceIp.replace(/，/, ',')
    },

    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        taskId: null,
        taskName: null,
        taskType: null,
        resourceIp: null,
        systemId: null,
        systemName: null,
        resourceType: null,
        objectType: null,
        requireContent: null,
        operateName: null,
        checkMethod: null,
        compliance: null,
        repairSug: null,
        findPeople: null,
        findTime: null,
        repeatFlag: null,
        riskOperatorGroupId: null,
        disposeId: null,
        disposeName: null,
        status: null,
        remark: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null
      }
      if (['report'].includes(this.$props.sourceType)) {
        this.form = {
          ...this.form,
          taskId: this.$props.taskDetail.taskId,
          taskName: this.$props.taskDetail.taskName,
          taskType: this.$props.taskDetail.taskType
        }
      }

      if ('statistics'.includes(this.$props.sourceType)) {
        this.form = {
          ...this.form,
          ...this.$props.search
        }
      }

      this.resetForm('form')
    },
    // 失去焦点触发
    onBlurSystem() {
      setTimeout(() => {
        this.isSystem = false
      }, 500)
    },
    // 输入框改变
    onChangeSystem(val) {
      this.isSystem = true
      request({
        url: `${this.searchUrl}`,
        method: 'get'
        // params: {
        //   riskType: 1,
        //   riskWarnType: 20005,
        //   field: 'systemName',
        //   value: val,
        // },
      }).then((response) => {
        if (!response) {
          this.systemData = []
        } else {
          this.systemData = response.data
        }
      })
    },
    onSystemMsg(val) {
      this.isSystem = false
      this.queryParams.systemName = val
    },
    // 输入框改变
    onChangeRequire(val) {
      this.isRequire = true
      request({
        url: `/risk/warn/hint/search`,
        method: 'get',
        params: {
          riskType: 1,
          riskWarnType: 20005,
          field: 'requireContent',
          value: val
        }
      }).then((response) => {
        if (!response) {
          this.requireData = []
        } else {
          this.requireData = response.data
        }
      })
    },
    // 失去焦点触发
    onBlurRequire() {
      setTimeout(() => {
        this.isRequire = false
      }, 500)
    },
    onRequireMsg(val) {
      this.isRequire = false
      this.queryParams.requireContent = val
    },
    // 查看历史
    onHistoryDetail(row) {
      this.taskDetail.resourceIp = row.resourceIp
      this.taskDetail.requireContent = row.requireContent
      this.taskId = this.taskDetail.taskId
      this.repeatFlag = this.taskDetail.repeatFlag
      delete this.taskDetail.taskId
      delete this.taskDetail.repeatFlag
      delete this.taskDetail.statusList
      this.$refs.historyRef.show()
    },
    // 关闭查看历史
    onClose() {
      this.taskDetail.resourceIp = ''
      this.taskDetail.requireContent = ''
      this.taskDetail.taskId = this.taskId
      this.taskDetail.repeatFlag = this.repeatFlag
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.ids = []
      this.getList()
      if (this.$props.sourceType === 'index') {
        this.getBasicAlarm()
      }
    },
    // 获取开始ip
    getStartIp(val) {
      if (val == '...') {
        val = null
      }
      this.queryParams.startIp = val
    },
    // 获取结束ip
    getEndIp(val) {
      if (val == '...') {
        val = null
      }
      this.queryParams.endIp = val
      this.getList()
    },
    /** TODO 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.queryParams.startIp = ''
      this.queryParams.endIp = ''
      this.queryParams.systemName = ''
      this.queryParams.standardName = ''
      this.daterange = []
      this.onChangeDaterange()
      this.handleQuery()
      this.$refs.ipRef.reset()
    },
    // 多选框选中
    selectRow(selection, row) {
      if (this.ids.includes(row.id)) {
        const index = this.ids.findIndex((id) => id == row.id)
        this.ids.splice(index, 1)
      } else {
        this.ids.push(row.id)
      }
      this.multipleLength = this.ids.length
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    // 选中当前页面所有数据
    selectAll(selection) {
      if (selection.length == 0) {
        this.lineList.forEach((l) => {
          if (this.ids.includes(l.id)) {
            const index = this.ids.findIndex((id) => id == l.id)
            this.ids.splice(index, 1)
          }
        })
      } else {
        selection.forEach((l) => {
          if (!this.ids.includes(l.id)) {
            this.ids.push(l.id)
          }
        })
      }
      this.multipleLength = this.ids.length
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    onSelectionChange(selection) {
      this.selection = selection
    },
    // 分配
    onAllocation() {
      this.multipleLength = 0
      this.ids = []
      this.multiple = true
      this.queryParams.pageNum = 1
      this.getList()
      if (this.$props.sourceType === 'index') {
        this.getBasicAlarm()
      }
      this.$emit('confirm')
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = `新增${this.$route.meta.title}`
      this.getTaskListByType()
    },
    /** 修改按钮操作 */
    // handleUpdate(row) {
    //   this.reset();
    //   const id = row.id || this.ids;
    //   getLine(id).then((response) => {
    //     this.form = response.data;
    //     this.open = true;
    //     this.title = `修改${this.$route.meta.title}`;
    //   });
    // },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateLine(this.form).then((response) => {
              this.$modal.msgSuccess('修改成功')
              this.open = false
              this.getList()
              if (this.$props.sourceType === 'index') {
                this.getBasicAlarm()
              }
            })
          } else {
            addLine(this.form).then((response) => {
              this.$modal.msgSuccess('新增成功')
              this.open = false
              this.getList()
              if (this.$props.sourceType === 'index') {
                this.getBasicAlarm()
              }
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids
      this.$modal
        .confirm(`是否确认删除${row.taskName || '当前所选'}数据项？`)
        .then(function() {
          return delLine(ids)
        })
        .then(() => {
          this.multiple = true
          this.ids = []
          this.multipleLength = 0
          this.queryParams.pageNum = 1
          this.getList()
          if (this.$props.sourceType === 'index') {
            this.getBasicAlarm()
          }
          this.$modal.msgSuccess('删除成功')
          this.$emit('confirm')
        })
        .catch(() => {})
    },
    /** 导出按钮操作 */
    handleExport() {
      if (this.total > 200000) {
        this.$modal.msgWarning('抱歉，导出数据量过大，请选择筛选条件后再进行导出')
        return false
      }

      const submitAuery = JSON.parse(JSON.stringify(this.queryParams))
      delete submitAuery.pageNum
      delete submitAuery.pageSize
      submitAuery.statusList = submitAuery.statusList.join()

      this.download(
        this.$props.source.exportUrl,
        {
          ...submitAuery,
          ...(this.$props.source?.exportParams || {}),
          ids: this.ids
        },
        `导出${this.$route.meta.title}_${new Date().getTime()}.xlsx`
      )
    },
    onDetail(row) {
      this.$refs.infoDialogRef.open(row)
    },
    // onDetail(row) {
    //   request({
    //     url: `/risk/basic/line/${row.id}`,
    //     method: 'get'
    //   }).then((response) => {
    //     this.detail.data = response.data
    //     // this.detail.data = row;
    //     // 暂不处理时 显示最终修复时间和申请附件
    //     if (row.status == 3) {
    //       this.detail.formLabel = [
    //         {
    //           label: '任务类型',
    //           field: 'taskType',
    //           type: 'main_task_type',
    //           row: 6
    //         },
    //         {
    //           label: '任务名称',
    //           field: 'taskName',
    //           row: 6
    //         },
    //         {
    //           label: '资产IP',
    //           field: 'resourceIp',
    //           row: 6
    //         },
    //         {
    //           label: '所属资产类型',
    //           field: 'resourceType',
    //           row: 6
    //         },
    //         {
    //           label: '标准系统名称',
    //           field: 'standardName',
    //           row: 6
    //         },
    //         {
    //           label: '原报告系统名称',
    //           field: 'systemName',
    //           row: 6
    //         },
    //         {
    //           label: '部门名称',
    //           field: 'deptName',
    //           row: 12
    //         },
    //         {
    //           label: '处置状态',
    //           field: 'status',
    //           type: 'risk_status',
    //           row: 6
    //         },
    //         {
    //           label: '创建时间',
    //           field: 'createTime',
    //           row: 6
    //         },
    //         {
    //           label: '基线要求',
    //           field: 'requireContent',
    //           row: 12
    //         },
    //         {
    //           label: '检查方法',
    //           field: 'checkMethod',
    //           row: 12
    //         },
    //         {
    //           label: '操作指南',
    //           field: 'operateName',
    //           row: 12
    //         },
    //         {
    //           label: '检测结果',
    //           field: 'liveNetworkResult',
    //           row: 12
    //         },
    //         {
    //           label: '整改建议',
    //           field: 'repairSug',
    //           row: 12
    //         },
    //         {
    //           label: '最终修复时间',
    //           field: 'repairTime'
    //         },
    //         {
    //           label: '申请附件',
    //           field: 'accessoryName',
    //           isTooltip: true
    //         }
    //       ]
    //     }

    //     if (this.detail?.data?.disposalVoucherName) {
    //       this.detail.formLabel.push({
    //         label: '修复凭证',
    //         field: 'disposalVoucherName',
    //         isTooltip: true
    //       })
    //     }

    //     if (this.detail?.data?.disposalType || ['3'].includes(this.detail?.data?.status)) {
    //       this.detail.formLabel.push({
    //         label: '处置原因',
    //         field: 'disposalType'
    //       })
    //     }

    //     this.$refs.detail.show()
    //   })
    // },
    async onOper(row = {}) {
      try {
        await this.validateSource()
      } catch (error) {
        this.$message.warning(error.message)
        return false
      }

      const data = {
        ids: row.id?.toString().split(',') || this.ids,
        disposeStatus: row.status || '',
        remarks: row.remarks || ''
      }

      const rows = row?.id ? [row] : this.selection
      const defaultRangeInfluence = [...new Set(rows.flatMap(item => [item.resourceIp]).filter(Boolean))]

      this.oper.data = {
        riskType: '1',
        riskWarnType: 20005,
        riskContainerSafetyType: '',
        ...data,
        riskType2: '2',
        defaultRangeInfluence: defaultRangeInfluence.join(',')
      }

      this.oper.isRecord = !!row.id

      await this.$nextTick()

      this.$refs.oper.show()
    },
    onOperDetail() {
      this.$refs.multipleTableRef.clearSelection()
      this.multipleLength = 0
      this.ids = []
      this.multiple = true
      this.queryParams.pageNum = 1
      this.getList()
      if (this.$props.sourceType === 'index') {
        this.getBasicAlarm()
      }
      this.$emit('confirm')
    },
    getTaskListByType() {
      if (this.form.taskType) {
        request({
          url: '/risk/report/task/getTaskListByType',
          method: 'get',
          params: {
            taskType: this.form.taskType,
            taskName: this.taskName
          }
        }).then((response) => {
          this.taskIdList = response.data
        })
      }
    },
    onChangeTaskId() {
      this.$nextTick(() => {
        this.form.taskName = this.$refs.ddddddddddddd.selectedLabel
      })
    },
    remoteMethod(val) {
      this.taskName = val
      this.getTaskListByType()
    },
    selectable(row) {
      return true
    },
    onChangeDaterange() {
      if (this.daterange && this.daterange.length > 0) {
        this.queryParams.startTime = this.daterange[0]
        this.queryParams.endTime = this.daterange[1]
      } else {
        delete this.queryParams.startTime
        delete this.queryParams.endTime
      }
    },
    async onAllocationDetail(type, row, title) {
      try {
        await this.validateSource()
      } catch (error) {
        this.$message.warning(error.message)
        return false
      }

      const queryBo = JSON.parse(JSON.stringify(this.queryParams))
      delete queryBo.pageNum
      delete queryBo.pageSize

      let ids = []
      if (type == 2) {
        ids = row ? [row.id] : this.ids
      }
      this.allocationDetail = {
        riskWarnType: '20005',
        type: type,
        taskId: this.$props.taskDetail.taskId,
        ids,
        title: title || '分配',
        riskType: '2',
        lineQueryBo: queryBo
      }
      this.$refs.allocationDetailRef.show()
    },
    async goBackRiskData(row) {
      try {
        await this.validateSource()
      } catch (error) {
        this.$message.warning(error.message)
        return false
      }
      const ids = row.id || this.ids.join(',')
      this.$modal.confirm(`是否确认退回`).then(() => {
        request({
          url: '/risk/turnoverRecord/goBackRiskData',
          method: 'post',
          data: {
            riskType: '2',
            ids
          }
        }).then((response) => {
          this.$modal.msgSuccess(response.msg)
          this.multiple = true
          this.ids = []
          this.multipleLength = 0
          this.queryParams.pageNum = 1
          this.getList()
          if (this.$props.sourceType === 'index') {
            this.getBasicAlarm()
          }
        })
      })
    },
    getBasicAlarm() {
      // const params = {
      //   type: '2',
      //   queryType: 3,
      //   ...this.queryParams
      // }
      // delete params.pageNum
      // delete params.pageSize
      // request({
      //   url: '/risk/census/basicAlarm',
      //   method: 'post',
      //   data: params
      // }).then((response) => {
      //   this.statisticsList.forEach((item) => {
      //     item.num = response.data.find((i) => i.status == item.type)?.num || 0
      //   })
      // })
    },
    async onOpenBaseLineXmlFileInfo() {
      this.baseLineXmlFileInfoData.search = {
        taskId: this.$props.taskDetail.taskId
      }
      this.$refs.baseLineXmlFileInfoRef.show()
    },
    getQueryAllGroupNameList() {
      request({
        url: '/risk/operator/groupUser/queryAllGroupNameList',
        method: 'get'
      }).then((response) => {
        this.riskOperatorGroupIdList = response.data
      })
    },
    getSystemNameList() {
      request({
        url: `${this.searchUrl}`,
        method: 'get'
      }).then((response) => {
        this.systemNameList = response.data
      })
    },
    async handleExportSystem(type) {
      let url
      if (type == 1) {
        url = '/risk/basic/line/groupExport'
      } else if (type == 2) {
        url = '/risk/basic/line/adminAndTenantLineGroupExport'
      }
      if (this.total > 200000) {
        this.$modal.msgWarning('抱歉，导出数据量过大，请选择筛选条件后再进行导出')
        return false
      }

      const submitAuery = JSON.parse(JSON.stringify(this.queryParams))
      delete submitAuery.pageNum
      delete submitAuery.pageSize
      submitAuery.statusList = submitAuery.statusList.join()

      if (submitAuery.riskOperatorGroupId) {
        submitAuery.riskOperatorGroupId = submitAuery.riskOperatorGroupId.join()
      }

      this.download(
        url,
        {
          ...submitAuery
        },
        `导出汇总明细_${new Date().getTime()}.xlsx`
      )
    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = '导入处置结果'
      this.upload.open = true
    },
    /** 下载模板操作 */
    importTemplate() {
      importTemplate().then((response) => {
        this.download(response.msg)
      })
    },
    /** 文件上传中处理*/
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true
    },
    /** 文件上传成功处理*/
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false
      this.upload.isUploading = false
      this.$refs.upload.clearFiles()
      this.$alert(response.msg, '导入结果', { dangerouslyUseHTMLString: true })
      this.getList()
    },
    /** 提交上传文件*/
    async submitFileForm() {
      if (!this.$refs.upload.uploadFiles.length) {
        this.$message.warning('请先导入处置结果')
        return false
      }

      if (this.upload.handleType === 'ignore') {
        try {
          this.upload.data = await this.$refs.ignoreFormRef.submit()
        } catch (error) {
          console.warn(error.message)
          return false
        }
      }

      await this.$nextTick()

      this.$refs.upload.submit()
    },
    onUploadClosed() {
      this.upload = this.$options.data().upload
    },
    handleApprove(row) {
      const ids = row?.id ? [row.id] : this.ids

      this.$refs.approveDialogRef.open({
        params: {
          riskWarnType: '20005',
          ids
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
  .change-input {
    margin-left: 12px;
  }

  .prompt {
    position: relative;

    .prompt-box {
      position: absolute;
      left: -28px;
      top: 40px;
    }

    .prompt-box-content {
      position: relative;
      width: 200px;
      height: auto;
      z-index: 100;
      border: solid 1px #dfe4ed;
      border-radius: 4px;
      background-color: #ffffff;
      padding: 8px;
      white-space: nowrap;
      overflow-x: scroll;
    }

    .ovflow-y {
      height: 260px;
      overflow-y: scroll;
    }

    .prompt-box-sanjiao {
      position: absolute;
      top: -9px;
      left: 50%;
      transform: translateX(-50%);
      z-index: 101;
    }

    .sanjiao {
      width: 0;
      height: 0;
      border-style: solid;
      border-width: 0 10px 10px;
      border-color: transparent transparent #dfe4ed;
      position: relative;
    }

    .sanjiao::after {
      content: '';
      border-style: solid;
      border-width: 0 9px 9px;
      border-color: transparent transparent #fff;
      position: absolute;
      top: 1px;
      left: -9px;
    }

    .input-msg-item {
      padding-bottom: 6px;
      // border-bottom: 1px solid #f0f0f0;
      cursor: pointer;
    }
  }

  .statistics-item {
    height: 80px;
    border-radius: 8px;
    background: #ffffff;
    box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.04);
    margin: 0 0 16px;
    padding: 10px 20px 20px;

    .num {
      font-family: OPPOSans;
      font-size: 24px;
      font-weight: 900;
      line-height: 36px;
      color: #3e4040;
    }

    .title {
      font-size: 14px;
      font-weight: 400;
      line-height: 18px;
      color: #636466;

      img {
        flex-shrink: 0;
        width: 16px;
        height: 16px;
        margin-right: 4px;
      }
    }
  }

  @media only screen and (min-width: 992px) {
    .el-col-md-6 {
      width: 20%;
    }
  }
</style>
