<template>
  <div class="h-full" :class="['detail', 'allocation', 'basicOverview'].includes(sourceType) ? 'page-main' : ''">
    <StatsPanel
      v-if="sourceType == 'index' && lazyQueryParams"
      ref="statsPanelRef"
      :key="JSON.stringify(lazyQueryParams)"
      :params="{
        type: '1',
        queryType: 3,
        ...lazyQueryParams,
      }"
      class="!overflow-hidden !transition-all"
      :class="statsFlag ? '!h-[96px]' : '!h-0'"
    />

    <el-form
      v-if="!['statistics'].includes(sourceType)"
      v-show="showSearch"
      ref="queryForm"
      class="sa-query"
      :model="queryParams"
      size="small"
      :inline="true"
      label-width="68px"
    >
      <!-- <el-form-item class="" label="数据类别" prop="dataSourceType">
          <DictSelect v-model="queryParams.dataSourceType" dict-type="tenantDataType" />
        </el-form-item>
        <el-form-item class="" label="所属租户" prop="tenId">
          <TenantSelect
            ref="tenantSelectRef"
            v-model="queryParams.tenId"
            @change="handleQuery"
          />
        </el-form-item> -->
      <!-- <div class="sa-query-item sa-flex sa-flex-wrap" :class="searchMore ? 'sa-query-item1' : 'sa-query-item2'"> -->
      <template v-if="['index', 'basicOverview'].includes(sourceType)">
        <el-form-item label="任务名称" prop="taskName">
          <el-input
            v-model="queryParams.taskName"
            placeholder="请输入任务名称"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="任务类型" prop="taskType">
          <el-select
            v-model="queryParams.taskType"
            placeholder="请选择任务类型"
            clearable
            @change="handleQuery"
          >
            <template v-for="dict in dict.type.main_task_type">
              <el-option
                v-if="dict.value == 1 || dict.value == 3"
                :label="dict.label"
                :value="dict.value"
              />
            </template>
          </el-select>
        </el-form-item>
      </template>

      <el-form-item v-if="!['dispose'].includes(sourceType)" label="部门名称" prop="tenantName">
        <CommonDepartmentSelect
          v-model="queryParams.tenantName"
          placeholder="请输入"
          clearable
          return-name
          @change="()=> queryParams.systemName = void 0"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item v-if="!['dispose'].includes(sourceType)" label="业务系统" prop="systemName">
        <CommonSystemSelect
          v-model="queryParams.systemName"
          placeholder="请输入"
          return-name
          clearable
          :params="{
            deptName: queryParams.tenantName
          }"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item v-if="!['dispose'].includes(sourceType)" label="漏洞名称" prop="leakName">
        <sa-search
          :search-value="queryParams.leakName"
          :url="searchUrl"
          filed="leakName"
          @confirm="(val) => (queryParams.leakName = val)"
        />
      </el-form-item>
      <el-form-item label="CVE编号" prop="cveNum">
        <div class="prompt">
          <el-input
            slot="reference"
            v-model="queryParams.cveNum"
            placeholder="请输入CVE编号"
            clearable
            @blur="onBlurSearch"
            @input="onInputSearch($event, 'cveNum')"
            @keyup.enter.native="handleQuery"
          />
          <div v-if="hintSearchList.length > 0 && isHintSearch == 'cveNum'" class="prompt-box">
            <div class="prompt-box-sanjiao">
              <div class="sanjiao" />
            </div>
            <div class="prompt-box-content" :class="{ 'ovflow-y': hintSearchList.length > 6 }">
              <div
                v-for="(item, index) in hintSearchList"
                :key="index"
                class="input-msg-item"
                @click="onHintSearch(item, 'cveNum')"
              >
                {{ item }}
              </div>
            </div>
          </div>
        </div>
      </el-form-item>
      <el-form-item v-if="!['dispose'].includes(sourceType)" label="风险等级" prop="riskLevel">
        <el-select
          v-model="queryParams.riskLevel"
          placeholder="请选择风险等级"
          clearable
          @change="handleQuery"
        >
          <el-option
            v-for="dict in dict.type.risk_level"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item v-if="!['dispose'].includes(sourceType)" label="处置状态" prop="statusList">
        <el-select
          v-model="queryParams.statusList"
          multiple
          collapse-tags
          placeholder="请选择处置状态"
          clearable
          @change="handleQuery"
        >
          <el-option
            v-for="dict in dict.type.risk_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item
        v-if="
          (['report'].includes(sourceType) && ['detail','allocation'].includes(operType)) ||
            ['index', 'basicOverview'].includes(sourceType)
        "
        label="分配状态"
        prop="dispatchStatus"
      >
        <el-select
          v-model="queryParams.dispatchStatus"
          placeholder="请选择分配状态"
          clearable
          filterable
          @change="handleQuery"
        >
          <el-option
            v-for="dict in dict.type.allocation_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item v-if="!['dispose'].includes(sourceType)" label="全量重复标记" prop="historyRepeatFlag">
        <el-select
          v-model="queryParams.historyRepeatFlag"
          placeholder="请选择全量重复标记"
          clearable
          class="el-select-highlight"
          @change="handleQuery"
        >
          <el-option
            v-for="dict in dict.type.risk_repeatFlag"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item v-if="!['dispose'].includes(sourceType)" label="复现标记" prop="repeatFlag">
        <el-select
          v-model="queryParams.repeatFlag"
          placeholder="请选择复现标记"
          clearable
          class="el-select-highlight"
          @change="handleQuery"
        >
          <el-option
            v-for="dict in dict.type.risk_repeatFlag"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item v-if="!['dispose'].includes(sourceType)" label="超期标记" prop="overdueStatus">
        <EleSelectDict v-model="queryParams.overdueStatus" dict-type="whether" @change="handleQuery" />
      </el-form-item>

      <el-form-item v-if="!['dispose'].includes(sourceType)" class="daterange" label="创建时间" prop="daterange">
        <el-date-picker
          v-model="daterange"
          type="daterange"
          value-format="yyyy-MM-dd"
          format="yyyy-MM-dd"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="onChangeDaterange"
        />
      </el-form-item>

      <el-form-item v-if="!['dispose'].includes(sourceType)" label="IP区间" class="ip-item">
        <ip-input
          ref="ipRef"
          @startIp="getStartIp($event, 'startIp')"
          @endIp="getEndIp($event, 'endIp')"
        />
      </el-form-item>

      <el-form-item v-if="!['dispose'].includes(sourceType)" label="IP" prop="ip" class="ip">
        <el-input
          v-model="queryParams.ip"
          placeholder="IP(多IP请用&quot;,&quot;分开)"
          clearable
          @input="onInputIp('ip')"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item v-if="!['dispose'].includes(sourceType)" label="资源池名称" prop="poolName">
        <div class="prompt">
          <el-input
            slot="reference"
            v-model="queryParams.poolName"
            placeholder="请输入资源池名称"
            clearable
            @blur="onBlurSearch"
            @input="onInputSearch($event, 'poolName')"
            @keyup.enter.native="handleQuery"
          />
          <div v-if="hintSearchList.length > 0 && isHintSearch == 'poolName'" class="prompt-box">
            <div class="prompt-box-sanjiao">
              <div class="sanjiao" />
            </div>
            <div class="prompt-box-content" :class="{ 'ovflow-y': hintSearchList.length > 6 }">
              <div
                v-for="(item, index) in hintSearchList"
                :key="index"
                class="input-msg-item"
                @click="onHintSearch(item, 'poolName')"
              >
                {{ item }}
              </div>
            </div>
          </div>
        </div>
      </el-form-item>
      <el-form-item
        v-if="
          ['index', 'basicOverview'].includes(sourceType) ||
            (['report'].includes(sourceType) && operType == 'detail')
        "
        label="处置分组"
        prop="riskOperatorGroupId"
      >
        <el-select
          v-model="queryParams.riskOperatorGroupId"
          placeholder="请选择处置分组"
          clearable
          filterable
          multiple
        >
          <el-option
            v-for="item in riskOperatorGroupIdList"
            :key="item.id"
            :label="item.groupName"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item v-if="!['dispose'].includes(sourceType)" label="集群名称" prop="clusterName">
        <el-input
          v-model="queryParams.clusterName"
          placeholder="请输入集群名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item v-if="!['dispose'].includes(sourceType)" label="端口" prop="port">
        <el-input
          v-model="queryParams.port"
          placeholder="请输入端口"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item v-if="!['dispose'].includes(sourceType)" label="发现方式" prop="findMethod">
        <el-input
          v-model="queryParams.findMethod"
          placeholder="请输入发现方式"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <!-- <el-form-item label="负责团队" prop="deptName">
          <div class="prompt">
            <el-input
              v-model="queryParams.deptName"
              placeholder="请输入负责团队"
              clearable
              slot="reference"
              @blur="onBlurSearch"
              @input="onInputSearch($event, 'deptName')"
              @keyup.enter.native="handleQuery"
            />
            <div
              class="prompt-box"
              v-if="hintSearchList.length > 0 && isHintSearch == 'deptName'"
            >
              <div class="prompt-box-sanjiao">
                <div class="sanjiao"></div>
              </div>
              <div class="prompt-box-content" :class="{ 'ovflow-y': hintSearchList.length > 6 }">
                <div
                  class="input-msg-item"
                  v-for="(item, index) in hintSearchList"
                  @click="onHintSearch(item, 'deptName')"
                  :key="index"
                >
                  {{ item }}
                </div>
              </div>
            </div>
          </div>
        </el-form-item> -->
      <!-- </div> -->
      <el-form-item class="query-handle">
        <el-button type="primary" plain icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh-left" @click="resetQuery">重置</el-button>
        <EleSheetSearchMore :parent-ref="()=> $refs.queryForm" />
      </el-form-item>
    </el-form>

    <div class="sa-title flex items-center">

      <div class="space-x-2 flex-1 w-0">
        <template v-if="taskDetail.riskOperatorGroupId != 0">
          <template v-if="sourceType != 'check'" class="">
            <!-- <el-button
              v-if="taskDetail.taskType != 2"
              type="primary"
              icon="el-icon-plus"
              @click="handleAdd"
              v-hasPermi="['risk:basic:system:add']"
              >新增</el-button
            > -->
            <!-- <el-button
              v-if="source.exportUrl.includes('export')"
              type="primary"
              icon="el-icon-download"
              @click="handleExport"
              v-hasPermi="['risk:basic:system:export']"
              >导出
            </el-button>
            <el-button
              v-if="source.exportUrl.includes('adminAndTenantSystemListExport')"
              type="primary"
              icon="el-icon-download"
              @click="handleExport"
              v-hasPermi="['risk:basic:system:adminAndTenantSystemListExport']"
              >导出
            </el-button> -->

            <ExportActions
              v-bind="{
                queryParams,
                source,
                sourceType,
                basicType: 'system'
              }"
            />

            <el-button
              v-if="layout.includes('approve')"
              type="success"
              icon="el-icon-s-check"
              :disabled="multiple"
              @click="handleApprove()"
            >审批</el-button>

            <el-button
              v-if="taskDetail.taskType != 2 && !['statistics', 'dispose'].includes(sourceType) && layout.includes('all')"
              v-hasPermi="['risk:warn:system:dispose']"
              type="primary"
              :disabled="multiple"
              @click="onOper()"
            ><IconHandyman class="" />处置
            </el-button>
          <!-- <el-button
              v-if="!['index'].includes(sourceType) && taskDetail.taskType != 2"
              type="danger"
              plain
              :disabled="multiple"
              @click="handleDelete"
              v-hasPermi="['risk:basic:system:remove']"
              ><i class="iconfont icon-piliangshanchu"></i>批量删除</el-button
            > -->
          </template>
        </template>

        <template v-if="!['statistics', 'dispose'].includes(sourceType) && layout.includes('all')">
          <template v-if="taskDetail.riskOperatorGroupId == 0" class="">
            <el-popover v-if="$checkPermi(['risk:task:security:issue:batch:distribution'])" placement="top-start" trigger="hover" content="列表所选中的进行分配">
              <el-button
                slot="reference"
                type="warning"
                :disabled="ids.length == 0"
                @click="onAllocationDetail(2)"
              ><i class="iconfont icon-piliangfenpei1" />批量分配
              </el-button>
            </el-popover>
            <el-popover v-if="$checkPermi(['risk:task:security:issue:full:allocation']) && ['report'].includes(sourceType)" placement="top-start" trigger="hover" content="当前条件查询的进行分配">
              <el-button
                slot="reference"
                class=""
                type="warning"
                :disabled="systemList.length == 0"
                @click="onAllocationDetail(1)"
              ><i class="iconfont icon-piliangfenpei" />全量分配
              </el-button>
            </el-popover>
          </template>
          <template v-else>
            <el-popover v-if="$checkPermi(['risk:basic:system:batch:distribution'])" placement="top-start" trigger="hover" content="列表所选中的进行分配">
              <el-button
                slot="reference"
                type="warning"
                :disabled="ids.length == 0"
                @click="onAllocationDetail(2)"
              ><i class="iconfont icon-piliangfenpei1" />批量分配
              </el-button>
            </el-popover>
            <el-popover v-if="$checkPermi(['risk:basic:system:full:allocation']) && ['report'].includes(sourceType)" placement="top-start" trigger="hover" content="当前条件查询的进行分配">
              <el-button
                slot="reference"
                class=""
                type="warning"
                :disabled="systemList.length == 0"
                @click="onAllocationDetail(1)"
              ><i class="iconfont icon-piliangfenpei" />全量分配
              </el-button>
            </el-popover>
          </template>
        </template>

        <el-button
          v-if="taskDetail.riskOperatorGroupId != 0 && $checkPermi(['risk:turnoverRecord:system:goBackRiskData']) && !['statistics', 'dispose'].includes(sourceType) && layout.includes('all')"
          type="warning"
          :disabled="multiple"
          @click="goBackRiskData"
        ><icon-undo class="el-icon" />退回
        </el-button>

        <template v-if="['index', 'basicOverview'].includes(sourceType)">
          <el-button
            v-if="$checkPermi(['risk:turnoverRecord:system:transferRiskData'])"
            type="warning"
            :disabled="multiple"
            @click="onAllocationDetail(2, '', '转派')"
          ><IconForward class="" />转派
          </el-button>
          <el-button
            v-hasPermi="['risk:basic:system:group:export']"
            type="primary"
            icon="el-icon-download"
            @click="handleExportSystem(1)"
          >导出汇总明细
          </el-button>
          <el-button
            v-hasPermi="['risk:basic:system:group:importData']"
            type="primary"
            icon="el-icon-upload2"
            @click="handleImport"
          >导入处置结果
          </el-button>

          <el-button
            v-hasPermi="['risk:basic:system:group:clusterBug']"
            type="primary"
            @click="handleClusterBug"
          ><IconBarChart class="" />集群漏洞数统计
          </el-button>
        </template>

        <el-button
          v-if="
            $checkPermi(['risk:basic:system:export:details']) &&
              ['report'].includes(sourceType) &&
              operType == 'detail' &&
              layout.includes('all')
          "
          type="success"
          icon="el-icon-download"
          @click="handleExportSystem(2)"
        >导出汇总明细
        </el-button>
        <div v-if="ids.length > 0" class="change-input inline-block">共选中 {{ ids.length }} 项</div>
      </div>

      <sa-toolbar :show-search.sync="showSearch" class="flex-none" @queryTable="getList" />
    </div>

    <div v-full:height="autoHeight" class="">
      <el-table
        ref="multipleTableRef"
        v-loading="loading"
        class="sa-table"
        v-bind="{
          ...(autoHeight ? {
            height:'100%'
          }:{})
        }"
        :data="systemList"
        @select="selectRow"
        @select-all="selectAll"
        @selection-change="onSelectionChange"
      >
        <el-table-column v-if="!['dispose'].includes(sourceType)" type="selection" width="55" align="center" :selectable="selectable" fixed="left" />

        <template v-if="['index', 'basicOverview'].includes(sourceType)">
          <el-table-column
            label="任务名称"
            align="left"
            prop="taskName"
            min-width="120"
            :show-overflow-tooltip="true"
          />
          <el-table-column label="任务类型" align="center" min-width="120">
            <template slot-scope="scope">
              <dict-tag :options="dict.type.main_task_type" :value="scope.row.taskType" />
            </template>
          </el-table-column>
        </template>

        <el-table-column v-if="layout.includes('approve')" label="修复凭证" align="center">
          <template #default="{ row }">
            <el-link type="primary" @click="$openURL(row.disposalVoucher)"> {{ row.disposalVoucherName }} </el-link>
          </template>
        </el-table-column>

        <el-table-column label="处置状态" align="center">
          <template slot-scope="scope">
            <dict-tag :options="dict.type.risk_status" :value="scope.row.status" />
          </template>
        </el-table-column>

        <el-table-column
          v-if="
            (['report'].includes(sourceType) && operType == 'detail') ||
              ['index', 'basicOverview'].includes(sourceType)
          "
          label="分配状态"
          align="center"
        >
          <template slot-scope="scope">
            <dict-tag :options="dict.type.allocation_status" :value="scope.row.dispatchStatus" />
          </template>
        </el-table-column>

        <el-table-column label="业务部门" align="center" prop="tenantName" width="160" :show-overflow-tooltip="true" />
        <el-table-column label="业务系统" align="center" prop="systemName" width="200" :show-overflow-tooltip="true" />

        <el-table-column label="风险等级" align="center" width="140">
          <template slot-scope="scope">
            <dict-tag :options="dict.type.risk_level" :value="scope.row.riskLevel" />
          </template>
        </el-table-column>

        <el-table-column label="漏洞名称" align="center" prop="leakName" width="240" :show-overflow-tooltip="true" />
        <el-table-column label="CVE编号" align="center" prop="cveNum" width="160" :show-overflow-tooltip="true" />
        <el-table-column label="资源池名称" align="center" prop="poolName" width="120" :show-overflow-tooltip="true" />

        <el-table-column label="POD名称" align="center" prop="podName" width="120" :show-overflow-tooltip="true" />

        <el-table-column label="端口" align="center" prop="port" width="100" />

        <el-table-column label="扫描IP" align="center" width="180">
          <template slot-scope="scope">
            <sa-tooltip :content="scope.row.ipAddress" :is-copy="true" />
          </template>
        </el-table-column>

        <el-table-column label="业务IP" align="center" width="180">
          <template slot-scope="scope">
            <sa-tooltip :content="scope.row.businessIp" :is-copy="true" />
          </template>
        </el-table-column>
        <el-table-column label="管理IP" align="center" width="180">
          <template slot-scope="scope">
            <sa-tooltip :content="scope.row.manageIp" :is-copy="true" />
          </template>
        </el-table-column>
        <!-- <el-table-column label="pod" align="center" prop="podName" width="160" :show-overflow-tooltip="true"/> -->
        <el-table-column label="集群名称" align="center" prop="clusterName" width="160" :show-overflow-tooltip="true" />

        <!-- <el-table-column label="检测时间" align="center" prop="findTime" width="180"/> -->

        <el-table-column
          v-if="['index', 'basicOverview'].includes(sourceType) || (['report'].includes(sourceType) && operType == 'detail')"
          label="处置分组"
          align="center"
          prop="groupName"
          :show-overflow-tooltip="true"
        >
          <template slot-scope="scope">
            {{ scope.row.dispatchStatus == 1 ? '-' : scope.row.groupName }}
          </template>
        </el-table-column>

        <el-table-column label="处置原因" align="center">
          <template #default="{ row }">
            <KnowDisposalTag :value="row.disposalType" :misinformation="['4'].includes(String(row.status))" />
          </template>
        </el-table-column>

        <el-table-column label="全量重复标记" align="center" width="120">
          <template slot-scope="scope">
            <dict-tag :options="dict.type.risk_repeatFlag" :value="scope.row.historyRepeatFlag" />
          </template>
        </el-table-column>
        <el-table-column label="复现标记" align="center" width="120">
          <template slot-scope="scope">
            <dict-tag :options="dict.type.risk_repeatFlag" :value="scope.row.repeatFlag" />
          </template>
        </el-table-column>

        <el-table-column label="超期标记" align="center" width="120">
          <template #default="{ row }">
            <EleTagDict :value="row.overdueStatus" dict-type="whether" />
          </template>
        </el-table-column>

        <el-table-column label="超期天数" align="center" width="120" prop="overdueDays">
        </el-table-column>

        <el-table-column label="知识库申请次数" align="center" width="150" prop="knowApplyCount" show-overflow-tooltip />

        <!-- <el-table-column label="数据类别" align="center" width="100" prop="dataSourceType">
          <template #default="{ row }">
            <DictTagV2 :value="row.dataSourceType" dict-type="tenantDataType" />
          </template>
        </el-table-column>
        <el-table-column label="所属租户" align="center" width="150" prop="tenName" show-overflow-tooltip /> -->
        <el-table-column label="审批状态" align="center">
          <template #default="{ row }">
            <dict-tag :options="dict.type.risk_approval_status" :value="row.approvalStatus" />
          </template>
        </el-table-column>

        <el-table-column label="处置时间" align="center" prop="disposeTime" width="200" />

        <el-table-column label="分配时间" align="center" prop="dispatchTime" width="200" />

        <el-table-column label="处置维度" align="center" width="100">
          <template #default="{ row }">
            <EleTagDict :options="dict.type.basic_type_base_dim" :value="row.disposeType" />
          </template>
        </el-table-column>
        <el-table-column label="规则编号" prop="ruleNo" align="center" width="100" show-overflow-tooltip>
        </el-table-column>

        <el-table-column label="业务维护分组名称" align="center" prop="serviceDefend" width="150" />
        <el-table-column label="OS维护分组名称" align="center" prop="osDefend" width="150" />

        <el-table-column label="创建时间" align="center" prop="createTime" width="200" />

        <el-table-column fixed="right" label="操作" align="center" width="220">
          <template slot-scope="scope">
            <el-button v-if="$checkPermi(['risk:basic:system:query'])" size="mini" type="text" @click="onDetail(scope.row)">查看</el-button>

            <el-button
              v-if="layout.includes('approve')"
              type="text"
              @click="handleApprove(scope.row)"
            >审批</el-button>

            <template v-if="!['check', 'statistics', 'dispose'].includes(sourceType) && ![3, 4].includes(Number(scope.row.dataSourceType)) && layout.includes('all')">
              <el-button
                v-if="taskDetail.riskOperatorGroupId == 0"
                size="mini"
                type="text"
                @click="onAllocationDetail(2, scope.row)"
              >分配
              </el-button>
              <!-- <el-button
                v-if="!scope.row.deptName && !scope.row.systemName && !scope.row.poolName && $checkPermi(['risk:basic:system:complete']) && !['statistics'].includes(sourceType)"
                size="mini"
                type="text"
                @click="onAddMsg()"
              >补录信息
              </el-button> -->
              <el-button
                v-if="
                  showHistory ||
                    (scope.row.repeatFlag == 1 && checkPermi(['risk:basic:system:view:history']) && !['statistics'].includes(sourceType))
                "
                v-hasPermi="['risk:basic:system:view:history']"
                size="mini"
                type="text"
                @click="onHistoryDetail(scope.row)"
              >查看历史
              </el-button>
              <el-button
                v-if="!['index', 'statistics', 'dispose'].includes(sourceType)"
                v-hasPermi="['risk:basic:system:remove']"
                size="mini"
                type="text"
                @click="handleDelete(scope.row)"
              >删除
              </el-button>
              <template v-if="taskDetail.riskOperatorGroupId != 0 && !['statistics'].includes(sourceType)">
                <template v-if="scope.row.status != 2">
                  <el-button
                    v-hasPermi="['risk:warn:system:dispose']"
                    size="mini"
                    type="text"
                    @click="onOper(scope.row)"
                  >处置
                  </el-button>
                  <template v-if="['index', 'basicOverview'].includes(sourceType)">
                    <el-button
                      v-if="$checkPermi(['risk:turnoverRecord:system:goBackRiskData'])"
                      size="mini"
                      type="text"
                      @click="goBackRiskData(scope.row)"
                    >退回
                    </el-button>
                    <el-button
                      v-if="$checkPermi(['risk:turnoverRecord:system:transferRiskData'])"
                      size="mini"
                      type="text"
                      @click="onAllocationDetail(2, scope.row, '转派')"
                    >转派
                    </el-button>
                  </template>
                </template>
              </template>
            </template>
            <slot name="table-action-after" v-bind="{ row: scope.row }" />
          <!-- <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['risk:basic:system:edit']"
            >修改</el-button
          >-->
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="sa-footer sa-row-center">

      <sa-pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </div>

    <!-- 详情 -->
    <sa-detail ref="detail" :detail-data="detail">
      <template #disposalType="{ data }">
        <KnowDisposalTag :value="data.disposalType" :misinformation="['4'].includes(String(data.status))" />
      </template>
    </sa-detail>

    <!-- 处置 -->
    <oper-detail ref="oper" :oper-data="{...oper, ...(taskDetail.taskId ? { taskId: taskDetail.taskId } : {}) }" @ok="onOperDetail" />

    <history-index
      ref="historyRef"
      :source="source"
      :task-detail="taskDetail"
      :task-type="taskType"
      :basic-type="basicType"
      @close="onClose"
    />

    <!-- 补录系统漏洞对话框 -->
    <el-dialog title="补录信息" :visible.sync="isShowAdd" width="800px" append-to-body>
      <el-form ref="addForm" :model="addForm" :rules="addRules" label-width="120px">
        <el-form-item label="资源主机名称" prop="name">
          <el-input v-model="addForm.name" placeholder="请输入资源主机名称" />
        </el-form-item>
        <el-form-item label="资源池名" prop="resPoolName">
          <el-input v-model="addForm.resPoolName" placeholder="请输入资源池名" />
        </el-form-item>
        <el-form-item label="POD名称" prop="resPodName">
          <el-input v-model="addForm.resPodName" placeholder="请输入POD名称" />
        </el-form-item>
        <el-form-item label="资源组名称" prop="resGroupName">
          <el-input v-model="addForm.resGroupName" placeholder="请输入资源组名称" />
        </el-form-item>
        <el-form-item label="业务系统" prop="sysName">
          <el-input v-model="addForm.sysName" placeholder="请输入业务系统" />
        </el-form-item>
        <el-form-item label="手动维护的集群名称" prop="manualClusterName">
          <el-input
            v-model="addForm.manualClusterName"
            type="textarea"
            placeholder="请输入内容"
          />
        </el-form-item>
        <el-form-item label="操作系统" prop="operatingSystem">
          <el-input v-model="addForm.operatingSystem" placeholder="请输入操作系统" />
        </el-form-item>
        <el-form-item label="安全域" prop="securityDomain">
          <el-input v-model="addForm.securityDomain" placeholder="请输入安全域" />
        </el-form-item>
        <el-form-item label="组件" prop="assembly">
          <el-input v-model="addForm.assembly" placeholder="请输入组件" />
        </el-form-item>
        <el-form-item label="产品" prop="product">
          <el-input v-model="addForm.product" placeholder="请输入产品" />
        </el-form-item>
        <el-form-item label="内存" prop="memory">
          <el-input v-model="addForm.memory" type="number" placeholder="请输入内存" />
        </el-form-item>
        <el-form-item label="快存储" prop="blockStorage">
          <el-input v-model="addForm.blockStorage" type="number" placeholder="请输入快存储" />
        </el-form-item>
        <el-form-item label="磁盘" prop="disk">
          <el-input v-model="addForm.disk" type="number" placeholder="请输入磁盘" />
        </el-form-item>
        <el-form-item label="cpu核数" prop="cpuCores">
          <el-input v-model="addForm.cpuCores" type="number" placeholder="请输入cpu核数" />
        </el-form-item>
        <el-form-item label="vpc名称" prop="vpcName">
          <el-input v-model="addForm.vpcName" placeholder="请输入vpc名称" />
        </el-form-item>
        <el-form-item label="是否4A接入" prop="isFourA">
          <el-input v-model="addForm.isFourA" placeholder="请输入是否4A接入" />
        </el-form-item>
        <el-form-item label="vip" prop="vip">
          <el-input v-model="addForm.vip" placeholder="请输入vip" />
        </el-form-item>
        <el-form-item label="管理网IP" prop="managementIp">
          <el-input v-model="addForm.managementIp" placeholder="请输入管理网IP" />
        </el-form-item>
        <el-form-item label="存储网IP" prop="storageIp">
          <el-input v-model="addForm.storageIp" placeholder="请输入存储网IP" />
        </el-form-item>
        <el-form-item label="业务网IP" prop="serviceIp">
          <el-input v-model="addForm.serviceIp" placeholder="请输入业务网IP" />
        </el-form-item>
        <el-form-item label="承载网IP" prop="bearerIp">
          <el-input v-model="addForm.bearerIp" placeholder="请输入承载网IP" />
        </el-form-item>
        <el-form-item label="是否交维" prop="transfer">
          <el-input v-model="addForm.transfer" placeholder="请输入是否交维" />
        </el-form-item>
        <el-form-item label="负责团队" prop="deptName">
          <el-input v-model="addForm.deptName" placeholder="请输入负责团队" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelAdd">取 消</el-button>
        <el-button type="primary" @click="submitAddForm">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 新增或修改系统漏洞对话框 -->
    <el-dialog
      class="add-edit-dialog"
      :title="title"
      :visible.sync="open"
      width="800px"
      append-to-body
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="120px" inline>
        <el-form-item v-if="!['report'].includes(sourceType)" label="任务" prop="taskId">
          <el-select
            ref="taskIdRef"
            v-model="form.taskId"
            placeholder="请选择任务"
            filterable
            remote
            reserve-keyword
            :remote-method="remoteMethod"
            @change="onChangeTaskId"
          >
            <el-option
              v-for="dict in taskIdList"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="业务系统" prop="systemName">
          <el-input v-model="form.systemName" placeholder="请输入业务系统" />
        </el-form-item>
        <el-form-item label="资源池名称" prop="poolName">
          <el-input v-model="form.poolName" placeholder="请输入资源池名称" />
        </el-form-item>
        <el-form-item label="POD名称" prop="podName">
          <el-input v-model="form.podName" placeholder="请输入POD名称" />
        </el-form-item>
        <el-form-item label="集群名称" prop="clusterName">
          <el-input v-model="form.clusterName" placeholder="请输入集群名称" />
        </el-form-item>
        <el-form-item label="业务IP" prop="businessIp">
          <el-input v-model="form.businessIp" placeholder="请输入业务IP" />
        </el-form-item>
        <el-form-item label="管理IP" prop="manageIp">
          <el-input v-model="form.manageIp" placeholder="请输入管理IP" />
        </el-form-item>
        <el-form-item label="端口" prop="port">
          <el-input v-model="form.port" placeholder="请输入端口" />
        </el-form-item>
        <el-form-item label="IP地址" prop="ipAddress">
          <el-input v-model="form.ipAddress" placeholder="请输入IP地址" />
        </el-form-item>
        <el-form-item class="sa-w-full" label="漏洞名称" prop="leakName">
          <el-input v-model="form.leakName" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="风险等级" prop="riskLevel">
          <el-select v-model="form.riskLevel" placeholder="请选择风险等级" clearable>
            <el-option
              v-for="dict in dict.type.risk_level"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item class="sa-w-full" label="漏洞描述" prop="leakContent">
          <el-input v-model="form.leakContent" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item class="sa-w-full" label="加固建议" prop="repairSug">
          <el-input v-model="form.repairSug" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="CVE编号" prop="cveNum">
          <el-input v-model="form.cveNum" placeholder="请输入CVE编号" />
        </el-form-item>
        <el-form-item label="发现方式" prop="findMethod">
          <el-input v-model="form.findMethod" placeholder="请输入发现方式" />
        </el-form-item>
        <el-form-item label="发现人" prop="findPeople">
          <el-input v-model="form.findPeople" placeholder="请输入发现人" />
        </el-form-item>
        <el-form-item label="发现时间" prop="findTime">
          <!--<el-input v-model="form.findTime" placeholder="请输入发现时间" />-->
          <el-date-picker
            v-model="form.findTime"
            clearable
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择时间"
          />
        </el-form-item>
        <!-- <el-form-item label="负责团队" prop="deptName">
            <el-input v-model="form.deptName" placeholder="请输入负责团队" />
          </el-form-item> -->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
        <el-button type="primary" @click="submitForm">确 定</el-button>
      </div>
    </el-dialog>

    <allocation-detail
      ref="allocationDetailRef"
      :allocation-detail="allocationDetail"
      :select-other-tenant="!!taskDetail.taskId"
      @ok="onAllocation"
    />

    <el-dialog
      :title="upload.title"
      :visible.sync="upload.open"
      width="400px"
      append-to-body
      @closed="onUploadClosed"
    >
      <div class="" style="padding-bottom: 12px">
        <el-radio-group v-model="upload.handleType">
          <el-radio-button label="fixed">已修复</el-radio-button>
          <el-radio-button label="ignore">暂不处理</el-radio-button>
        </el-radio-group>
      </div>

      <el-upload
        v-if="upload.open"
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        :on-change="(file) => upload.file = file"
        :data="upload.data"
        drag
      >
        <i class="el-icon-upload" />
        <div class="el-upload__text">
          将文件拖到此处，或
          <em>点击上传</em>
        </div>
        <!-- <div slot="tip" class="el-upload__tip">
              <el-link type="info" style="font-size:14px;color:green" @click="importTemplate">点击下载模板</el-link>
          </div> -->
        <div slot="tip" class="el-upload__tip" style="color: red">
          提示：在导出汇总明细数据基础上导入处置结果（编号不能改动并且操作修复结果为“已修复”，其他修复结果则手动操作）最大限制1000条安全问题。
        </div>
      </el-upload>

      <div v-if="upload.handleType === 'ignore'" class="">
        <el-divider content-position="left">附加参数</el-divider>
        <IgnoreForm
          ref="ignoreFormRef"
          risk-type="system"
          :dispose-result-file="upload.file"
        />
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="upload.open = false, upload.file = void 0">取 消</el-button>
        <el-button type="primary" :loading="upload.loading" @click="submitFileForm">确 定</el-button>
      </div>
    </el-dialog>

    <ClusterBugDialog ref="clusterBugDialogRef" />

    <ApproveDialog ref="approveDialogRef" />

    <InfoDialog ref="infoDialogRef" />
  </div>
</template>

<script>
import {
  listSystem,
  getSystem,
  delSystem,
  addSystem,
  updateSystem
} from '@/api/risk/basic/system'
import request from '@/utils/request'
import OperDetail from '../../components/oper.vue'
import AllocationDetail from '../AllocationDetail.vue'
import IpInput from '@/views/components/ipInput/ipInput.vue'
import HistoryIndex from './history-index.vue'
import { checkPermi, checkRole } from '@/utils/permission'
import { getToken } from '@/utils/auth'
import ClusterBugDialog from './ClusterBugDialog/index.vue'

import IgnoreForm from '@/views/risk/basic/components/IgnoreForm/index.vue'

import ExportActions from '@/views/risk/basic/components/ExportActions/index.vue'

import ApproveDialog from '@/views/risk/basic/components/ApproveDialog/index.vue'

import StatsPanel from '@/views/risk/basic/components/StatsPanel/index.vue'

import InfoDialog from './InfoDialog/index.vue'
export default {
  name: 'System',
  dicts: [
    'risk_status',
    'risk_level',
    'dispose_dimension',
    'risk_repeatFlag',
    'main_task_type',
    'allocation_status',
    'risk_repeatFlag',
    'risk_approval_status',
    'basic_type_base_dim'
  ],
  components: {
    StatsPanel,
    IpInput,
    OperDetail,
    AllocationDetail,
    HistoryIndex,
    ClusterBugDialog,
    IgnoreForm,
    ExportActions,
    ApproveDialog,
    InfoDialog
  },
  props: {
    layout: {
      type: [Array, String],
      default: 'all'
    },
    sourceType: {
      type: String,
      default: 'index' // index|basicOverview|report|check|BasicSystemDetail|statistics
    },
    operType: {
      type: String,
      default: '' // allocation|detail
    },
    search: {
      type: Object,
      default: () => ({}) // 搜素条件
    },
    source: {
      type: Object,
      default: () => ({
        listUrl: `/risk/basic/system/list`,
        exportUrl: `/risk/basic/system/export`
      })
    },
    taskDetail: {
      type: Object,
      default: () => ({
        riskOperatorGroupId: null
      })
    },
    taskType: {
      type: Boolean,
      default: true
    },
    showHistory: {
      type: Boolean,
      default: false
    },
    statsFlag: {
      type: Boolean,
      default: false
    },
    autoHeight: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      multipleLength: 0,
      isInputMsg: true,
      isCve: true,
      // 提示输入框数据
      cveData: [],
      leakData: [],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 选中的行
      selection: [],
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 系统漏洞表格数据
      systemList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 导入参数
      upload: {
        // 是否显示弹出层
        open: false,
        // 弹出层标题
        title: '',
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: { Authorization: 'Bearer ' + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + '/risk/basic/system/groupImportData',

        handleType: 'fixed',
        data: {},

        file: void 0,

        loading: false
      },
      // 查询参数
      lazyQueryParams: void 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        taskName: null,
        taskType: null,
        status: null,
        leakName: null,
        businessIp: null,
        riskLevel: null,
        cveNum: null,
        startIp: null,
        endIp: null,
        manageIp: null,
        startManageIp: null,
        endManageIp: null,
        repeatFlag: null,
        tenantName: void 0,
        systemName: void 0,
        riskOperatorGroupId: void 0,
        clusterName: null,
        dispatchStatus: null,
        port: null,
        findMethod: null,
        historyRepeatFlag: void 0,
        statusList: [],
        overdueStatus: void 0
      },
      daterange: [],
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        taskType: [{ required: true, message: '请选择任务类型', trigger: 'blur' }],
        taskId: [{ required: true, message: '请选择任务', trigger: 'blur' }],
        taskName: [{ required: true, message: '请输入任务名称', trigger: 'blur' }],
        // deptName: [{ required: true, message: '请输入负责团队', trigger: 'blur' }],
        systemName: [{ required: true, message: '请输入业务系统', trigger: 'blur' }],
        poolName: [{ required: true, message: '请输入资源池名称', trigger: 'blur' }],
        podName: [{ required: true, message: '请输入POD名称', trigger: 'blur' }],
        clusterName: [{ required: true, message: '请输入集群名称', trigger: 'blur' }],
        businessIp: [{ required: true, message: '请输入业务IP', trigger: 'blur' }],
        manageIp: [{ required: true, message: '请输入管理IP', trigger: 'blur' }],
        port: [{ required: true, message: '请输入端口', trigger: 'blur' }],
        ipAddress: [{ required: true, message: '请输入IP地址', trigger: 'blur' }],
        leakName: [{ required: true, message: '请输入内容', trigger: 'blur' }],
        riskLevel: [{ required: true, message: '请选择风险等级', trigger: 'blur' }],
        leakContent: [{ required: true, message: '请输入内容', trigger: 'blur' }],
        repairSug: [{ required: true, message: '请输入内容', trigger: 'blur' }],
        cveNum: [{ required: true, message: '请输入CVE编号', trigger: 'blur' }],
        findMethod: [{ required: true, message: '请输入发现方式', trigger: 'blur' }],
        findPeople: [{ required: true, message: '请输入发现人', trigger: 'blur' }],
        findTime: [{ required: true, message: '请输入发现时间', trigger: 'blur' }]
      },

      // 表单参数
      addForm: {},
      // 表单校验
      addRules: {
        // 表单校验
        name: [{ required: true, message: '资源主机名称不能为空', trigger: 'blur' }],
        resPoolName: [{ required: true, message: '资源池名称不能为空', trigger: 'blur' }],
        resPodName: [{ required: true, message: 'POD名称不能为空', trigger: 'blur' }],
        resGroupName: [{ required: true, message: '资源组名称不能为空', trigger: 'blur' }],
        deptName: [{ required: true, message: '负责团队不能为空', trigger: 'blur' }],
        serviceIp: [{ required: true, message: '业务网IP不能为空', trigger: 'blur' }]
      },
      detail: {
        data: {},
        formLabel: [
          {
            label: '任务名称',
            field: 'taskName',
            row: 6
          },
          {
            label: '任务类型',
            field: 'taskType',
            type: 'main_task_type',
            row: 6
          },
          {
            label: '业务系统',
            field: 'systemName',
            row: 6
          },
          {
            label: '业务部门',
            field: 'tenantName',
            row: 6
          },
          {
            label: '业务IP',
            field: 'businessIp',
            row: 6
          },
          {
            label: 'IP地址',
            field: 'ipAddress',
            row: 6
          },
          {
            label: '管理IP',
            field: 'manageIp',
            row: 6
          },
          {
            label: '端口',
            field: 'port',
            row: 6
          },
          {
            label: '集群名称',
            field: 'clusterName',
            row: 6
          },
          {
            label: 'CVE编号',
            field: 'cveNum',
            row: 6
          },
          {
            label: 'POD',
            field: 'podName',
            row: 6
          },
          {
            label: '发现方式',
            field: 'findMethod',
            row: 6
          },
          {
            label: '风险等级',
            field: 'riskLevel',
            type: 'risk_level',
            row: 6
          },
          {
            label: '处置状态',
            field: 'status',
            type: 'risk_status',
            row: 6
          },
          {
            label: '创建时间',
            field: 'createTime',
            row: 6
          },
          {
            label: '检测时间',
            field: 'findTime',
            row: 6
          },
          {
            label: '资源池名称',
            field: 'poolName',
            row: 6
          },
          {
            label: '负责团队',
            field: 'deptName',
            row: 6
          },
          {
            label: '漏洞名称',
            field: 'leakName',
            row: 12
          },
          {
            label: '漏洞描述',
            field: 'leakContent',
            row: 24
          },
          {
            label: '加固建议',
            field: 'repairSug',
            row: 24
          }
        ],
        isShowOper: true,
        riskType: '1',
        isRecord: true
      },
      oper: {
        data: {},
        isRecord: true
      },

      taskIdList: [],
      taskName: null,

      allocationDetail: {},
      // 是否显示补录弹出层
      isShowAdd: false,
      taskId: 0,
      repeatFlag: 0,

      searchMore: false,
      hintSearchList: [],
      isHintSearch: null,

      statisticsList: [
        {
          type: '1',
          title: '未修复',
          num: 0,
          img: require('@/assets/images/task-processed.png')
        },
        {
          type: '2',
          title: '已修复',
          num: 0,
          img: require('@/assets/images/task-unprocessed.png')
        },
        {
          type: '3',
          title: '暂不处理',
          num: 0,
          img: require('@/assets/images/task-leave-aside.png')
        },
        {
          type: '4',
          title: '误报',
          num: 0,
          img: require('@/assets/images/task-white-list.png')
        },
        {
          type: '5',
          title: '待确认',
          num: 0,
          img: require('@/assets/images/task-repeat.png')
        },
        {
          type: '6',
          title: '提前结单',
          num: 0,
          img: require('@/assets/images/task-repeat.png')
        },
        {
          type: '7',
          title: '已作废',
          num: 0,
          img: require('@/assets/images/task-repeat.png')
        }
      ],

      basicType: {
        queryType: null,
        queryBasicType: null
      },

      riskOperatorGroupIdList: [],

      systemNameList: [],

      leakNameList: [],

      searchUrl: ''
    }
  },
  computed: {
    tableData() {
      return this.systemList
    }
  },
  created() {
    this.$on('on-stats-click', () => {
      this.$emit('update:stats-flag', !this.statsFlag)
    })

    if (['index'].includes(this.$props.sourceType)) {
      this.queryParams.statusList = ['1', '5']
    }

    this.queryParams = { ...this.queryParams, ...this.$props.taskDetail }
    if (this.$props.sourceType === 'index') {
      this.getBasicAlarm()
      this.basicType = {
        queryType: 3,
        queryBasicType: 1
      }
    }
    if (['basicOverview', 'check', 'report'].includes(this.$props.sourceType)) {
      this.basicType = {
        queryType: 1,
        queryBasicType: 1
      }
      if (this.$route.path.includes('/tso/')) {
        this.basicType = {
          queryType: 2,
          queryBasicType: 1
        }
      }
    }

    if (['basicOverview', 'check', 'index', 'statistics', 'report'].includes(this.$props.sourceType)) {
      this.queryParams = { ...this.queryParams, ...this.$props.search }
    }

    this.searchUrl = `/risk/basic/system/querySystemNameAndLeakNameList?queryType=${this.basicType.queryType}&queryBasicType=1`

    this.getQueryAllGroupNameList()

    this.getLeakNameList()

    this.getList()

    this.initSearch()
  },
  methods: {
    validateSource() {
      for (let index = 0; index < this.selection.length; index++) {
        const item = this.selection[index]
        if ([3, 4].includes(Number(item.dataSourceType))) {
          throw new Error(`安全问题：${item.taskName} 中包含租户转出或租户授权的数据，暂无法操作`)
        }
      }
    },
    onUploadClosed() {
      this.upload = this.$options.data().upload
    },
    initSearch() {
      this.$nextTick(() => {
        if (!this.searchMore) {
          let clientWidth = this.$refs.queryForm.$el.clientWidth
          let idx1
          let idx2
          for (const i in this.$refs.queryForm.$children) {
            if (clientWidth - this.$refs.queryForm.$children[i].$el.clientWidth - 24 < 0) {
              idx1 = i - 1
              break
            }
            clientWidth = clientWidth - this.$refs.queryForm.$children[i].$el.clientWidth - 24
          }
          clientWidth = this.$refs.queryForm.$el.clientWidth - 200
          for (const i in this.$refs.queryForm.$children) {
            if (i > idx1) {
              if (clientWidth - this.$refs.queryForm.$children[i].$el.clientWidth - 24 < 0) {
                idx2 = i - 1
                break
              }
              clientWidth = clientWidth - this.$refs.queryForm.$children[i].$el.clientWidth - 24
            }
          }
          for (const i in this.$refs.queryForm.$children) {
            if (i > idx2 && i != this.$refs.queryForm.$children.length - 1) {
              this.$refs.queryForm.$children[i].$el.style.display = 'none'
            }
          }
        } else {
          for (const i in this.$refs.queryForm.$children) {
            this.$refs.queryForm.$children[i].$el.style.display = 'flex'
          }
        }
      })
    },
    checkPermi,
    checkRole,
    /** 查询系统漏洞列表 */
    getList() {
      this.loading = true
      this.queryParams = {
        ...this.queryParams,
        ...this.basicType
      }
      // statusList 会自动转所以为了不影响其他数据做了一个深拷贝
      const paramsCopy = JSON.parse(JSON.stringify(this.queryParams))
      paramsCopy.statusList = paramsCopy.statusList.join(',')
      paramsCopy.riskOperatorGroupId = (paramsCopy.riskOperatorGroupId || []).join(',')
      if (paramsCopy.statusList == '') {
        paramsCopy.statusList = null
      }

      this.lazyQueryParams = { ...paramsCopy }
      request({
        url: this.$props.source.listUrl,
        method: 'get',
        params: paramsCopy
      }).then((response) => {
        if (response.code == 200) {
          this.systemList = response.rows
          this.total = response.total
          this.$nextTick(() => {
            this.systemList.forEach((l) => {
              if (this.ids?.includes(l.id)) {
                this.$refs.multipleTableRef.toggleRowSelection(l, true)
              }
            })
          })
        }
        this.loading = false
      })

      this.$nextTick(() => {
        this.$refs.statsPanelRef?.reload?.()
      })
    },
    // 多选框选中
    selectRow(selection, row) {
      if (this.ids.includes(row.id)) {
        const index = this.ids.findIndex((id) => id == row.id)
        this.ids.splice(index, 1)
      } else {
        this.ids.push(row.id)
      }
      this.multipleLength = this.ids.length
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    // 选中当前页面所有数据
    selectAll(selection) {
      if (selection.length == 0) {
        this.systemList.forEach((l) => {
          if (this.ids.includes(l.id)) {
            const index = this.ids.findIndex((id) => id == l.id)
            this.ids.splice(index, 1)
          }
        })
      } else {
        selection.forEach((l) => {
          if (!this.ids.includes(l.id)) {
            this.ids.push(l.id)
          }
        })
      }
      this.multipleLength = this.ids.length
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    // 分配
    onAllocation() {
      this.multiple = true
      this.queryParams.pageNum = 1
      this.multipleLength = 0
      this.ids = []
      this.getList()
      if (this.$props.sourceType === 'index') {
        this.getBasicAlarm()
      }
      this.$emit('confirm')
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        taskId: null,
        taskName: null,
        taskType: null,
        deptId: null,
        deptName: null,
        poolId: null,
        poolName: null,
        podId: null,
        podName: null,
        clusterId: null,
        clusterName: null,
        groupTagId: null,
        businessIp: null,
        port: null,
        manageIp: null,
        ipAddress: null,
        leakName: null,
        riskLevel: null,
        leakContent: null,
        repairSug: null,
        cveNum: null,
        findMethod: null,
        findPeople: null,
        findTime: null,
        remark: null,
        repeatFlag: null,
        riskOperatorGroupId: void 0,
        disposeId: null,
        disposeName: null,
        status: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        systemName: null
      }
      if (this.$props.sourceType === 'report') {
        this.form = {
          ...this.form,
          taskId: this.$props.taskDetail.taskId,
          taskName: this.$props.taskDetail.taskName,
          taskType: this.$props.taskDetail.taskType
        }
      }
      this.resetForm('form')
    },
    // 失去焦点触发
    // onBlurLeak() {
    //   setTimeout(() => {
    //     this.isInputMsg = false;
    //   }, 500);
    // },
    // // 输入框改变
    // onChangeLeak(val) {
    //   this.isInputMsg = true;
    //   request({
    //     url: `/risk/warn/hint/search`,
    //     method: 'get',
    //     params: {
    //       riskType: 1,
    //       riskWarnType: 20004,
    //       field: 'leakName',
    //       value: val,
    //     },
    //   }).then((response) => {
    //     if (!response) {
    //       this.leakData = [];
    //     } else {
    //       this.leakData = response.data;
    //     }
    //   });
    // },
    // onLeakMsg(val) {
    //   this.isInputMsg = false;
    //   this.queryParams.leakName = val;
    // },
    // // 失去焦点触发
    // onBlurCve() {
    //   setTimeout(() => {
    //     this.isCve = false;
    //   }, 500);
    // },
    // // 输入框改变
    // onChangeCve(val) {
    //   this.isCve = true;
    //   request({
    //     url: `/risk/warn/hint/search`,
    //     method: 'get',
    //     params: {
    //       riskType: 1,
    //       riskWarnType: 20004,
    //       field: 'cveNum',
    //       value: val,
    //     },
    //   }).then((response) => {
    //     if (!response) {
    //       this.cveData = [];
    //     } else {
    //       this.cveData = response.data;
    //     }
    //   });
    // },
    // onCveMsg(val) {
    //   this.isCve = false;
    //   this.queryParams.cveNum = val;
    // },

    // 查看历史
    onHistoryDetail(row) {
      this.taskDetail.leakName = row.leakName
      this.taskDetail.businessIp = row.businessIp
      this.taskDetail.cveNum = row.cveNum
      this.taskId = this.taskDetail.taskId
      this.repeatFlag = this.taskDetail.repeatFlag
      delete this.taskDetail.taskId
      delete this.taskDetail.repeatFlag
      delete this.taskDetail.statusList
      this.$refs.historyRef.show()
    },
    // 关闭查看历史
    onClose() {
      this.taskDetail.leakName = ''
      this.taskDetail.businessIp = ''
      this.taskDetail.cveNum = ''
      this.taskDetail.taskId = this.taskId
      this.taskDetail.repeatFlag = this.repeatFlag
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.ids = []
      this.getList()
      if (this.$props.sourceType === 'index') {
        this.getBasicAlarm()
      }
    },
    // 获取开始ip
    getStartIp(val, field) {
      this.queryParams[field] = val
    },
    // 获取结束ip
    getEndIp(val, field) {
      this.queryParams[field] = val
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.queryParams.startIp = ''
      this.queryParams.endIp = ''
      this.$refs.ipRef?.reset?.()

      this.daterange = []
      this.onChangeDaterange()

      this.queryParams.startManageIp = ''
      this.queryParams.endManageIp = ''
        this.$refs.manageIpRef?.reset?.()

        this.queryParams.systemName = ''
        this.queryParams.leakName = ''

        this.handleQuery()
    },
    // 多选框选中数据
    onSelectionChange(selection) {
      this.selection = selection
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = `新增${this.$route.meta.title}`
      this.getTaskListByType()
    },
    // 补录信息
    onAddMsg() {
      this.isShowAdd = true
    },
    /** 修改按钮操作 */
    // handleUpdate(row) {
    //   this.reset();
    //   const id = row.id || this.ids;
    //   getSystem(id).then((response) => {
    //     this.form = response.data;
    //     this.open = true;
    //     this.title = `修改${this.$route.meta.title}`;
    //   });
    // },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateSystem(this.form).then((response) => {
              this.$modal.msgSuccess('修改成功')
              this.open = false
              this.getList()
              if (this.$props.sourceType === 'index') {
                this.getBasicAlarm()
              }
            })
          } else {
            addSystem(this.form).then((response) => {
              this.$modal.msgSuccess('新增成功')
              this.open = false
              this.getList()
              if (this.$props.sourceType === 'index') {
                this.getBasicAlarm()
              }
            })
          }
        }
      })
    },
    /** 补录信息按钮 */
    submitAddForm() {
      this.$refs['addForm'].validate((valid) => {
        if (valid) {
          request({
            url: '/risk/basic/system/completeAdd',
            method: 'post',
            data: this.addForm
          }).then((response) => {
            this.$modal.msgSuccess('新增成功')
            this.isShowAdd = false
            this.getList()
            if (this.$props.sourceType === 'index') {
              this.getBasicAlarm()
            }
          })
        }
      })
    },
    // 取消补录按钮
    cancelAdd() {
      this.isShowAdd = false
      this.resetAdd()
    },
    // 重置补录表单
    resetAdd() {
      this.addForm = {
        id: null,
        taskId: null,
        taskName: null,
        taskType: null,
        deptId: null,
        deptName: null,
        poolId: null,
        poolName: null,
        podId: null,
        podName: null,
        clusterId: null,
        clusterName: null,
        groupTagId: null,
        businessIp: null,
        port: null,
        manageIp: null,
        ipAddress: null,
        leakName: null,
        riskLevel: null,
        leakContent: null,
        repairSug: null,
        cveNum: null,
        findMethod: null,
        findPeople: null,
        findTime: null,
        remark: null,
        repeatFlag: null,
        riskOperatorGroupId: void 0,
        disposeId: null,
        disposeName: null,
        status: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        systemName: null
      }
      this.resetForm('addForm')
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids
      this.$modal
        .confirm(`是否确认删除${row.taskName || '当前所选'}数据项？`)
        .then(function() {
          return delSystem(ids)
        })
        .then(() => {
          this.multiple = true
          this.queryParams.pageNum = 1
          this.multipleLength = 0
          this.ids = []
          this.getList()
          if (this.$props.sourceType === 'index') {
            this.getBasicAlarm()
          }
          this.$modal.msgSuccess('删除成功')
          this.$emit('confirm')
        })
        .catch(() => {})
    },
    /** 导出按钮操作 */
    async handleExport() {
      if (this.total > 200000) {
        this.$modal.msgWarning('抱歉，导出数据量过大，请选择筛选条件后再进行导出')
        return false
      }

      const submitAuery = JSON.parse(JSON.stringify(this.queryParams))
      delete submitAuery.pageNum
      delete submitAuery.pageSize
      submitAuery.statusList = submitAuery.statusList.join()

      this.download(
        this.$props.source.exportUrl,
        {
          ...submitAuery,
          ids: this.ids
        },
        `导出${this.$route.meta.title}_${new Date().getTime()}.xlsx`
      )
    },
    handleClusterBug() {
      this.$refs.clusterBugDialogRef.open()
    },
    async handleExportSystem(type) {
      let url
      if (type == 1) {
        url = '/risk/basic/system/groupExport'
      } else if (type == 2) {
        url = '/risk/basic/system/adminAndTenantSystemGroupExport'
      }
      if (this.total > 200000) {
        this.$modal.msgWarning('抱歉，导出数据量过大，请选择筛选条件后再进行导出')
        return false
      }

      const submitAuery = JSON.parse(JSON.stringify(this.queryParams))
      delete submitAuery.pageNum
      delete submitAuery.pageSize
      submitAuery.statusList = submitAuery.statusList.join()

      if (submitAuery.riskOperatorGroupId) {
        submitAuery.riskOperatorGroupId = submitAuery.riskOperatorGroupId.join()
      }

      this.download(
        url,
        {
          ...submitAuery
        },
        `导出汇总明细_${new Date().getTime()}.xlsx`
      )
    },
    onDetail(row) {
      this.$refs.infoDialogRef.open(row)
    },
    // onDetail(row) {
    //   request({
    //     url: `/risk/basic/system/${row.id}`,
    //     method: 'get'
    //   }).then((response) => {
    //     this.detail.data = response.data
    //     // this.detail.data = row;
    //     // 暂不处理时 显示最终修复时间和申请附件
    //     if (row.status == 3) {
    //       this.detail.formLabel = [
    //         {
    //           label: '任务名称',
    //           field: 'taskName'
    //         },
    //         {
    //           label: '任务类型',
    //           field: 'taskType',
    //           type: 'main_task_type'
    //         },
    //         {
    //           label: '资源池名称',
    //           field: 'poolName'
    //         },
    //         {
    //           label: '业务系统',
    //           field: 'systemName'
    //         },
    //         {
    //           label: 'POD',
    //           field: 'podName'
    //         },
    //         {
    //           label: '集群名称',
    //           field: 'clusterName'
    //         },
    //         {
    //           label: '业务部门',
    //           field: 'tenantName'
    //         },
    //         {
    //           label: '业务IP',
    //           field: 'businessIp'
    //         },
    //         {
    //           label: '管理IP',
    //           field: 'manageIp'
    //         },
    //         {
    //           label: 'IP地址',
    //           field: 'ipAddress'
    //         },
    //         {
    //           label: '端口',
    //           field: 'port'
    //         },
    //         {
    //           label: '漏洞名称',
    //           field: 'leakName'
    //         },
    //         {
    //           label: '漏洞描述',
    //           field: 'leakContent'
    //         },
    //         {
    //           label: '加固建议',
    //           field: 'repairSug'
    //         },
    //         {
    //           label: 'CVE编号',
    //           field: 'cveNum'
    //         },
    //         {
    //           label: '发现方式',
    //           field: 'findMethod'
    //         },
    //         {
    //           label: '风险等级',
    //           field: 'riskLevel',
    //           type: 'risk_level'
    //         },
    //         {
    //           label: '处置状态',
    //           field: 'status',
    //           type: 'risk_status'
    //         },
    //         {
    //           label: '创建时间',
    //           field: 'createTime'
    //         },
    //         {
    //           label: '检测时间',
    //           field: 'findTime'
    //         },
    //         {
    //           label: '负责团队',
    //           field: 'deptName'
    //         },
    //         {
    //           label: '最终修复时间',
    //           field: 'repairTime'
    //         },
    //         {
    //           label: '申请附件',
    //           field: 'accessoryName',
    //           isTooltip: true
    //         }
    //       ]
    //     }

    //     if (this.detail?.data?.disposalVoucherName) {
    //       this.detail.formLabel.push({
    //         label: '修复凭证',
    //         field: 'disposalVoucherName',
    //         isTooltip: true
    //       })
    //     }

    //     if (this.detail?.data?.disposalType || ['3'].includes(this.detail?.data?.status)) {
    //       this.detail.formLabel.push({
    //         label: '处置原因',
    //         field: 'disposalType'
    //       })
    //     }

    //     this.$refs.detail.show()
    //   })
    // },
    async onOper(row = {}) {
      try {
        await this.validateSource()
      } catch (error) {
        this.$message.warning(error.message)
        return false
      }

      const data = {
        ids: row.id?.toString().split(',') || this.ids,
        disposeStatus: row.status || '',
        remarks: row.remarks || ''
      }

      const rows = row?.id ? [row] : this.selection
      const defaultRangeInfluence = [...new Set(rows.flatMap(item => [item.ipAddress]).filter(Boolean))]

      this.oper.data = {
        riskType: '1',
        riskWarnType: 20004,
        riskContainerSafetyType: '',
        ...data,
        riskType2: '1',
        defaultRangeInfluence: defaultRangeInfluence.join(',')
      }

      this.oper.isRecord = !!row.id

      await this.$nextTick()

      this.$refs.oper.show()
    },
    onOperDetail() {
      this.$refs.multipleTableRef.clearSelection()
      this.multiple = true
      this.queryParams.pageNum = 1
      this.multipleLength = 0
      this.ids = []
      this.getList()
      if (this.$props.sourceType === 'index') {
        this.getBasicAlarm()
      }
      this.$emit('confirm')
    },
    getTaskListByType() {
      if (this.form.taskType) {
        request({
          url: '/risk/report/task/getTaskListByType',
          method: 'get',
          params: {
            taskType: this.form.taskType,
            taskName: this.taskName
          }
        }).then((response) => {
          this.taskIdList = response.data
        })
      }
    },
    onChangeTaskId() {
      this.$nextTick(() => {
        this.form.taskName = this.$refs.taskIdRef.selectedLabel
      })
    },
    remoteMethod(val) {
      this.taskName = val
      this.getTaskListByType()
    },
    selectable(row) {
      return true
    },
    onChangeDaterange() {
      if (this.daterange && this.daterange.length > 0) {
        this.queryParams.startTime = this.daterange[0]
        this.queryParams.endTime = this.daterange[1]
      } else {
        delete this.queryParams.startTime
        delete this.queryParams.endTime
      }
    },
    async onAllocationDetail(type, row, title) {
      try {
        await this.validateSource()
      } catch (error) {
        this.$message.warning(error.message)
        return false
      }

      const queryBo = JSON.parse(JSON.stringify(this.queryParams))
      delete queryBo.pageNum
      delete queryBo.pageSize

      let ids = []
      if (type == 2) {
        ids = row ? [row.id] : this.ids
      }
      this.allocationDetail = {
        riskWarnType: '20004',
        type: type,
        taskId: this.$props.taskDetail.taskId,
        ids,
        title: title || '分配',
        riskType: '1',
        systemQueryBo: queryBo
      }
      this.$refs.allocationDetailRef.show()
    },
    onBlurSearch() {
      setTimeout(() => {
        this.isHintSearch = null
      }, 500)
    },
    onInputSearch(val, field) {
      this.hintSearchList = []
      this.isHintSearch = field
      if (field == 'systemName' || field == 'leakName') {
        request({
          url: `${this.searchUrl}`,
          method: 'get'
          // params: {
          //   riskType: 1,
          //   riskWarnType: 20004,
          //   field,
          //   value: val,
          // },
        }).then((response) => {
          if (!response) {
            this.hintSearchList = []
          } else {
            this.hintSearchList = response.data[field]
          }
        })
      } else {
        request({
          url: `/risk/warn/hint/search`,
          method: 'get',
          params: {
            riskType: 1,
            riskWarnType: 20004,
            field,
            value: val
          }
        }).then((response) => {
          if (!response) {
            this.hintSearchList = []
          } else {
            this.hintSearchList = response.data
          }
        })
      }
    },
    onHintSearch(val, field) {
      this.isHintSearch = null
      this.queryParams[field] = val
    },
    onInputIp(field) {
      this.queryParams[field] = this.queryParams[field].replace(/，/, ',')
    },
    async goBackRiskData(row) {
      try {
        await this.validateSource()
      } catch (error) {
        this.$message.warning(error.message)
        return false
      }

      const ids = row.id || this.ids.join(',')
      this.$modal.confirm(`是否确认退回`).then(() => {
        request({
          url: '/risk/turnoverRecord/goBackRiskData',
          method: 'post',
          data: {
            riskType: '1',
            ids
          }
        }).then((response) => {
          this.$modal.msgSuccess(response.msg)
          this.multiple = true
          this.ids = []
          this.multipleLength = 0
          this.queryParams.pageNum = 1
          this.getList()
          if (this.$props.sourceType === 'index') {
            this.getBasicAlarm()
          }
        })
      })
    },
    getBasicAlarm() {
      // const params = {
      //   type: '1',
      //   queryType: 3,
      //   ...this.queryParams
      // }
      // delete params.pageNum
      // delete params.pageSize
      // request({
      //   url: '/risk/basic/system/countList',
      //   method: 'get',
      //   data: params
      // }).then((response) => {
      //   this.statisticsList.forEach((item) => {
      //     item.num = response.data.find((i) => i.status == item.type)?.num || 0
      //   })
      // })
    },
    getQueryAllGroupNameList() {
      request({
        url: '/risk/operator/groupUser/queryAllGroupNameList',
        method: 'get'
      }).then((response) => {
        this.riskOperatorGroupIdList = response.data
      })
    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = '导入处置结果'
      this.upload.open = true
    },
    /** 下载模板操作 */
    importTemplate() {
      importTemplate().then((response) => {
        this.download(response.msg)
      })
    },
    /** 文件上传中处理*/
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true
    },
    /** 文件上传成功处理*/
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false
      this.upload.isUploading = false
      this.$refs.upload.clearFiles()
      this.$alert(response.msg, '导入结果', { dangerouslyUseHTMLString: true })
      this.getList()
    },
    /** 提交上传文件*/
    async submitFileForm() {
      this.upload.loading = true

      setTimeout(() => {
        this.upload.loading = false
      }, 1000)

      if (!this.$refs.upload.uploadFiles.length) {
        this.$message.warning('请先导入处置结果')
        return false
      }

      if (this.upload.handleType === 'ignore') {
        try {
          this.upload.data = await this.$refs.ignoreFormRef.submit()
        } catch (error) {
          console.warn(error.message)
          return false
        }
      }

      await this.$nextTick()

      this.$refs.upload.submit()
    },
    getLeakNameList() {
      request({
        url: `${this.searchUrl}`,
        method: 'get'
      }).then((response) => {
        this.systemNameList = response.data.systemName
        this.leakNameList = response.data.leakName
      })
    },
    handleApprove(row) {
      const ids = row?.id ? [row.id] : this.ids

      this.$refs.approveDialogRef.open({
        params: {
          riskWarnType: '20004',
          ids
        }
      })
    },
    beforeUploadDispose() {}
  }
}
</script>
<style lang="scss" scoped>
  .change-input {
    margin-left: 12px;
  }

  .prompt {
    position: relative;

    .prompt-box {
      position: absolute;
      left: -28px;
      top: 40px;
    }

    .prompt-box-content {
      position: relative;
      width: 200px;
      height: auto;
      z-index: 100;
      border: solid 1px #dfe4ed;
      border-radius: 4px;
      background-color: #ffffff;
      padding: 8px;
      white-space: nowrap;
      overflow-x: scroll;
    }

    .ovflow-y {
      height: 260px;
      overflow-y: scroll;
    }

    .prompt-box-sanjiao {
      position: absolute;
      top: -9px;
      left: 50%;
      transform: translateX(-50%);
    }

    .sanjiao {
      width: 0;
      height: 0;
      border-style: solid;
      border-width: 0 10px 10px;
      border-color: transparent transparent #dfe4ed;
      position: relative;
    }

    .sanjiao::after {
      content: '';
      border-style: solid;
      border-width: 0 9px 9px;
      border-color: transparent transparent #fff;
      position: absolute;
      top: 1px;
      left: -9px;
    }

    .input-msg-item {
      padding-bottom: 6px;
      // border-bottom: 1px solid #f0f0f0;
      cursor: pointer;
    }
  }

  .statistics-item {
    height: 80px;
    border-radius: 8px;
    background: #ffffff;
    box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.04);
    margin: 0 0 16px;
    padding: 10px 20px 20px;

    .num {
      font-family: OPPOSans;
      font-size: 24px;
      font-weight: 900;
      line-height: 36px;
      color: #3e4040;
    }

    .title {
      font-size: 14px;
      font-weight: 400;
      line-height: 18px;
      color: #636466;

      img {
        flex-shrink: 0;
        width: 16px;
        height: 16px;
        margin-right: 4px;
      }
    }
  }

  @media only screen and (min-width: 992px) {
    .el-col-md-6 {
      width: 20%;
    }
  }
</style>
