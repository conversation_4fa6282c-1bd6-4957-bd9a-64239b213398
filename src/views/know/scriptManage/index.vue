<template>
  <EleSheet ref="sheetRef" v-bind="sheetProps" class="page-main">
    <template #table:uploadSwitch="{ columnProps }">
      <el-table-column v-slot="{ row }" v-bind="{ ...columnProps }">
        <el-switch
          v-model="row.uploadSwitch"
          active-value="1"
          inactive-value="0"
          :disabled="!$checkPermi(['know:scriptManage:upload'])"
          @change="(value) => onSwitchChange(value, row)"
        />
      </el-table-column>
    </template>

    <template #table:after="{ editHandler }">
      <el-table-column v-slot="{ row }" label="操作" align="center" width="200" fixed="right">
        <template
          v-if="
            row.url &&
              (($route.query.scriptType == 1 && $checkPermi(['know:basicLineLibrary:download'])) ||
                ($route.query.scriptType == 2 && $checkPermi(['know:basicPasswordRule:download'])))
          "
        >
          <el-button
            size="mini"
            type="text"
            @click="handleDownload(row.url, row.fileName)"
          >下载</el-button>
          <el-button type="text" size="mini" @click="handlePreview(row)"> 预览 </el-button>
        </template>

        <el-button v-hasPermi="['know:scriptManage:edit']" size="mini" type="text" @click="editHandler(row)">编辑</el-button>

        <el-button v-if="$checkPermi(['res:info:list'])" type="text" size="mini" @click="handleHistory(row)"> 历史 </el-button>
      </el-table-column>
    </template>

    <template #search:tenId:simple="{ model }">
      <TenantSelect v-model="model.tenId" show-global-option global-value="0" />
    </template>

    <template
      #form:tenId:simple="{ model }"
    >
      <TenantSelect
        :value="model.tenId || 'null'"
        show-global-option
        @input="(value)=> model.tenId = value"
        @label-change="(label) => (model.tenName = label)"
      />
    </template>

    <template #after>
      <el-dialog
        ref="previewRef"
        title="预览"
        :visible.sync="previewProps.visible"
        v-bind="{
          ...previewProps,
        }"
        append-to-body
      >
        <div v-if="previewProps.content" v-html="previewProps.content" />
        <div v-else class="text-center"> 暂无数据 </div>
      </el-dialog>

      <HistoryDialog ref="historyDialogRef" />

      <ScriptDialog ref="scriptDialogRef" />
    </template>
  </EleSheet>
</template>

<script>
import {
  addScriptManage,
  delScriptManage,
  getScriptManage,
  listScriptManage,
  updateScriptManage
} from '@/api/know/scriptManage'

import HistoryDialog from './HistoryDialog/index.vue'
import ScriptDialog from './ScriptDialog/index.vue'

import request from '@/utils/request.js'

export default {
  components: {
    HistoryDialog,
    ScriptDialog
  },
  data() {
    return {
      previewProps: {
        visible: false,
        content: '',
        open(content) {
          this.content = content
          this.visible = true
        },
        close() {
          this.visible = false
        },
        onClose() {
          this.content = ''
        }
      }
    }
  },
  computed: {
    scriptTypeText() {
      const value = {
        1: '基线',
        2: '弱口令'
      }[this.$route.query.scriptType]

      return value
    },
    sheetProps() {
      const value = {
        title: `${this.scriptTypeText}脚本`,

        api: {
          add: (params) => addScriptManage({ ...params }),
          edit: (params) => updateScriptManage({ ...params }),
          list: listScriptManage,
          info: getScriptManage,
          remove: delScriptManage,
          export: '/know/scriptManage/export'
        },

        hiddenActions: {
          add: !this.$checkPermi(['know:scriptManage:add']),
          edit: !this.$checkPermi(['know:scriptManage:edit']),
          remove: !this.$checkPermi(['know:scriptManage:remove']),
          export: !this.$checkPermi(['know:scriptManage:export'])
        },

        infoProps: {
          title: true
        },

        model: {
          scriptType: {
            type: 'text',
            label: '脚本类型',
            align: 'left',
            hidden: true,
            value: this.$route.query.scriptType
          },
          name: {
            type: 'text',
            label: '脚本名称',
            form: {
              rules: true
            }
          },
          execCommand: {
            type: 'text',
            label: '指令执行名称',
            search: {
              hidden: true
            }
          },
          relationHostTotal: {
            type: 'text',
            label: '上传记录',
            search: {
              hidden: true
            },
            tableColumnProps: {
              className: 'text-primary-500 hover:underline cursor-pointer',
              formatter: (row) => row?.relationHostTotal || 0,
              onClick: (row) => this.handleScript(row)
            },
            form: {
              hidden: true
            }
          },
          execCommandTotal: {
            type: 'text',
            label: '脚本执行次数',
            search: {
              hidden: true
            },
            form: {
              hidden: true
            }
          },
          version: {
            type: 'text',
            label: '版本号',
            search: {
              hidden: true
            },
            form: {
              hidden: true
            }
          },
          uploadSwitch: {
            label: '脚本上传',
            search: {
              hidden: true
            },
            type: 'select',
            options: [
              {
                label: '是',
                value: '1'
              },
              {
                label: '否',
                value: '0'
              }
            ]
          },
          tenId: {
            label: '所属租户',
            table: {
              formatter: (row) => row.tenName
            },
            rules: true
          },

          createTime: {
            type: 'text',
            label: '创建时间',
            search: {
              type: 'date-range',
              parameter: (data) => {
                return {
                  startTime: data?.[0],
                  endTime: data?.[1]
                }
              }
            },
            form: {
              hidden: true
            },
            width: 200
          },
          updateTime: {
            type: 'text',
            label: '修改时间',
            search: {
              hidden: true
            },
            form: {
              hidden: true
            },
            width: 200
          },
          remarks: {
            type: 'text',
            label: '备注',
            search: {
              hidden: true
            }
          },
          url: {
            type: 'upload',
            label: '上传脚本文件',
            search: {
              hidden: true
            },
            info: {
              hidden: true
            },
            table: {
              hidden: true
            },
            parameter: (value) => {
              return {
                url: value?.[0]?.url
              }
            }
          }
        }
      }

      return value
    }
  },
  methods: {
    handleScript(row) {
      this.$refs.scriptDialogRef.open(row)
    },
    handleHistory(row) {
      this.$refs.historyDialogRef.open({
        baseId: row.id,
        parentSheetModel: this.sheetProps.model
      })
    },
    async handlePreview(row) {
      this.$refs.sheetRef.loading = true

      const res = await request({
        url: '/know/scriptManage/readScriptFileContent',
        method: 'post',
        data: {
          fileUrl: row.url
        }
      })

      this.$refs.sheetRef.loading = false

      const content = res.data.replace(new RegExp('\n', 'g'), '<br/>')

      this.previewProps.open(content)
    },

    async onSwitchChange(uploadSwitch, row) {
      const params = {
        id: row.id,
        uploadSwitch
      }

      const res = await this.sheetProps.api.edit(params)

      if (res.code !== 200) {
        this.$message.warning(res.msg)
      }
    },
    handleDownload(downloadPath, fileName) {
      this.download(
        `${downloadPath}`,
        {},
        fileName
      )
    }
  }
}
</script>

<style></style>
