<template>
  <div class="page-main">
    <el-form
      v-show="showSearch"
      ref="queryForm"
      class="sa-query"
      :model="queryParams"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="弱口令值" prop="passwordDictValue">
        <el-input
          v-model="queryParams.passwordDictValue"
          placeholder="请输入弱口令值"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option
            v-for="dict in dict.type.start_stop_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item class="query-handle">
        <el-button type="primary" plain icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh-left" @click="resetQuery">重置</el-button>
        <EleSheetSearchMore :parent-ref="()=> $refs.queryForm" />
      </el-form-item>
    </el-form>

    <div class="sa-title">
      <sa-toolbar :show-search.sync="showSearch" @queryTable="getList" />
      <el-button
        v-hasPermi="['know:basicPasswordDck:add']"
        type="primary"
        icon="el-icon-plus"
        @click="handleAdd"
      >新增</el-button>
      <el-button
        v-hasPermi="['know:basicPasswordDck:export']"
        type="primary"
        icon="el-icon-upload2"
        @click="handleImport"
      >导入</el-button>
      <el-button
        v-hasPermi="['know:basicPasswordDck:export']"
        type="primary"
        icon="el-icon-download"
        :loading="exportLoading"
        @click="handleExport"
      >导出</el-button>
    </div>

    <!-- <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">

      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['know:basicPasswordDck:edit']"
          >修改</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['know:basicPasswordDck:remove']"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">

      </el-col>
      <el-col :span="1.5">

      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row> -->

    <el-table
      v-loading="loading"
      class="sa-table"
      :data="basicPasswordDckList"
      @selection-change="handleSelectionChange"
    >
      <!-- <el-table-column type="selection" width="55" align="center" /> -->
      <!-- <el-table-column label="主键ID" align="center" prop="id" v-if="false" /> -->
      <el-table-column label="弱口令值" align="center" prop="passwordDictValue" />
      <el-table-column label="命中次数" align="center" prop="hitNumber" />
      <el-table-column label="创建时间" align="center" prop="createTime" />
      <el-table-column label="备注" align="center" prop="remarks" />
      <el-table-column label="状态" align="center">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.status"
            class="sa-m-r-12"
            active-value="0"
            inactive-value="1"
            :disabled="!$checkPermi(['know:basicPasswordDck:updateStatus'])"
            @change="onEdit(scope.row)"
          />
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" fixed="right">
        <template slot-scope="scope">
          <!-- <el-button size="mini" type="text" @click="onDetail(scope.row)">查看</el-button> -->
          <el-button
            v-hasPermi="['know:basicPasswordDck:edit']"
            size="mini"
            type="text"
            @click="handleUpdate(scope.row)"
          >修改</el-button>
          <el-button
            v-hasPermi="['know:basicPasswordDck:remove']"
            size="mini"
            type="text"
            @click="handleDelete(scope.row)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <div class="sa-footer sa-row-center">
      <sa-pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </div>

    <!-- 添加或修改弱口令字典对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="弱口令值" prop="passwordDictValue">
          <el-input v-model="form.passwordDictValue" placeholder="请输入弱口令值" />
        </el-form-item>
        <el-form-item label="是否启用" prop="status" required>
          <el-radio-group v-model="form.status">
            <el-radio
              v-for="dict in dict.type.start_stop_status"
              :key="dict.value"
              :label="dict.value"
            >{{ dict.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <!-- <el-form-item label="删除标志" prop="delFlag">
          <el-input v-model="form.delFlag" placeholder="请输入删除标志" />
        </el-form-item>
        <el-form-item label="创建者" prop="createBy">
          <el-input v-model="form.createBy" placeholder="请输入创建者" />
        </el-form-item>
        <el-form-item label="创建时间" prop="createTime">
          <el-date-picker
            clearable
            size="small"
            v-model="form.createTime"
            type="datetime"
            value-format="yyyy-MM-dd HH:mm:ss"
            placeholder="选择创建时间"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="更新者" prop="updateBy">
          <el-input v-model="form.updateBy" placeholder="请输入更新者" />
        </el-form-item>
        <el-form-item label="更新时间" prop="updateTime">
          <el-date-picker
            clearable
            size="small"
            v-model="form.updateTime"
            type="datetime"
            value-format="yyyy-MM-dd HH:mm:ss"
            placeholder="选择更新时间"
          >
          </el-date-picker>
        </el-form-item> -->
        <el-form-item label="备注" prop="remarks">
          <el-input v-model="form.remarks" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
        <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
      </div>
    </el-dialog>
    <!-- 导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls,.txt"
        :headers="upload.headers"
        :action="upload.url"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload" />
        <div class="el-upload__text">
          将文件拖到此处，或
          <em>点击上传</em>
        </div>
        <div slot="tip" class="el-upload__tip">
          <el-link
            type="info"
            style="font-size: 14px; color: green"
            @click="importTemplate"
          >点击下载模板</el-link>
        </div>
        <div
          slot="tip"
          class="el-upload__tip"
          style="color: red"
        >提示：仅允许导入“xls”、“xlsx”或“txt”格式文件！</div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button @click="upload.open = false">取 消</el-button>
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
      </div>
    </el-dialog>

    <sa-detail ref="detailRef" :detail-data="detailData" />
  </div>
</template>

<script>
import {
  addBasicPasswordDck,
  delBasicPasswordDck,
  exportBasicPasswordDck,
  getBasicPasswordDck,
  listBasicPasswordDck,
  updateBasicPasswordDck
} from '@/api/know/basicPasswordDck'
import { getToken } from '@/utils/auth'
import request from '@/utils/request'

export default {
  name: 'BasicPasswordDck',
  dicts: ['start_stop_status'],
  data() {
    return {
      // 按钮loading
      buttonLoading: false,
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 弱口令字典表格数据
      basicPasswordDckList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 导入参数
      upload: {
        // 是否显示弹出层
        open: false,
        // 弹出层标题
        title: '',
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: { Authorization: 'Bearer ' + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + '/know/basicPasswordDck/importDataAll'
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        passwordDictValue: undefined,

        hitNumber: undefined,

        status: undefined,

        remarks: undefined
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        passwordDictValue: [{ required: true, message: '不能为空', trigger: 'change' }]
      },

      detailData: {
        id: '',
        data: {},
        formLabel: [
          {
            label: '弱口令值',
            field: 'passwordDictValue'
          },
          {
            label: '命中次数',
            field: 'hitNumber'
          },
          {
            label: '状态',
            field: 'status',
            type: 'start_stop_status'
          },
          {
            label: '创建时间',
            field: 'createTime'
          },
          {
            label: '备注',
            field: 'remarks'
          }
        ]
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    /** 查询弱口令字典列表 */
    getList() {
      this.loading = true
      listBasicPasswordDck(this.queryParams).then((response) => {
        this.basicPasswordDckList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        id: undefined,

        passwordDictValue: undefined,

        hitNumber: undefined,

        delFlag: undefined,

        status: '1',

        createBy: undefined,

        createTime: undefined,

        updateBy: undefined,

        updateTime: undefined,

        remarks: undefined
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = '添加弱口令字典'
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.loading = true
      this.reset()
      const id = row.id || this.ids
      getBasicPasswordDck(id).then((response) => {
        this.loading = false
        this.form = response.data
        this.open = true
        this.title = '修改弱口令字典'
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          this.buttonLoading = true
          if (this.form.id != null) {
            updateBasicPasswordDck(this.form)
              .then((response) => {
                this.$modal.msgSuccess('修改成功')
                this.open = false
                this.getList()
              })
              .finally(() => {
                this.buttonLoading = false
              })
          } else {
            addBasicPasswordDck(this.form)
              .then((response) => {
                this.$modal.msgSuccess('新增成功')
                this.open = false
                this.getList()
              })
              .finally(() => {
                this.buttonLoading = false
              })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids
      this.$confirm('是否确认删除当前所选数据项？', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.loading = true
          return delBasicPasswordDck(ids)
        })
        .then(() => {
          this.loading = false
          this.getList()
          this.$modal.msgSuccess('删除成功')
        })
        .catch(() => {})
    },
    /** 导出按钮操作 */
    handleExport() {
      if (this.total > 200000) {
        this.$modal.msgWarning('抱歉，导出数据量过大，请选择筛选条件后再进行导出')
        return false
      }

      const submitAuery = JSON.parse(JSON.stringify(this.queryParams))
      delete submitAuery.pageNum
      delete submitAuery.pageSize

      this.download(
        '/know/basicPasswordDck/export',
        {
          ...submitAuery
        },
        `导出${this.$route.meta.title}_${new Date().getTime()}.xlsx`
      )
      // const queryParams = this.queryParams;
      // this.$confirm('是否确认导出所有弱口令字典数据项?', '警告', {
      //   confirmButtonText: '确定',
      //   cancelButtonText: '取消',
      //   type: 'warning',
      // })
      //   .then(() => {
      //     this.exportLoading = true;
      //     return exportBasicPasswordDck(queryParams);
      //   })
      //   .then((response) => {
      //     this.download(response.msg);
      //     this.exportLoading = false;
      //   })
      //   .catch(() => {});
    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = '弱口令字典导入'
      this.upload.open = true
    },
    /** 下载模板操作 */
    importTemplate() {
      this.download(
        'know/basicPasswordDck/importTemplate',
        {},
        `${this.$route.meta.title}模板_${new Date().getTime()}.xlsx`
      )
    },
    /** 文件上传中处理*/
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true
    },
    /** 文件上传成功处理*/
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false
      this.upload.isUploading = false
      this.$refs.upload.clearFiles()
      this.$alert(response.msg, '导入结果', { dangerouslyUseHTMLString: true })
      this.getList()
    },
    /** 提交上传文件*/
    submitFileForm() {
      this.$refs.upload.submit()
    },
    onDetail(row) {
      this.detailData.data = row
      this.$refs.detailRef.show()
    },
    onEdit(row) {
      request({
        url: '/know/basicPasswordDck/updateStatus',
        method: 'post',
        data: row
      }).then((response) => {
        this.$modal.msgSuccess(response.msg)
        this.getList()
      })
    }
  }
}
</script>
