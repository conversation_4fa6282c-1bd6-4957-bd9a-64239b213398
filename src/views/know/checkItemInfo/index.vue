<template>
  <div class="page-main">
    <el-tabs v-model="queryParams.templateType" class="sa-m-b-10" @tab-click="resetQuery">
      <template v-for="(item, index) in checkItemType">
        <el-tab-pane
          v-if="type == 'index'"
          :key="item.dictValue"
          :label="`${item.dictLabel}(${item.num})`"
          :name="item.dictValue"
        />
        <el-tab-pane
          v-if="type == 'add' && templateType.includes(item.dictValue)"
          :key="item.dictValue"
          :label="`${item.dictLabel}(${item.num})`"
          :name="item.dictValue"
        />
        <el-tab-pane
          v-if="type == 'list' && templateType.includes(item.dictValue)"
          :key="item.dictValue"
          :label="`${item.dictLabel}`"
          :name="item.dictValue"
        />
      </template>
    </el-tabs>
    <el-form
      v-show="showSearch"
      ref="queryForm"
      class="sa-query"
      :model="queryParams"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="检查项编码" prop="flag">
        <el-input
          v-model="queryParams.flag"
          placeholder="请输入检查项编码"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="检查项名称" prop="baslineName">
        <el-input
          v-model="queryParams.baslineName"
          placeholder="请输入检查项名称"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="组件名称" prop="genre">
        <el-select v-model="queryParams.genre" placeholder="请选择组件名称" clearable>
          <el-option
            v-for="dict in dict.type.base_line_check_item_modules"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item class="query-handle">
        <el-button type="primary" plain icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh-left" @click="resetQuery">重置</el-button>
        <EleSheetSearchMore :parent-ref="()=> $refs.queryForm" />
      </el-form-item>
    </el-form>

    <div class="sa-title">
      <sa-toolbar :show-search.sync="showSearch" @queryTable="getList" />
      <el-button
        v-if="['index', 'add'].includes(type)"
        v-hasPermi="['know:checkItemInfo:add']"
        type="primary"
        icon="el-icon-plus"
        @click="handleAdd"
      >新增</el-button>
      <el-button
        v-if="['index', 'add'].includes(type)"
        v-hasPermi="['know:checkItemInfo:remove']"
        type="info"
        :disabled="ids.length == 0"
        @click="handleDelete"
      ><IconPowerOff class="" />停用</el-button>

      <el-button v-if="['list'].includes($props.type) && $checkPermi(['know:templateInfo:detail:export'])" type="primary" icon="el-icon-download" @click="handleExport">导出</el-button>
    </div>
    <el-table ref="tableRef" v-loading="loading" class="sa-table" :data="list">
      <el-table-column width="100" align="center">
        <template slot="header" slot-scope="scope">
          <el-checkbox
            :value="isSelectAll"
            :indeterminate="isIndeterminate"
            @change="onSelectAll"
          />
        </template>
        <template slot-scope="scope">
          <el-checkbox
            :value="ids.includes(scope.row.id)"
            @change="onSelect($event, scope.row)"
          />
        </template>
      </el-table-column>
      <el-table-column v-if="false" label="主键自增id" align="center" prop="id" />
      <el-table-column label="检查项编码" align="center" prop="flag" />
      <el-table-column label="检查项名称" align="center" prop="baslineName" />
      <el-table-column label="组件名称" align="center">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.base_line_check_item_modules" :value="scope.row.genre" />
        </template>
      </el-table-column>
      <el-table-column label="来源" align="center">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.base_line_check_item_source" :value="scope.row.source" />
        </template>
      </el-table-column>
      <el-table-column label="风险等级" align="center">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.base_line_check_item_level" :value="scope.row.devel" />
        </template>
      </el-table-column>
      <el-table-column
        v-if="['index'].includes(type)"
        fixed="right"
        label="操作"
        align="center"
        width="160"
      >
        <template slot-scope="scope">
          <el-button v-if="$checkPermi(['know:checkItemInfo:query'])" size="mini" type="text" @click="onDetail(scope.row)">查看</el-button>
          <el-button
            v-hasPermi="['know:checkItemInfo:edit']"
            size="mini"
            type="text"
            @click="handleUpdate(scope.row)"
          >修改</el-button>
          <el-button
            v-hasPermi="['know:checkItemInfo:remove']"
            size="mini"
            type="text"
            @click="handleDelete(scope.row)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <div class="sa-footer sa-row-center">
      <sa-pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </div>

    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="检查项编码" prop="flag">
          <el-input v-model="form.flag" placeholder="请输入检查项编码" :disabled="form.id ? true : false" />
        </el-form-item>
        <el-form-item label="风险等级" prop="devel">
          <el-select v-model="form.devel" placeholder="请选择风险等级" clearable>
            <el-option
              v-for="dict in dict.type.base_line_check_item_level"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="系统类型" prop="osType">
          <el-select v-model="form.osType" placeholder="请选择系统类型" clearable>
            <el-option
              v-for="dict in dict.type.base_line_check_item_osType"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="组件名称" prop="genre">
          <el-select v-model="form.genre" placeholder="请选择组件名称" clearable>
            <el-option
              v-for="dict in dict.type.base_line_check_item_modules"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="来源" prop="source">
          <el-select v-model="form.source" placeholder="请选择来源" clearable>
            <el-option
              v-for="dict in dict.type.base_line_check_item_source"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="检查项名称" prop="baslineName">
          <el-input v-model="form.baslineName" placeholder="请输入检查项名称" />
        </el-form-item>
        <el-form-item label="判断逻辑" prop="judgeType">
          <el-select v-model="form.judgeType" placeholder="请选择判断逻辑" clearable>
            <el-option
              v-for="dict in dict.type.base_line_check_item_judge"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item v-if="form.judgeType != '0'" label="判断值" prop="judgeValue">
          <el-input v-model="form.judgeValue" placeholder="请输入检查项名称" />
        </el-form-item>
        <el-form-item label="检测方法" prop="checkMethod">
          <el-input v-model="form.checkMethod" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="判断依据" prop="judgeBasis">
          <el-input v-model="form.judgeBasis" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="整改建议" prop="solution">
          <el-input v-model="form.solution" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="是否人工确认" prop="isPersonJudge">
          <el-switch v-model="form.isPersonJudge" active-value="1" inactive-value="0">
          </el-switch>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
        <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
      </div>
    </el-dialog>

    <sa-detail ref="detailRef" :detail-data="detailData" />
  </div>
</template>

<script>
import {
  addCheckItemInfo,
  delCheckItemInfo,
  exportCheckItemInfo,
  getCheckItemInfo,
  listCheckItemInfo,
  updateCheckItemInfo
} from '@/api/know/checkItemInfo'
import { getToken } from '@/utils/auth'
import request from '@/utils/request'
import checkEchoMixin from './checkEchoMixin.js'
import { omit } from 'lodash-es'

export default {
  mixins: [checkEchoMixin()],
  name: 'CheckItemInfo',
  dicts: [
    'base_line_check_item_type',
    'base_line_check_item_level',
    'base_line_check_item_modules',
    'base_line_check_item_osType',
    'base_line_check_item_source',
    'base_line_check_item_judge'
  ],
  props: {
    type: {
      type: String,
      default: 'index' // index|add|list
    },
    search: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      // 按钮loading
      buttonLoading: false,
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 基线检查项信息表格数据
      list: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        templateType: '',
        flag: undefined,
        baslineName: undefined,
        genre: undefined
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        flag: [{ required: true, message: '不能为空', trigger: 'blur' }],
        devel: [{ required: true, message: '不能为空', trigger: 'blur' }],
        osType: [{ required: true, message: '不能为空', trigger: 'blur' }],
        genre: [{ required: true, message: '不能为空', trigger: 'blur' }],
        source: [{ required: true, message: '不能为空', trigger: 'blur' }],
        baslineName: [{ required: true, message: '不能为空', trigger: 'blur' }],
        judgeType: [{ required: true, message: '不能为空', trigger: 'blur' }],
        judgeValue: [
          {
            required: true,
            validator: (rule, value, callback) => {
              if (value) {
                if (
                  this.form.judgeType == 3 ||
                    this.form.judgeType == 4 ||
                    this.form.judgeType == 8 ||
                    this.form.judgeType == 9
                ) {
                  var reg = /^[0-9]*$/g
                  if (reg.test(value)) {
                    callback()
                  } else {
                    callback(new Error(`只能输入数字`))
                  }
                } else {
                  callback()
                }
              } else {
                callback(new Error(`不能为空`))
              }
            },
            trigger: 'blur'
          }
        ],
        checkMethod: [{ required: true, message: '不能为空', trigger: 'blur' }],
        judgeBasis: [{ required: true, message: '不能为空', trigger: 'blur' }],
        solution: [{ required: true, message: '不能为空', trigger: 'blur' }]
      },

      checkItemType: [],

      detailData: {
        id: '',
        data: {},
        formLabel: [
          {
            label: '检查项编码',
            field: 'flag'
          },
          {
            label: '风险等级',
            field: 'devel',
            type: 'base_line_check_item_level'
          },
          {
            label: '系统类型',
            field: 'osType',
            type: 'base_line_check_item_osType'
          },
          {
            label: '组件名称',
            field: 'genre',
            type: 'base_line_check_item_modules'
          },
          {
            label: '来源',
            field: 'source',
            type: 'base_line_check_item_source'
          },
          {
            label: '检查项名称',
            field: 'baslineName'
          },
          {
            label: '判断逻辑',
            field: 'judgeType',
            type: 'base_line_check_item_judge'
          },
          {
            label: '检测方法',
            field: 'checkMethod'
          },
          {
            label: '判断依据',
            field: 'judgeBasis'
          },
          {
            label: '整改建议',
            field: 'solution'
          }
        ]
      },

      selectedItem: [],
      isSelectAll: false,
      isIndeterminate: false,

      templateType: ''
    }
  },
  created() {
    this.getCheckItemType()
    this.getList()
  },
  methods: {
    handleExport() {
      this.download(`/know/templateInfo/getTemplateCheckItemListExport/${this.$props.search.templateId}`, { ...omit(this.queryParams, ['pageNum', 'pageSize']) }, `导出_基线检查项_${Date.now()}.xlsx`)
    },
    /** 查询基线检查项信息列表 */
    getList() {
      this.loading = true
      console
      if (this.$props.type == 'index') {
        if (!this.queryParams.templateType) {
          this.getDicts('base_line_check_item_type').then((response) => {
            if (response.data.length > 0) {
              this.queryParams.templateType = response.data[0].dictValue
              listCheckItemInfo(this.queryParams).then((response) => {
                this.list = response.rows
                this.total = response.total

                this.calculateSelect()

                this.loading = false
              })
            }
          })
        } else {
          listCheckItemInfo(this.queryParams).then((response) => {
            this.list = response.rows
            this.total = response.total

            this.calculateSelect()

            this.loading = false
          })
        }
      } else if (this.$props.type == 'add') {
        this.templateType = this.$props.search.templateType
        if (!this.queryParams.templateType) {
          this.queryParams.templateType = this.$props.search.templateType.split(',')[0]
        }
        listCheckItemInfo(this.queryParams).then(async(response) => {
          this.list = response.rows
          this.total = response.total

          this.calculateSelect()

          const { selectedItem, ids } = await this.checkEchoMixin.echoSelection({ ...this.$props.search, ...this.queryParams })

          this.selectedItem = selectedItem
          this.ids = ids

          this.loading = false
        })
      } else if (this.$props.type == 'list') {
        this.templateType = this.$props.search.templateType
        if (!this.queryParams.templateType) {
          this.queryParams.templateType = this.$props.search.templateType.split(',')[0]
        }
        request({
          url: `/know/templateInfo/getTemplateCheckItemList/${this.$props.search.templateId}`,
          method: 'get',
          params: {
            ...this.queryParams,
            templateType: this.queryParams.templateType
          }
        }).then((response) => {
          this.list = response.rows
          this.total = response.total

          this.calculateSelect()

          this.loading = false
        })
      }
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        id: undefined,

        flag: undefined,

        identifier: undefined,

        baslineName: undefined,

        devel: undefined,

        checkMethod: undefined,

        source: undefined,

        templateType: this.queryParams.templateType,

        osType: undefined,

        genre: undefined,

        checkType: undefined,

        solution: undefined,

        judgeBasis: undefined,

        judgeType: undefined,

        judgeValue: undefined,

        remark: undefined,

        createBy: undefined,

        createTime: undefined,

        updateBy: undefined,

        updateTime: undefined,

        isPersonJudge: '0'
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = '添加基线检查项信息'
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.loading = true
      this.reset()
      const id = row.id || this.ids
      getCheckItemInfo(id).then((response) => {
        this.loading = false
        this.form = response.data
        this.open = true
        this.title = '修改基线检查项信息'
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          this.buttonLoading = true
          if (this.form.id != null) {
            updateCheckItemInfo(this.form)
              .then((response) => {
                this.$modal.msgSuccess('修改成功')
                this.open = false
                this.getList()
              })
              .finally(() => {
                this.buttonLoading = false
              })
          } else {
            addCheckItemInfo(this.form)
              .then((response) => {
                this.$modal.msgSuccess('新增成功')
                this.open = false
                this.getList()
              })
              .finally(() => {
                this.buttonLoading = false
              })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids
      this.$confirm('是否确认删除基线检查项信息编号为"' + ids + '"的数据项?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.loading = true
          return delCheckItemInfo(ids)
        })
        .then(() => {
          this.loading = false
          this.getList()
          this.$modal.msgSuccess('删除成功')
        })
        .catch(() => {})
    },
    getCheckItemType() {
      request({
        url: `/know/checkItemInfo/getCheckItemType`,
        method: 'get'
      }).then((response) => {
        this.checkItemType = response.data
      })
    },
    onDetail(row) {
      this.detailData.data = row
      this.$refs.detailRef.show()
    },
    onSelect(type, row) {
      if (type) {
        this.ids.push(row.id)
        this.selectedItem.push(row)
      } else {
        const findIndex = this.ids.findIndex((id) => id == row.id)
        this.ids.splice(findIndex, 1)
        this.selectedItem.splice(findIndex, 1)
      }
      this.calculateSelect()
    },
    onSelectAll(type) {
      if (type) {
        this.list.forEach((item) => {
          this.ids.push(item.id)
          this.selectedItem.push(item)
        })
        this.ids = Array.from(new Set(this.ids))
      } else {
        this.list.forEach((item) => {
          if (this.ids.findIndex((id) => id == item.id) !== -1) {
            this.ids.splice(
              this.ids.findIndex((id) => id == item.id),
              1
            )
            this.selectedItem.splice(
              this.selectedItem.findIndex((i) => i.flagArr == item.flagArr),
              1
            )
          }
        })
      }
      this.calculateSelect()
    },
    calculateSelect() {
      this.isSelectAll = false
      this.isIndeterminate = false
      if (this.list.every((item) => this.ids.includes(item.id))) {
        this.isSelectAll = true
        this.isIndeterminate = false
      } else if (this.list.some((item) => this.ids.includes(item.id))) {
        this.isSelectAll = false
        this.isIndeterminate = true
      }

      if (this.list.length === 0) {
        this.isSelectAll = false
        this.isIndeterminate = false
      }

      if (this.$props.type == 'add' && this.ids.length > 0) {
        this.$emit('addTemplateCheckItem', this.selectedItem)
      }
    }
  }
}
</script>
