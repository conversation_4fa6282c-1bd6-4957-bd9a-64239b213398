<template>
  <EleSheet ref="sheetRef" v-bind="sheetProps" class="page-main page-main--full">
    <!-- <template #toolbar:after>
      <el-button type="success" @click="handleMapping"> 数据库映射 </el-button>
    </template> -->

    <template #table:isEnable="{ columnProps }">
      <el-table-column v-slot="{ row }" v-bind="{ ...columnProps }">
        <el-switch
          v-model="row.isEnable"
          active-value="1"
          inactive-value="0"
          :disabled="!$checkPermi(['common:ruleInfo:updateStatus'])"
          @change="(value) => onSwitchChange(value, row)"
        />
      </el-table-column>
    </template>

    <template #table:action:before="{ row }">
      <el-button v-if="$checkPermi(['rules:scan:execute'])" type="text" size="mini" :loading="row.$loading" @click="handleExecute(row)">{{
        row.$loading ? '执行中' : '执行'
      }}</el-button>
    </template>

    <template #form:ruleScanDetailList="{ model }">
      <el-col :span="24">
        <el-divider content-position="left">告警数据扫描条件</el-divider>
        <ScanFormGroup v-bind="{ model }" />
      </el-col>
    </template>

    <template #form:cronExpression:simple="{ model }">
      <CronInput v-model="model.cronExpression" />
    </template>

    <template #form:ruleHitActionDetailList="{ model }">
      <el-col :span="24">
        <el-divider content-position="left" class="!mt-12">命中结论</el-divider>
        <TargetFormGroup v-bind="{ model }" />
      </el-col>
    </template>

    <template #edit:footer:before="{ formMixin }">
      <el-button type="warning" :loading="formMixin.loading" @click="formMixin.get">刷新</el-button>
    </template>
  </EleSheet>
</template>

<script>
import request from '@/utils/request.js'

import ScanFormGroup from './components/ScanFormGroup/index.vue'
import TargetFormGroup from './components/TargetFormGroup/index.vue'
import CronInput from '@/components/CronInput/index.vue'

import { dataMixin } from '@/mixins'

export default {
  dicts: ['main_task_type', 'risk_basic_type'],
  mixins: [
    dataMixin({
      ruleTableList: {
        default: [],
        async load() {
          const res = await request({
            url: '/common/ruleTableInfo/getRuleTableList',
            method: 'get'
          })

          return (res.data || []).map((item) => ({
            label: item.tableChineseName,
            value: item.id
          }))
        }
      }
    })
  ],
  components: {
    ScanFormGroup,
    TargetFormGroup,
    CronInput
  },
  data() {
    return {}
  },
  computed: {
    sheetProps() {
      const value = {
        title: '定时扫描规则',

        api: {
          add: async(params) => {
            const res = await request({
              url: '/common/ruleInfo/setRuleDetails',
              method: 'post',
              data: {
                ...params
              }
            })

            return res
          },
          edit: async(params) => {
            const isWaterLevelPoint = params.ruleScanDetailList.some(item => item.valueType === 'waterLevelPoint')

            if (!isWaterLevelPoint) {
              this.$message.warning('请添加一条水位点位扫描条件')
              throw new Error()
            }

            params.ruleScanDetailList = params.ruleScanDetailList.map(item => ({
              ...item,
              value: String(item.value)
            }))

            const res = await request({
              url: '/common/ruleInfo/setRuleDetails',
              method: 'post',
              data: {
                ...params
              }
            })

            return res
          },
          list: async(params) => {
            const res = await request({
              url: '/common/ruleInfo/list',
              method: 'get',
              params: {
                ...params
              }
            })

            Object.assign(res, {
              rows: (res?.rows || []).map((item) => ({
                ...item,
                ruleScanDetailList: (item.ruleScanDetailList || []).map(item_1 => ({
                  ...item_1
                })),
                $loading: false
              }))
            })

            return res
          },
          info: async(id) => {
            const res = await request({
              url: `/common/ruleInfo/${id}`,
              method: 'get',
              params: {}
            })

            if (!res.data?.ruleScanDetailList?.length) {
              Object.assign(res, {
                data: {
                  ...res.data,
                  ruleScanDetailList: this.initScanFields()
                }
              })
            }

            if (!res.data?.ruleHitActionDetailList?.length) {
              Object.assign(res, {
                data: {
                  ...res.data,
                  ruleHitActionDetailList: this.initTargetList()
                }
              })
            }

            if (res?.data) {
              Object.assign(res.data, {
                ruleScanDetailList: res.data.ruleScanDetailList.map(item => ({
                  ...item,
                  value: ['system', 'department', 'dict'].includes(item.dataSource) ? item.value.split(',') : item.value
                }))
              })
            }

            return res
          },
          remove: async(ids) => {
            const res = await request({
              url: `/common/ruleInfo/${ids}`,
              method: 'delete',
              params: {}
            })

            return res
          },
          export: '/common/ruleInfo/export'
        },

        infoProps: {
          title: true
        },

        hiddenActions: {
          add: !this.$checkPermi(['common:ruleInfo:setRuleDetails']),
          export: !this.$checkPermi(['common:ruleInfo:export']),
          remove: !this.$checkPermi(['common:ruleInfo:remove']),
          edit: !this.$checkPermi(['common:ruleInfo:edit']),
          info: true
        },

        tableProps: {
          height: '100%'
        },

        model: {
          ruleName: {
            type: 'text',
            label: '规则名称',
            align: 'left',
            form: {
              rules: true
            }
          },
          ruleType: {
            type: 'select',
            label: '安全风险类型',
            form: {
              rules: true,
              fieldProps: {
                on: {
                  change: (value, label, ctx) => {
                    this.$set(ctx.model, 'ruleScanDetailList', this.initScanFields())
                  }
                }
              }
            },
            options: this.dict.type.risk_basic_type
          },
          createTime: {
            type: 'text',
            label: '创建时间',
            search: {
              type: 'date-range',
              parameter: (data) => {
                return {
                  startTime: data?.[0],
                  endTime: data?.[1]
                }
              }
            },
            form: {
              hidden: true
            },
            width: 200
          },
          remarks: {
            type: 'text',
            label: '备注',
            form: {}
          },
          cronExpression: {
            hidden: true,
            label: 'cron执行表达式',
            form: {
              hidden: false
            }
          },
          isEnable: {
            type: 'switch',
            label: '启用/停用',
            search: {
              type: 'select',
              label: '启停状态',
              options: [
                {
                  label: '启用',
                  value: '1'
                },
                {
                  label: '停用',
                  value: '0'
                }
              ]
            },
            form: {
              hidden: true
            },
            options: [
              {
                label: void 0,
                value: '1'
              },
              {
                label: void 0,
                value: '0'
              }
            ]
          },
          runTime: {
            label: '执行时间',
            search: {
              hidden: true
            },
            form: {
              hidden: true
            },
            width: 200
          },
          nextRunTime: {
            label: '下次执行时间',
            search: {
              hidden: true
            },
            form: {
              hidden: true
            },
            width: 200
          },

          ruleScanDetailList: {
            hidden: true,
            label: '告警结论扫描条件',
            form: {
              hidden: false,
              value: this.initScanFields()
            }
          },
          ruleHitActionDetailList: {
            hidden: true,
            label: '命中结论',
            form: {
              hidden: false,
              value: this.initTargetList()
            }
          }
          // createBy: {
          //   type: 'text',
          //   label: '创建人',
          //   form: {
          //     hidden: true
          //   }
          // },
        }
      }

      return value
    }
  },
  methods: {
    initScanFields() {
      return [
        {
          fieldId: '',
          formula: '',
          formulaName: '',
          isScript: '0',
          scriptContent: '',
          value: void 0,
          valueType: 'fixedValue',
          valueTypeName: '固定值',
          dataSource: 'default'
        }
      ]
    },
    initTargetList() {
      return [
        {
          hitActionType: '',
          hitActionTypeName: '',
          actionValue: '',
          isScript: '0',
          scriptContent: ''
        }
      ]
    },
    async handleExecute(row) {
      try {
        await this.$confirm('请确认是否执行该规则？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
      } catch (error) {
        return error?.message
      }

      const params = {
        id: row.id
      }

      row.$loading = true

      const res = await request({
        url: `/common/ruleInfo/runRule/${params.id}`,
        method: 'get',
        params: {}
      })

      row.$loading = false

      if (res.code === 200) {
        this.$message.success(res.msg)
        this.$refs.sheetRef.getTableData()
      }
    },
    async onSwitchChange(isEnable, row) {
      const params = {
        id: row.id,
        isEnable
      }

      const res = await request({
        url: '/common/ruleInfo/setEnabled',
        method: 'post',
        data: {
          ...params
        }
      })

      if (res.code !== 200) {
        this.$message.warning(res.msg)
      }
    }
  }
}
</script>

<style></style>
