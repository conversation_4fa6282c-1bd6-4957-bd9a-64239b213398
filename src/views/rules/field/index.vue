<template>
  <EleSheet ref="sheetRef" v-bind="sheetProps" class="page-main page-main--full">
    <!-- <template #toolbar:after>
      <el-button type="success" @click="handleMapping">  </el-button>
    </template> -->

    <template #table:isEnable="{ columnProps }">
      <el-table-column v-slot="{ row }" v-bind="{ ...columnProps }">
        <el-switch
          v-model="row.isEnable"
          active-value="1"
          inactive-value="0"
          :disabled="!$checkPermi(['rules:field:updateStatus'])"
          @change="(value) => onSwitchChange(value, row)"
        />
      </el-table-column>
    </template>

    <template #table:after="{ editHandler, removeHandler }">
      <el-table-column v-slot="{ row }" label="操作" align="center" width="300" fixed="right">
        <el-button v-if="$checkPermi(['rules:field:edit'])" size="mini" type="text" @click="editHandler(row)">编辑</el-button>
        <el-button v-if="$checkPermi(['rules:field:config'])" type="text" size="mini" @click="handleMapping(row)"> 字段维护 </el-button>
        <el-button v-if="$checkPermi(['rules:field:edit'])" size="mini" type="text" @click="removeHandler(row)">删除</el-button>
      </el-table-column>
    </template>

    <template #after>
      <MappingDialog ref="mappingDialogRef" />
    </template>
  </EleSheet>
</template>

<script>
import request from '@/utils/request.js'

import MappingDialog from './components/MappingDialog/index.vue'
import { dataMixin } from '@/mixins'

export default {
  dicts: ['risk_basic_type'],
  mixins: [
    dataMixin({
      databaseTableList: {
        default: [],
        async load() {
          const res = await request({
            url: '/common/ruleTableInfo/getTables',
            method: 'post',
            data: {}
          })

          return (res.data || []).map((item) => ({
            label: `${item.table_name}（${item.table_comment}）`,
            value: item.table_name
          }))
        }
      }
    })
  ],
  components: {
    MappingDialog
  },
  data() {
    return {}
  },
  computed: {
    sheetProps() {
      const value = {
        title: '规则字段配置',

        api: {
          add: async(params) => {
            const res = await request({
              url: '/common/ruleTableInfo',
              method: 'post',
              data: {
                ...params
              }
            })

            return res
          },
          edit: async(params) => {
            const res = await request({
              url: '/common/ruleTableInfo',
              method: 'put',
              data: {
                ...params
              }
            })

            return res
          },
          list: async(params) => {
            const res = await request({
              url: '/common/ruleTableInfo/list',
              method: 'get',
              params: {
                ...params
              }
            })

            return res
          },
          info: async(id) => {
            const res = await request({
              url: `/common/ruleTableInfo/${id}`,
              method: 'get',
              params: {}
            })

            return res
          },
          remove: async(ids) => {
            const res = await request({
              url: `/common/ruleTableInfo/${ids}`,
              method: 'delete',
              params: {}
            })

            return res
          },
          export: '/common/ruleTableInfo/export'
        },

        infoProps: {
          title: true
        },

        tableProps: {
          height: '100%'
        },

        hiddenActions: {
          add: !this.$checkPermi(['rules:field:add']),
          edit: !this.$checkPermi(['rules:field:edit']),
          export: !this.$checkPermi(['rules:field:export']),
          remove: !this.$checkPermi(['rules:field:remove'])
        },

        model: {
          ruleType: {
            label: '安全风险类型',
            type: 'select',
            options: this.dict.type.risk_basic_type,
            align: 'left'
          },
          tableEnglishName: {
            label: '表英文名',
            type: 'text',
            options: this.dataMixin.databaseTableList,
            form: {
              type: 'select',
              rules: true,
              fieldProps: {
                on: {
                  change: (value, label, ctx) => {
                    ctx.model.tableChineseName = label.match(/（(.*?)）/)[1]
                  }
                }
              }
            },
            search: {
              type: 'select'
            }
          },
          tableChineseName: {
            type: 'text',
            label: '表中文名',
            align: 'left',
            form: {
              rules: true
            }
          },
          businessKey: {
            label: '业务主键',
            search: {
              hidden: true
            },
            form: {
              hidden: true
            }
          },
          isEnable: {
            type: 'switch',
            label: '启用/停用',
            search: {
              type: 'select',
              label: '启停状态',
              options: [
                {
                  label: '启用',
                  value: '1'
                },
                {
                  label: '停用',
                  value: '0'
                }
              ]
            },
            add: {
              hidden: true
            },
            table: {
              sort: 999
            }
          },
          updateTime: {
            type: 'text',
            label: '修改时间',
            search: {
              hidden: true
            },
            form: {
              hidden: true
            },
            width: 200
          },

          createTime: {
            type: 'text',
            label: '创建时间',
            search: {
              type: 'date-range',
              parameter: (data) => {
                return {
                  startTime: data?.[0],
                  endTime: data?.[1]
                }
              }
            },
            form: {
              hidden: true
            },
            width: 200
          }

          // createBy: {
          //   type: 'text',
          //   label: '创建人',
          //   form: {
          //     hidden: true
          //   }
          // },
          // status: {
          //   type: 'switch',
          //   label: '状态',
          //   search: {
          //     hidden: true
          //   },
          //   options: [
          //     {
          //       label: void 0,
          //       value: '1'
          //     },
          //     {
          //       label: void 0,
          //       value: '0'
          //     }
          //   ]
          // }
        }
      }

      return value
    }
  },
  methods: {
    handleMapping(row) {
      this.$refs.mappingDialogRef.open({
        params: {
          tableId: row.id,
          tableEnglishName: row.tableEnglishName
        }
      })
    },

    async onSwitchChange(isEnable, row) {
      const params = {
        id: row.id,
        isEnable
      }

      const res = await this.sheetProps.api.edit(params)

      if (res.code !== 200) {
        this.$message.warning(res.msg)
      }
    }
  }
}
</script>

<style></style>
