<template>
  <EleSheet ref="sheetRef" v-bind="sheetProps" class="page-main page-main--full" @list-success="onListSuccess">
    <template #before> </template>

    <template #form:icon:simple="{ model }">
      <el-popover
        placement="bottom-start"
        width="460"
        trigger="click"
        @show="$refs['iconSelect'].reset()"
      >
        <IconSelect ref="iconSelect" @selected="(name) => (model.icon = name)" />

        <template #reference>
          <el-input v-model="model.icon" placeholder="点击选择图标" readonly>
            <svg-icon
              v-if="model.icon"
              slot="prefix"
              :icon-class="model.icon"
              class="el-input__icon"
              style="height: 32px; width: 16px"
            />
            <i v-else slot="prefix" class="el-icon-search el-input__icon" />
          </el-input>
        </template>
      </el-popover>
    </template>

    <template #table:action:before="{ row, addHandler }">
      <el-button v-if="['A', 'M'].includes(row.codeEmbeddingPointType)" type="text" size="mini" :disabled="!$checkPermi(['rules:checkpoint:add'])" @click="addHandler({ parentId: row.id })">
        新增
      </el-button>
    </template>

    <template #after> </template>
  </EleSheet>
</template>

<script>
import request from '@/utils/request.js'

import { listPoint, getPoint, delPoint, addPoint, updatePoint } from '@/api/system/checkpoint.js'

import { pick } from 'lodash-es'

import IconSelect from '@/components/IconSelect'

import CheckpointTable from './components/CheckpointTable/index.vue'
import ElSelectTree from 'el-select-tree'

export default {
  dicts: ['sys_normal_disable_status'],
  components: {
    IconSelect,
    ElSelectTree
  },
  data() {
    return {
      idKey: 'id',
      tableData: []
    }
  },
  computed: {
    treeList() {
      const value = [{ id: 0, parentId: -1, codeEmbeddingPointName: '主类目', children: this.tableData }]

      return value
    },
    sheetProps() {
      const value = {
        title: '检查点配置',

        layout: 'search,toolbar,table',

        lazy: false,

        adapter: {
          table: CheckpointTable
        },

        api: {
          add: (params) => addPoint({ ...params }),
          edit: (params) => updatePoint({ ...params }),
          list: async(params) => {
            const res = await listPoint({ ...params, pageSize: 1000 })
            return {
              ...pick(res, ['code', 'msg']),
              rows: this.handleTree(res.rows || [], this.idKey)
            }
          },
          info: getPoint,
          remove: delPoint
          // export: '/system/point/export',
          // import: '/risk/point/import'
          // template: '/risk/point/importTemplate'
        },

        hiddenActions: {
          add: !this.$checkPermi(['rules:checkpoint:add']),
          remove: !this.$checkPermi(['rules:checkpoint:remove']),
          edit: !this.$checkPermi(['rules:checkpoint:edit']),
          info: true
        },

        infoProps: {
          title: true
        },

        idKey: this.idKey,

        tableProps: {
          rowKey: this.idKey,
          height: '100%'
        },

        model: {
          parentId: {
            label: '上级检查点',
            search: {
              hidden: true
            },
            form: {
              type: 'select-tree',
              rules: true,
              value: 0,
              fieldProps: {
                props: {
                  value: 'id',
                  label: 'codeEmbeddingPointName',
                  children: 'children'
                },
                checkStrictly: true,
                clearable: true,
                filterable: true
              },
              options: this.treeList
            }
          },
          codeEmbeddingPointName: {
            type: 'text',
            label: '检查点名称',
            form: {
              rules: true
            },
            table: {
              align: 'left'
            },
            width: 250
          },
          codeEmbeddingPointCode: {
            type: 'text',
            label: '检查点编号',
            form: {
              rules: true
            }
          },
          codeEmbeddingPointType: {
            type: 'select',
            label: '类型',
            options: [
              {
                label: '目录',
                value: 'A'
              },
              {
                label: '检查类型',
                value: 'M'
              },
              {
                label: '检查点',
                value: 'P'
              }
            ],
            form: {
              type: 'radio',
              value: 'A'
            }
          },
          icon: {
            label: '图标',
            search: {
              hidden: true
            }
          },
          orderNum: {
            type: 'input-number',
            label: '排序',
            width: 60,
            search: {
              hidden: true
            },
            table: {
              hidden: !this.$checkPermi(['rules:checkpoint:updateStatus'])
            },
            form: {
              value: 1
            }
          },
          status: {
            type: 'select',
            label: '状态',
            width: 80,
            form: {
              hidden: true
            },
            options: this.dict.type.sys_normal_disable_status
          },
          createTime: {
            type: 'text',
            label: '创建时间',
            search: {
              hidden: true,
              type: 'date-range',
              parameter: (data) => {
                return {
                  startTime: data?.[0],
                  endTime: data?.[1]
                }
              }
            },
            form: {
              hidden: true
            },
            width: 200
          }
        }
      }

      return value
    }
  },
  methods: {
    onListSuccess(data) {
      this.tableData = data
    }
  }
}
</script>

<style></style>
