<template>
  <el-table
    ref="tableRef"
    v-loading="tableMixin.loading"
    class="el-table--beautify"
    v-bind="{
      data: tableMixin.data,
      rowKey: tableMixin.rowKey,
    }"
    v-on="{
      ...$listeners,
      'selection-change': tableMixin.onSelectionChange,
      'row-click': tableMixin.onRowClick,
    }"
  >
    <el-table-column
      v-if="['multiple', true].includes(tableMixin.selectionType)"
      align="center"
      width="50"
      type="selection"
      v-bind="{ ...selectionProps }"
    />

    <slot name="before" />

    <el-table-column label="检查点名称" prop="codeEmbeddingPointName" width="250"></el-table-column>
    <el-table-column label="检查点编号" prop="codeEmbeddingPointCode" align="center"></el-table-column>

    <el-table-column v-slot="{ row }" label="类型" prop="codeEmbeddingPointType" align="center">
      <EleTagDict :value="row.codeEmbeddingPointType" :options="checkpointTypeDict" />
    </el-table-column>

    <el-table-column v-slot="{ row }" label="图标" prop="icon" align="center">
      <svg-icon v-if="row.icon" :icon-class="String(row.icon)" />
    </el-table-column>

    <el-table-column v-slot="{ row }" label="排序" prop="orderNum" align="center" width="150">
      <el-input-number v-model="row.orderNum" class="!w-full" :min="0" :disabled="!$checkPermi(['rules:checkpoint:updateStatus'])" @change="handleUpdate({ orderNum: $event }, row)"></el-input-number>
    </el-table-column>

    <el-table-column v-slot="{ row }" label="状态" prop="status" align="center">
      <el-switch v-model="row.status" active-value="1" inactive-value="0" :disabled="!$checkPermi(['rules:checkpoint:updateStatus'])" @change="handleUpdate({ status: $event }, row)"></el-switch>
    </el-table-column>

    <el-table-column label="创建时间" prop="createTime" align="center" width="200"></el-table-column>

    <slot name="after" />

    <template #empty>
      <slot name="empty" />
    </template>

    <template #append>
      <slot name="append" />
    </template>
  </el-table>
</template>

<script>
import {
  sheetMixin
} from '@/plugins/element-extends/mixins/index.js'
import { inheritComponentMethods } from '@/plugins/element-extends/helper.js'

export default {
  dicts: ['sys_normal_disable_status'],
  inheritAttrs: false,
  mixins: [sheetMixin({ modelKey: 'model', scope: 'table' })],
  props: {
    tableMixin: {
      type: Object,
      default: () => ({})
    },
    searchMixin: {
      type: Object,
      default: () => ({})
    },
    pagingMixin: {
      type: Object,
      default: () => ({})
    },
    tableColumnProps: {
      type: Object,
      default: () => ({})
    },
    showIndex: {
      type: Boolean,
      default: false
    },
    highlightRow: {
      type: Boolean,
      default: true
    },
    selection: {
      type: [Boolean, String],
      default: false
    },
    selectionRowKeys: {
      type: [Array, String],
      default: () => []
    },
    reserveSelection: {
      type: Boolean,
      default: true
    },
    selectionProps: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
    }
  },
  computed: {
    checkpointTypeDict() {
      return this.sheetMixin_mapModel.codeEmbeddingPointType?.options || []
    }
  },
  created() {
    if (this.$props.selection) {
      this.tableMixin.selectionType = this.$props.selection
    } else if (this.api.remove) {
      this.tableMixin.selectionType = 'multiple'
    }

    this.tableMixin.rowKey = this.$props.idKey
    this.tableMixin.api = this.$props.api

    if (!this.lazy) {
      this.getTableData()
    }
  },
  methods: {
    ...inheritComponentMethods('tableRef', ['doLayout']),

    async getTableData(...args) {
      try {
        const { data, params } = await this.tableMixin.getTableData(...args)

        this.$emit('list-success', data, params)
      } catch (error) {
        console.warn(error?.message)
        return false
      }

      await this.$nextTick()
    },

    async handleUpdate(changeParams, row) {
      const params = {
        id: row.id,
        ...changeParams
      }

      const res = await this.api.edit(params)

      if (res.code === 200) {
        this.$message.success(res.msg)
      } else {
        this.$message.warning(res.msg)
      }
    }
  }
}
</script>

<style></style>
