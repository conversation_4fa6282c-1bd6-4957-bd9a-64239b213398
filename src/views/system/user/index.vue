<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!--部门数据-->
      <el-col :span="4" :xs="24">
        <div class="page-main">
          <div class="head-container">
            <el-input
              v-model="deptName"
              placeholder="请输入部门名称"
              clearable
              size="small"
              prefix-icon="el-icon-search"
              style="margin-bottom: 20px"
            />
          </div>
          <div class="head-container">
            <el-tree
              ref="tree"
              :data="deptOptions"
              :props="defaultProps"
              :expand-on-click-node="false"
              :filter-node-method="filterNode"
              node-key="id"
              default-expand-all
              highlight-current
              @node-click="handleNodeClick"
            />
          </div>
        </div>
      </el-col>
      <!--用户数据-->
      <el-col :span="20" :xs="24">
        <div class="page-main">
          <el-form
            v-show="showSearch"
            ref="queryForm"
            class="sa-query"
            :model="queryParams"
            size="small"
            :inline="true"
            label-width="68px"
          >
            <el-form-item label="从账号" prop="userName">
              <el-input
                v-model="queryParams.userName"
                placeholder="请输入从账号"
                clearable
                @keyup.enter.native="handleQuery"
              />
            </el-form-item>
            <el-form-item label="用户名称" prop="nickName">
              <el-input
                v-model="queryParams.nickName"
                placeholder="请输入用户名称"
                clearable
                @keyup.enter.native="handleQuery"
              />
            </el-form-item>
            <el-form-item label="邮箱" prop="email">
              <el-input
                v-model="queryParams.email"
                placeholder="请输入邮箱"
                clearable
                @keyup.enter.native="handleQuery"
              />
            </el-form-item>
            <el-form-item label="手机号码" prop="phoneNumber">
              <el-input
                v-model="queryParams.phoneNumber"
                placeholder="请输入手机号码"
                clearable
                @keyup.enter.native="handleQuery"
              />
            </el-form-item>

            <el-form-item label="部署人员类型" prop="personType">
              <EleSelectDict v-model="queryParams.personType" :options="dict.type.person_type" />
            </el-form-item>

            <el-form-item label="状态" prop="status">
              <el-select v-model="queryParams.status" placeholder="用户状态" clearable>
                <el-option
                  v-for="dict in dict.type.sys_user_normal_disable"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item class="daterange" label="创建时间">
              <el-date-picker
                v-model="dateRange"
                value-format="yyyy-MM-dd"
                type="daterange"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              />
            </el-form-item>
            <el-form-item label="业务系统">
              <el-select
                v-model="queryParams.systemNames"
                placeholder="请选择业务系统"
                clearable
                multiple
                collapse-tags
                filterable
              >
                <el-option
                  v-for="dict in systemNamesSelect"
                  :key="dict.standardSystemName"
                  :label="dict.standardSystemName"
                  :value="dict.standardSystemName"
                />
              </el-select>
            </el-form-item>

            <el-form-item label="处置分组" prop="groupNames">
              <el-input v-model="queryParams.groupNames" placeholder="请输入" />
            </el-form-item>

            <el-form-item label="所属租户" prop="tenId">
              <TenantSelect v-model="queryParams.tenId" show-nothing @label-change="(label) => (queryParams.tenName = label)" />
            </el-form-item>

            <el-form-item label="角色" prop="roleIds" disabled>
              <RoleSelect :key="queryParams.tenId" v-model="queryParams.roleIds" show-nothing :disabled="!queryParams.tenId" v-bind="{ ...!queryParams.tenId ? { title: '请先选择所属租户' } : {} }" :params="{ tenId: queryParams.tenId }" multiple collapse-tags />
            </el-form-item>

            <el-form-item class="query-handle">
              <el-button type="primary" plain icon="el-icon-search" @click="handleQuery">搜索</el-button>
              <el-button icon="el-icon-refresh-left" @click="resetQuery">重置</el-button>
              <EleSheetSearchMore :parent-ref="()=> $refs.queryForm" />
            </el-form-item>
          </el-form>

          <div class="sa-title">
            <sa-toolbar :show-search.sync="showSearch" @queryTable="getList" />
            <el-button
              v-hasPermi="['system:user:add']"
              type="primary"
              icon="el-icon-plus"
              @click="handleAdd"
            >新增
            </el-button>
            <el-button
              v-hasPermi="['system:user:import']"
              type="primary"
              icon="el-icon-upload2"
              @click="handleImport"
            >导入
            </el-button>
            <el-button
              v-hasPermi="['system:user:export']"
              type="primary"
              icon="el-icon-download"
              @click="handleExport"
            >导出
            </el-button>
            <el-button
              v-hasPermi="['system:user:edit']"
              type="warning"
              icon="el-icon-edit-outline"
              :disabled="multiple"
              @click="handleEditBatch"
            >批量修改
            </el-button>
          </div>

          <!-- <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
              <el-button
                type="success"
                plain
                icon="el-icon-edit"
                size="mini"
                :disabled="single"
                @click="handleUpdate"
                v-hasPermi="['system:user:edit']"
                >修改</el-button
              >
            </el-col>
            <el-col :span="1.5">
              <el-button
                type="danger"
                plain
                icon="el-icon-delete"
                size="mini"
                :disabled="multiple"
                @click="handleDelete"
                v-hasPermi="['system:user:remove']"
                >删除</el-button
              >
            </el-col>
            <right-toolbar
              :showSearch.sync="showSearch"
              @queryTable="getList"
              :columns="columns"
            ></right-toolbar>
          </el-row> -->

          <el-table
            v-loading="loading"
            class="sa-table"
            :data="userList"
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" width="50" align="center" />
            <!-- <el-table-column
              v-if="columns[0].visible"
              key="userId"
              label="用户编号"
              align="center"
              prop="userId"
            /> -->
            <el-table-column
              v-if="columns[1].visible"
              key="userName"
              label="从账号"
              align="center"
              prop="userName"
              width="150"
              :show-overflow-tooltip="true"
            />
            <el-table-column
              v-if="columns[2].visible"
              key="nickName"
              label="名称"
              align="center"
              prop="nickName"
              width="150"
              :show-overflow-tooltip="true"
            />
            <el-table-column
              v-if="columns[3].visible"
              key="effectiveDeptName"
              label="部门"
              align="center"
              prop="effectiveDeptName"
              :show-overflow-tooltip="true"
            />
            <el-table-column
              label="处置分组"
              align="center"
              prop="groupNames"
              :show-overflow-tooltip="true"
            />
            <!-- <el-table-column
              label="邮箱"
              align="center"
              prop="email"
              width="120"
              :show-overflow-tooltip="true"
            /> -->
            <el-table-column
              v-if="columns[4].visible"
              key="phoneNumber"
              label="手机号码"
              align="center"
              prop="phoneNumber"
              width="120"
            />

            <el-table-column
              v-if="columns[7].visible"
              key="tenName"
              label="所属租户"
              align="center"
              prop="tenName"
              width="120"
            />
            <el-table-column
              key="roleNames"
              label="角色"
              align="center"
              prop="roleNames"
              width="200"
              show-overflow-tooltip
            >
            </el-table-column>

            <el-table-column
              key="personType"
              v-slot="{ row }"
              label="部署人员类型"
              align="center"
              prop="personType"
              width="200"
            >
              <EleTagDict :value="row.personType" :options="dict.type.person_type" />
            </el-table-column>

            <el-table-column v-if="columns[5].visible" key="status" label="状态" align="center">
              <template slot-scope="scope">
                <el-switch
                  v-model="scope.row.status"
                  active-value="1"
                  inactive-value="0"
                  :disabled="!$checkPermi(['system:user:updateStatus'])"
                  @change="handleStatusChange(scope.row)"
                />
              </template>
            </el-table-column>

            <el-table-column
              label="生效时间"
              align="center"
              prop="effectDate"
              width="200"
              show-overflow-tooltip
            >
            </el-table-column>

            <el-table-column
              label="过期时间"
              align="center"
              prop="expireDate"
              width="200"
              show-overflow-tooltip
            >
            </el-table-column>

            <el-table-column
              v-if="columns[6].visible"
              label="创建时间"
              align="center"
              prop="createTime"
              width="200"
            >
              <template slot-scope="scope">
                <span>{{ parseTime(scope.row.createTime) }}</span>
              </template>
            </el-table-column>

            <el-table-column
              fixed="right"
              label="操作"
              align="center"
              width="200"
              class-name="small-padding fixed-width"
            >
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  type="text"
                  @click="handleView(scope.row)"
                >查看
                </el-button>

                <el-button
                  v-hasPermi="['system:user:edit']"
                  size="mini"
                  type="text"
                  @click="handleUpdate(scope.row)"
                >修改
                </el-button>
                <el-button
                  v-hasPermi="['system:user:remove']"
                  size="mini"
                  type="text"
                  @click="handleDelete(scope.row)"
                >删除
                </el-button>
                <el-dropdown
                  v-hasPermi="['system:user:resetPwd', 'system:user:edit']"
                  size="mini"
                  @command="(command) => handleCommand(command, scope.row)"
                >
                  <el-button size="mini" type="text">更多</el-button>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item
                      v-hasPermi="['system:user:resetPwd']"
                      command="handleResetPwd"
                      icon="el-icon-key"
                    >重置密码
                    </el-dropdown-item>
                    <el-dropdown-item
                      v-hasPermi="['system:user:edit']"
                      command="handleAuthRole"
                      icon="el-icon-circle-check"
                    >分配角色
                    </el-dropdown-item>

                    <TenantDistributionAction v-slot="{ trigger }" @success="getList">
                      <el-dropdown-item
                        v-hasPermi="['system:user:tenant']"
                        icon="el-icon-coordinate"
                        @click.native="() => trigger({ params: scope.row })"
                      >
                        分配租户
                      </el-dropdown-item>
                    </TenantDistributionAction>
                  </el-dropdown-menu>
                </el-dropdown>
              </template>
            </el-table-column>
          </el-table>

          <div class="sa-footer sa-row-center">
            <sa-pagination
              v-show="total > 0"
              :total="total"
              :page.sync="queryParams.pageNum"
              :limit.sync="queryParams.pageSize"
              @pagination="getList"
            />
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 添加或修改用户配置对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px" :disabled="formDisabled">
        <el-row type="flex" class="!flex-wrap">
          <el-col :span="12">
            <el-form-item label="名称" prop="nickName">
              <el-input v-model="form.nickName" placeholder="请输入名称" maxlength="30" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="归属组" prop="deptId">
              <treeselect
                v-model="form.deptId"
                :disabled="formDisabled"
                :options="deptOptions"
                :show-count="true"
                placeholder="请选择归属组"
                @select="onDeptSelect"
                @input="onDeptInput"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="手机号码" prop="phoneNumber">
              <el-input v-model="form.phoneNumber" placeholder="请输入手机号码" maxlength="11" />
            </el-form-item>
          </el-col>
          <!-- v-if="!form.deptType || ![1, 2, 3].includes(Number(form.deptType))" -->
          <el-col v-if="[4].includes(Number(form.deptType))" :span="12">
            <el-form-item label="归属部门" prop="effectiveDeptName">
              <el-input v-model="form.effectiveDeptName" :placeholder="form.deptName || '请选择归属组'" maxlength="50" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="邮箱" prop="email">
              <el-input v-model="form.email" placeholder="请输入邮箱" maxlength="50" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="从账号" prop="userName">
              <el-input v-model="form.userName" placeholder="请输入从账号" maxlength="30" />
            </el-form-item>
          </el-col>
          <el-col v-if="!form.userId" :span="12">
            <el-form-item label="用户密码" prop="password">
              <el-input
                v-model="form.password"
                placeholder="请输入用户密码"
                type="password"
                maxlength="20"
                show-password
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="部署人员类型" prop="personType">
              <EleSelectDict v-model="form.personType" :options="dict.type.person_type" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态">
              <el-radio-group v-model="form.status">
                <el-radio
                  v-for="dict in dict.type.sys_user_normal_disable"
                  :key="dict.value"
                  :label="dict.value"
                >{{ dict.label }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="是否开启harbor">
              <el-radio-group v-model="form.isOpenHarbor">
                <el-radio :label="1">是</el-radio>
                <el-radio :label="0">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="所属租户" prop="tenId">
              <TenantSelect v-model="form.tenId" @change="updateRoleOptions(form, $event)" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="角色" prop="roleIds">
              <el-select ref="roleIdsRef" v-model="form.roleIds" title="请先选择所属租户" placeholder="请选择角色" multiple :disabled="!form.tenId">
                <el-option
                  v-for="item in roleOptions"
                  :key="item.roleId"
                  :label="item.roleName"
                  :value="item.roleId"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="业务系统">
              <el-select
                v-model="form.systemNames"
                placeholder="请选择业务系统"
                clearable
                multiple
                filterable
              >
                <el-option
                  v-for="dict in systemNamesSelect"
                  :key="dict.standardSystemName"
                  :label="dict.standardSystemName"
                  :value="dict.standardSystemName"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col v-if="form.userId" :span="12">
            <el-form-item label="处置分组">
              <el-input v-model="form.groupNames" disabled></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="数据类型">
              <el-select v-model="form.dataType" placeholder="数据类型" clearable>
                <el-option
                  v-for="dict in dict.type.platform_data_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="Number(dict.value)"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="备注">
              <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div v-if="!formDisabled" slot="footer" class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
        <el-button type="primary" @click="submitForm">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 用户导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload" />
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div slot="tip" class="el-upload__tip text-center">
          <div slot="tip" class="el-upload__tip">
            <el-checkbox v-model="upload.updateSupport" />
            是否更新已经存在的用户数据
          </div>
          <span>仅允许导入xls、xlsx格式文件。</span>
          <el-link
            type="primary"
            :underline="false"
            style="font-size: 12px; vertical-align: baseline"
            @click="importTemplate"
          >下载模板
          </el-link>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button @click="upload.open = false">取 消</el-button>
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
      </div>
    </el-dialog>

    <system-select ref="systemSelectRef" @confirm="onSelectSystemConfirm" />

    <EditBatchDialog ref="editBatchDialogRef" />
  </div>
</template>

<script>
import md5 from 'js-md5'
import {
  listUser,
  getUser,
  delUser,
  addUser,
  updateUser,
  resetUserPwd,
  changeUserStatus,
  deptTreeSelect
} from '@/api/system/user'
import request from '@/utils/request'
import { getToken } from '@/utils/auth'
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import { mapGetters } from 'vuex'
import { validEmail } from '@/utils/validate'
import SystemSelect from '@/views/res/system/SystemSelect.vue'

import TenantDistributionAction from '@/components/business/TenantDistributionAction/index.vue'

import RoleSelect from '@/components/business/RoleSelect/index.vue'

import EditBatchDialog from './components/EditBatchDialog/index.vue'

export default {
  name: 'User',
  dicts: ['sys_user_normal_disable', 'sys_user_sex', 'platform_data_type', 'person_type'],
  components: { Treeselect, SystemSelect, TenantDistributionAction, RoleSelect, EditBatchDialog },
  data() {
    return {
      systemNamesSelect: [],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      selectionData: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 用户表格数据
      userList: null,
      // 弹出层标题
      title: '',
      // 部门树选项
      deptOptions: undefined,
      // 是否显示弹出层
      open: false,
      // 部门名称
      deptName: undefined,
      // 默认密码
      initPassword: undefined,
      // 日期范围
      dateRange: [],
      // 岗位选项
      postOptions: [],
      // 角色选项
      roleOptions: [],
      // systemOptions: [],
      // 表单参数
      form: {
        userId: undefined,
        deptId: undefined,
        deptType: undefined,
        effectiveDeptName: undefined,
        userName: undefined,
        nickName: undefined,
        password: undefined,
        phoneNumber: undefined,
        email: undefined,
        sex: undefined,
        status: '1',
        isOpenHarbor: 0,
        remark: undefined,
        postIds: [],
        roleIds: null,
        systemId: [],
        sysUserSystemList: [],
        groupNames: undefined,
        systemName: null,
        dataType: null,
        harborPassword: null,
        systemNames: [],
        tenId: undefined,
        tenName: undefined,
        personType: void 0
      },
      defaultProps: {
        children: 'children',
        label: 'label'
      },
      // 用户导入参数
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: '',
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: { Authorization: 'Bearer ' + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + '/system/user/importData'
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        userName: undefined,
        phoneNumber: undefined,
        nickName: undefined,
        email: undefined,
        status: undefined,
        deptId: undefined,
        tenId: undefined,
        tenName: undefined,
        systemNames: [],
        groupNames: undefined,
        roleIds: [],
        personType: void 0
      },
      // 列信息
      columns: [
        { key: 0, label: `用户编号`, visible: true },
        { key: 1, label: `从账号`, visible: true },
        { key: 2, label: `名称`, visible: true },
        { key: 3, label: `部门`, visible: true },
        { key: 4, label: `手机号码`, visible: true },
        { key: 5, label: `状态`, visible: true },
        { key: 6, label: `创建时间`, visible: true },
        { key: 7, label: `租户名称`, visible: true }
      ],
      // 表单校验
      rules: {
        tenId: [{ required: true, message: '所属租户不能为空', trigger: 'blur' }],
        userName: [
          {
            required: true,
            validator: (rule, value, callback) => {
              if (value) {
                // 注册harbor才检测
                if (this.form.isOpenHarbor == 1) {
                  const exists = new FormData()
                  exists.append('target', 'username')
                  exists.append('value', value)
                  request({
                    url: '/harbor/c/userExists',
                    method: 'post',
                    data: exists
                  }).then((response) => {
                    if (response.data) {
                      callback(new Error('从账号已经存在。'))
                    } else {
                      callback()
                    }
                  })
                } else {
                  callback()
                }
              } else {
                callback(new Error('请输入从账号'))
              }
            },
            trigger: ['blur']
          }
        ],
        nickName: [{ required: true, message: '名称不能为空', trigger: 'blur' }],
        password: [
          { required: true, message: '密码不能为空', trigger: 'blur' }
          // {
          //   required: true,
          //   validator: (rule, value, callback) => {
          //     let reg = /^(?=.*[A-Z])(?=.*[a-z])(?=.*[0-9])[A-Za-z0-9 _]{8,15}$/;
          //     if (!reg.test(value)) {
          //       callback(
          //         new Error(
          //           '密码长度在8到20之间且需包含至少一个大写字符，一个小写字符和一个数字',
          //         ),
          //       );
          //     } else {
          //       callback();
          //     }
          //   },
          //   trigger: ['blur'],
          // },
        ],
        email: [
          {
            required: true,
            validator: (rule, value, callback) => {
              if (!validEmail(value)) {
                callback(new Error('请使用正确的邮箱地址，比如****************。'))
              } else {
                // if (!this.form.userId) {
                //   let emailExists = new FormData();
                //   emailExists.append('target', 'email');
                //   emailExists.append('value', value);
                //   request({
                //     url: '/harbor/c/userExists',
                //     method: 'post',
                //     data: emailExists,
                //   }).then((response) => {
                //     if (response.data) {
                //       callback(new Error('邮件地址已经存在。'));
                //     } else {
                //       callback();
                //     }
                //   });
                // } else {
                callback()
                // }
              }
            },
            trigger: ['blur']
          }
        ],
        phoneNumber: [
          {
            pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
            message: '请输入正确的手机号码',
            trigger: 'blur'
          }
        ],
        roleIds: [{ required: true, message: '角色为必填项', trigger: 'blur' }]
      },

      isSystemId: false,
      isRoleId: false,

      formDisabled: false
    }
  },
  computed: {
    ...mapGetters(['roles', 'userInfo'])
  },
  watch: {
    // 根据名称筛选部门树
    deptName(val) {
      this.$refs.tree.filter(val)
    }
  },
  created() {
    this.getList()
    this.getDeptTree()
    this.getSystemNamesSelect()
    this.getConfigKey('sys.user.initPassword').then((response) => {
      this.initPassword = response.msg
    })
  },
  activated() {
    this.getDeptTree()
  },
  methods: {
    onDeptInput(value) {
      if (!value) {
        this.form.effectiveDeptName = void 0
        this.form.deptType = undefined
      }
    },
    onDeptSelect(item) {
      this.form.effectiveDeptName = item.effectiveDeptName
      this.form.deptType = item.deptType
    },
    handleEditBatch() {
      this.$refs.editBatchDialogRef.open({
        params: {
          userIds: this.ids
        },
        success: () => {
          this.getList()
        }
      })
    },

    getSystemNamesSelect() {
      request({
        url: '/risk/operator/groupUser/queryAllStandardList',
        method: 'get'
      }).then((response) => {
        this.systemNamesSelect = response.data
      })
    },

    /** 查询用户列表 */
    getList() {
      this.loading = true

      const systemNames = this.queryParams.systemNames?.length ? this.queryParams.systemNames.join(',') : ''
      const roleIds = this.queryParams.roleIds?.length ? this.queryParams.roleIds.join(',') : ''

      const params = {
        ...this.queryParams,
        ...systemNames ? { systemNames } : {},
        ...roleIds ? { roleIds } : {}
      }

      listUser(this.addDateRange(params, this.dateRange)).then((response) => {
        this.userList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    /** 查询部门下拉树结构 */
    getDeptTree() {
      deptTreeSelect().then((response) => {
        this.deptOptions = response.data
      })
    },
    // 筛选节点
    filterNode(value, data) {
      if (!value) return true
      return data?.label?.indexOf?.(value) !== -1
    },
    // 节点单击事件
    handleNodeClick(data) {
      this.queryParams.deptId = data.id
      this.handleQuery()
    },
    // 用户状态修改
    handleStatusChange(row) {
      const text = row.status === '1' ? '启用' : '停用'
      this.$modal
        .confirm('确认要"' + text + '""' + row.userName + '"用户吗？')
        .then(function() {
          return changeUserStatus(row.userId, row.status)
        })
        .then(() => {
          this.$modal.msgSuccess(text + '成功')
        })
        .catch(function() {
          row.status = row.status === '1' ? '0' : '1'
        })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = []
      this.resetForm('queryForm')
      this.queryParams.deptId = undefined
      this.$refs.tree.setCurrentKey(null)
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.userId)
      this.selectionData = selection
      this.single = selection.length != 1
      this.multiple = !selection.length
    },
    // 更多操作触发
    handleCommand(command, row) {
      switch (command) {
        case 'handleResetPwd':
          this.handleResetPwd(row)
          break
        case 'handleAuthRole':
          this.handleAuthRole(row)
          break
        default:
          break
      }
    },
    updateRoleOptions(form, value) {
      getUser(form.userId, { tenId: value }).then((response) => {
        this.roleOptions = response.roles
      })
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.formDisabled = false
      this.form = this.$options.data().form
      getUser().then((response) => {
        this.postOptions = response.posts
        this.roleOptions = response.roles
        this.form.tenId = this.$store.getters.userInfo.tenId
        this.form.tenName = this.$store.getters.userInfo.tenName
        this.open = true
        this.title = '添加用户'
        this.form.password = this.initPassword
      })
      // this.getSystemOptions();
    },
    handleView(row) {
      this.handleUpdate(row)
      this.formDisabled = true
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.formDisabled = false
      this.reset()
      const userId = row.userId || this.ids
      getUser(userId).then((response) => {
        this.form = {
          ...response.data,
          systemNames: []
        }
        if (response.data.systemName) {
          this.form.systemNames = response.data.systemName.split(',')
        }
        this.postOptions = response.posts
        this.roleOptions = response.roles
        this.$set(this.form, 'postIds', response.postIds)
        this.$set(this.form, 'roleIds', response.roleIds)
        this.open = true
        this.title = '修改用户'
        this.form.password = ''

        // 处理字段
        this.form.systemId = []
        this.form.sysUserSystemList.forEach((item) => {
          this.form.systemId.push(item.systemId)
        })

        if (this.form.tenId) {
          this.updateRoleOptions(this.form, this.form.tenId)
        }
      })
      // this.getSystemOptions();
    },
    /** 重置密码按钮操作 */
    handleResetPwd(row) {
      this.$prompt('请输入"' + row.userName + '"的新密码', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        closeOnClickModal: false,
        inputPattern: /^.{5,20}$/,
        inputErrorMessage: '用户密码长度必须介于 5 和 20 之间'
      })
        .then(({ value }) => {
          resetUserPwd(row.userId, value).then((response) => {
            this.$modal.msgSuccess('修改成功，新密码是：' + value)
          })
        })
        .catch(() => {
        })
    },
    /** 分配角色操作 */
    handleAuthRole: function(row) {
      const userId = row.userId
      const tenId = row.tenId

      this.$router.push('/system/user-auth/role/' + [userId, tenId].join(','))
    },
    /** 提交按钮 */
    submitForm: function() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          const submitForm = JSON.parse(JSON.stringify(this.form))
          submitForm.systemId = ''
          submitForm.systemName = submitForm.systemNames ? submitForm.systemNames.join() : ''

          if (this.form.userId != undefined) {
            updateUser(submitForm).then(async(response) => {
              // isOpenHarbor 不可编辑
              if (submitForm.isOpenHarbor == 1) {
                request({
                  url: '/harbor/api/v2.0/users',
                  method: 'get',
                  params: {
                    page: 1,
                    page_size: 1,
                    username: submitForm.userName
                  }
                }).then(async(response) => {
                  if (response.data.length) {
                    request({
                      url: `/harbor/api/v2.0/users/${response.data[0].user_id}`,
                      method: 'put',
                      data: {
                        username: submitForm.userName,
                        email: submitForm.email,
                        realname: submitForm.userName,
                        password: submitForm.harborPassword,
                        comment: null
                      }
                    }).then((response) => {
                      this.$modal.msgSuccess('修改成功')
                      this.open = false
                      this.getList()
                    })
                  } else {
                    await this.harborUsers(submitForm)
                    this.$modal.msgSuccess('修改成功')
                    this.open = false
                    this.getList()
                  }
                })
              } else {
                // 删除harbor
                request({
                  url: '/harbor/api/v2.0/users',
                  method: 'get',
                  params: {
                    page: 1,
                    page_size: 1,
                    username: submitForm.userName
                  }
                }).then((response) => {
                  if (response.data.length) {
                    request({
                      url: `/harbor/api/v2.0/users/${response.data[0].user_id}`,
                      method: 'delete'
                    }).then((response) => {
                      this.$modal.msgSuccess('修改成功')
                      this.open = false
                      this.getList()
                    })
                  }
                }).finally(() => {
                  this.$modal.msgSuccess('修改成功')
                  this.open = false
                  this.getList()
                })
              }
            })
          } else {
            submitForm.harborPassword = md5(submitForm.password) + 'A'
            addUser(submitForm).then(async(response) => {
              if (submitForm.isOpenHarbor == 1) {
                await this.harborUsers(submitForm)
              }
              this.$modal.msgSuccess('新增成功')
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    harborUsers(submitForm) {
      return new Promise((resolve, reject) => {
        request({
          url: '/harbor/api/v2.0/users',
          method: 'post',
          data: {
            username: submitForm.userName,
            email: submitForm.email,
            realname: submitForm.userName,
            password: submitForm.harborPassword,
            comment: null
          }
        }).then((response) => {
          request({
            url: `/harbor/api/v2.0/users/${response.user_id}/sysadmin`,
            method: 'put',
            data: {
              user_id: response.user_id,
              sysadmin_flag: true
            }
          }).then((response) => {
            resolve(response)
          })
        })
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const that = this
      const userIds = row.userId || this.ids
      let labels = ''
      if (row.userName) {
        labels = row.userName
      } else {
        labels = []
        this.selectionData.form((item) => {
          labels.push(item.userName)
        })
        labels = labels.join(',')
      }
      this.$modal
        .confirm('是否确认删除用户"' + labels + '"的数据项？')
        .then(function() {
          request({
            url: '/harbor/api/v2.0/users',
            method: 'get',
            params: {
              page: 1,
              page_size: 1,
              username: labels
            }
          }).then((response) => {
            if (response.data.length) {
              // 第三方删除
              request({
                url: `/harbor/api/v2.0/users/${response.data[0].user_id}`,
                method: 'delete'
              }).then((response) => {
                request({
                  url: '/system/user/' + userIds,
                  method: 'delete'
                }).then((response) => {
                  that.getList()
                  that.$modal.msgSuccess('删除成功')
                })
              })
            } else {
              request({
                url: '/system/user/' + userIds,
                method: 'delete'
              }).then((response) => {
                that.getList()
                that.$modal.msgSuccess('删除成功')
              })
            }
          })
        })
        .catch(() => {
        })
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        'system/user/export',
        {
          ...this.queryParams
        },
        `user_${new Date().getTime()}.xlsx`
      )
    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = '用户导入'
      this.upload.open = true
    },
    /** 下载模板操作 */
    importTemplate() {
      this.download(
        'system/user/importTemplate',
        {},
        `user_template_${new Date().getTime()}.xlsx`
      )
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false
      this.upload.isUploading = false
      this.$refs.upload.clearFiles()
      this.$alert(
        '<div style=\'overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 20px;\'>' +
        response.msg +
        '</div>',
        '导入结果',
        { dangerouslyUseHTMLString: true }
      )
      this.getList()
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit()
    },
    // getSystemOptions() {
    //   request({
    //     url: '/res/system/list',
    //     method: 'get',
    //   }).then((response) => {
    //     this.systemOptions = response;
    //   });
    // },
    // onChangeSystemId() {
    //   this.$nextTick(() => {
    //     this.form.sysUserSystemList = [];
    //     this.$refs.systemIdRef.selected.forEach((item) => {
    //       this.form.sysUserSystemList.push({
    //         systemId: item.currentValue,
    //         systemName: item.currentLabel,
    //       });
    //     });
    //   });
    // },
    onSelectSystem() {
      this.$refs.systemSelectRef.show()
    },
    onSelectSystemConfirm(data) {
      this.form.sysUserSystemList = []
      data.forEach((item) => {
        this.form.sysUserSystemList.push({
          systemId: item.id,
          systemName: item.bussSysName
        })
      })
    },
    onDeleteSystem(index) {
      this.form.sysUserSystemList.splice(index, 1)
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  background: transparent;
  padding: 0;
}

.sa-template-wrap {
  line-height: 42px;

  .sa-template-header {
    display: flex;
    align-items: center;
    background: #eee;
  }

  .sa-template-item {
    display: flex;
    align-items: center;
  }

  .sa-template-key {
    flex: 1;
    padding: 0 10px;
  }

  .sa-template-oper {
    flex-shrink: 0;
    width: 120px;
    padding: 0 10px;
  }
}
</style>
