<template>
  <div class="page-main">
    <!-- 搜索工作栏 -->
    <el-form
      v-show="showSearch"
      ref="queryForm"
      class="sa-query"
      :model="queryParams"
      size="small"
      :inline="true"
    >
      <el-form-item label="规则名称" prop="ruleName">
        <el-input
          v-model="queryParams.ruleName"
          placeholder="请输入规则名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="服务商" prop="containerSafeType">
        <DictSelect
          v-model="queryParams.containerSafeType"
          dict-type="whiteProvider"
          placeholder="请选择服务商"
          clearable
        />
      </el-form-item>
      <el-form-item label="开启状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择服务商" size="small" clearable>
          <el-option label="开启" :value="1" />
          <el-option label="关闭" :value="0" />
        </el-select>
      </el-form-item>
      <el-form-item class="query-handle">
        <el-button type="primary" plain icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh-left" @click="resetQuery">重置</el-button>
        <EleSheetSearchMore :parent-ref="()=> $refs.queryForm" />
      </el-form-item>
    </el-form>
    <!-- 操作工具栏 -->
    <div class="sa-title">
      <sa-toolbar :show-search.sync="showSearch" @queryTable="getList" />
      <el-button
        v-if="$checkPermi(['system:whiteList:add'])"
        type="primary"
        icon="el-icon-plus"
        @click="handleAdd()"
      >新增</el-button>
    </div>
    <!-- 列表 -->
    <el-table v-loading="loading" class="sa-table" :data="list">
      <el-table-column label="规则名称" align="center" prop="ruleName" />
      <el-table-column label="告警类型" align="center" prop="warnTypeName" />
      <el-table-column label="服务商" align="center" prop="containerSafeType">
        <template #default="{ row }">
          <DictTagV2 :value="row.containerSafeType" dict-type="whiteProvider" text />
        </template>
      </el-table-column>
      <el-table-column label="数据来源" align="center" prop="source">
        <template #default="{ row }">
          <DictTagV2 :value="row.source" dict-type="whiteSource" text />
        </template>
      </el-table-column>
      <el-table-column label="开启状态" align="center" prop="status">
        <template #default="{ row }">
          <el-tag :type="row.status === 0 ? 'danger' : ''">{{
            row.status === 1 ? '开启' : '关闭'
          }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="{ row }">
          <el-button
            v-if="[1].includes(row.source) || [100].includes(row.containerSafeType)"
            size="mini"
            type="text"
            :disabled="!$checkPermi(['system:whiteList:edit'])"
            @click="handleEdit(row)"
          >编辑</el-button>
          <el-button v-else size="mini" type="text" @click="handleInfo(row)">查看</el-button>
          <el-button
            v-if="![100].includes(row.containerSafeType)"
            size="mini"
            type="text"
            :disabled="!$checkPermi(['system:whiteList:upgrade:generalRule'])"
            @click="handleUpgrade(row)"
          >升级为通用规则</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 对话框(添加 / 修改) -->
    <FormDialog ref="formDialogRef" @success="getList" />
  </div>
</template>

<script>
import { getWhiteListPage, riskWarnUpdateContainerSafeType } from '@/api/system/whiteList'
import FormDialog from './components/FormDialog/index.vue'
export default {
  name: 'WhiteList',
  components: { FormDialog },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 白名单列表
      list: [],
      // 是否显示弹出层
      showFormDialog: false,
      // 要编辑的id
      editId: '',
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        ruleName: null,
        containerSafeType: null,
        status: null
      }
    }
  },

  created() {
    this.getList()
  },

  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true
      // 执行查询
      getWhiteListPage(this.queryParams).then((response) => {
        this.list = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },

    handleAdd() {
      this.$refs.formDialogRef.open()
    },

    handleEdit(row) {
      this.$refs.formDialogRef.open({ id: row.id })
    },

    handleInfo(row) {
      this.$refs.formDialogRef.open({ id: row.id, infoMode: true })
    },

    async handleUpgrade(row) {
      try {
        await this.$confirm('确认要升级为通用规则吗?', '提示')
      } catch (error) {
        console.warn(error.message)
        return false
      }

      const params = {
        id: row.id
      }

      this.loading = true

      const res = await riskWarnUpdateContainerSafeType(params)

      this.loading = false

      if (res.code == 200) {
        this.$modal.msgSuccess(res.msg)
        this.getList()
      }
    }
  }
}
</script>
