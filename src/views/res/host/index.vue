<template>
  <div class="page-main">
    <el-form
      v-show="showSearch"
      ref="queryForm"
      class="sa-query"
      :model="queryParams"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="资源主机名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入资源主机名称"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="资源池" prop="resPoolId">
        <el-select v-model="queryParams.resPoolId" placeholder="请选择资源池" clearable>
          <el-option
            v-for="dict in dict.type.res_res_pool_data"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="资源池Pod" prop="resPodId">
        <el-select v-model="queryParams.resPodId" placeholder="请选择资源池Pod" clearable>
          <el-option
            v-for="dict in dict.type.res_k_pod_manager_data"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <template>
        <!-- <el-form-item label="资源组名称" prop="resGroupId">
          <el-select v-model="queryParams.resGroupId" placeholder="请选择资源组" clearable>
            <el-option
              v-for="dict in dict.type.res_res_group_data"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item> -->

        <el-form-item label="业务部门" prop="deptName">
          <CommonDepartmentSelect
            v-model="queryParams.deptName"
            return-name
            @change="() => (queryParams.sysName = '')"
          />
        </el-form-item>

        <el-form-item label="业务系统" prop="sysName">
          <CommonSystemSelect v-model="queryParams.sysName" return-name :params="{ deptName: queryParams.deptName }" />
        </el-form-item>

        <el-form-item label="运行状态" prop="runningStatus">
          <EleSelectDict v-model="queryParams.runningStatus" :options="dict.type.res_host_running_status" @change="handleQuery" />
        </el-form-item>

        <el-form-item label="手动维护的集群名称" prop="manualClusterName">
          <el-input
            v-model="queryParams.manualClusterName"
            placeholder="请输入手动维护的集群名称"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>

        <el-form-item label="操作系统" prop="operatingSystem">
          <el-input
            v-model="queryParams.operatingSystem"
            placeholder="请输入操作系统"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>

        <el-form-item label="管理网IP" prop="managementIp">
          <el-input
            v-model="queryParams.managementIp"
            placeholder="请输入管理网IP"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="存储网IP" prop="storageIp">
          <el-input
            v-model="queryParams.storageIp"
            placeholder="请输入存储网IP"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="业务网IP" prop="serviceIp">
          <el-input
            v-model="queryParams.serviceIp"
            placeholder="请输入业务网IP"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="承载网IP" prop="bearerIp">
          <el-input
            v-model="queryParams.bearerIp"
            placeholder="请输入承载网IP"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="数据类型" prop="insertType">
          <el-select v-model="queryParams.insertType" placeholder="请选择数据类型" clearable>
            <el-option
              v-for="dict in dict.type.insert_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <!-- <el-form-item label="是否交维" prop="transfer">
          <el-input
            v-model="queryParams.transfer"
            placeholder="请输入是否交维"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="创建人" prop="createUser">
          <el-input
            v-model="queryParams.createUser"
            placeholder="请输入创建人"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="更新用户" prop="updateUser">
          <el-input
            v-model="queryParams.updateUser"
            placeholder="请输入更新用户"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item> -->
        <el-form-item label="入库日期" prop="saveTime">
          <el-date-picker
            v-model="queryParams.saveTime"
            clearable
            size="small"
            type="date"
            value-format="yyyy-MM-dd"
            format="yyyy-MM-dd"
            placeholder="选择入库日期"
          />
        </el-form-item>
      </template>

      <el-form-item class="query-handle">
        <el-button type="primary" plain icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh-left" @click="resetQuery">重置</el-button>
        <EleSheetSearchMore :parent-ref="()=> $refs.queryForm" />
      </el-form-item>
    </el-form>

    <div v-if="!selectMode" class="sa-title space-x-2">
      <sa-toolbar :show-search.sync="showSearch" @queryTable="getList" />
      <el-button v-if="$checkPermi(['res:host:maintenance'])" type="danger" @click="handleResource('import')">
        <svg-icon class-name="" icon-class="hostDefend" />
        资产维护
      </el-button>

      <el-button v-if="$checkPermi(['res:host:export'])" type="primary" @click="handleResource('export')"><svg-icon icon-class="excelEp" />导出</el-button>

      <el-button
        v-if="$checkPermi(['res:host:refresh:cache'])"
        type="danger"
        :icon="cacheLoading ? '' : 'el-icon-refresh'"
        :loading="cacheLoading"
        @click="onClickCache"
      >刷新缓存</el-button>

      <!-- <el-dropdown>
        <el-button icon="el-icon-download">
          导出
          <i class="el-icon-arrow-down el-icon--right" />
        </el-button>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item
            v-hasPermi="['res:host:export']"
            @click.native="handleExport"
          >数据列表</el-dropdown-item>
          <el-dropdown-item
            v-hasPermi="['res:host:exportBill']"
            @click.native="onExportZipFile"
          >导出提单数据ZIP</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>

      <el-dropdown>
        <el-button icon="el-icon-download">
          导入
          <i class="el-icon-arrow-down el-icon--right" />
        </el-button>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item
            v-hasPermi="['res:host:import']"
            @click.native="handleImport"
          >数据列表</el-dropdown-item>
          <el-dropdown-item
            v-hasPermi="['res:host:importExtend']"
            @click.native="handleImportExtend"
          >资源主机扩展信息</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown> -->

      <el-button v-if="$checkPermi(['res:host:syn'])" type="warning" icon="el-icon-refresh" @click="onSynResData">同步</el-button>
    </div>

    <el-table
      ref="tableRef"
      v-loading="loading"
      class="sa-table"
      :data="hostList"
      row-key="id"
      @selection-change="onSelectionChange"
    >
      <el-table-column v-if="selectMode === 'multiple'" type="selection" width="55" align="center" reserve-selection />
      <el-table-column label="资源主机名称" align="left" prop="name" min-width="120" empty-text="未知" show-overflow-tooltip>
        <template #default="{ row }">
          <span class="text-primary-500 hover:underline cursor-pointer" @click="handleInfo(row)">{{
            row.name
          }}</span>
        </template>
      </el-table-column>
      <el-table-column label="资源池名称" align="left" prop="resPoolName" min-width="120" />
      <el-table-column label="资源池Pod名称" align="center" prop="resPodName" min-width="120" :formatter="unknownFormat" show-overflow-tooltip />
      <!-- <el-table-column label="资源组名称" align="center" prop="resGroupName" min-width="120" :formatter="unknownFormat" show-overflow-tooltip /> -->
      <el-table-column label="安全域" align="center" prop="securityDomain" min-width="80" :formatter="unknownFormat" show-overflow-tooltip />
      <el-table-column label="业务部门名称(业务维护部门)" align="center" prop="deptName" min-width="120" show-overflow-tooltip />
      <el-table-column label="业务系统名称" align="center" prop="sysName" min-width="120" show-overflow-tooltip />
      <el-table-column label="业务网IP" align="center" prop="serviceIp" min-width="120" show-overflow-tooltip :formatter="unknownFormat" />
      <el-table-column label="管理网IP" align="center" prop="managementIp" min-width="120" show-overflow-tooltip :formatter="unknownFormat" />
      <el-table-column label="存储网IP" align="center" prop="storageIp" min-width="120" show-overflow-tooltip :formatter="unknownFormat" />
      <el-table-column label="承载网IP" align="center" prop="bearerIp" min-width="120" show-overflow-tooltip :formatter="unknownFormat" />
      <el-table-column label="CPU个数" align="center" prop="cpuCores" min-width="100" show-overflow-tooltip :formatter="unknownFormat" />
      <el-table-column label="内存(GB)" align="center" prop="memory" min-width="100" show-overflow-tooltip :formatter="unknownFormat" />
      <!-- <el-table-column label="磁盘(GB)" align="center" prop="disk" min-width="100" show-overflow-tooltip :formatter="unknownFormat" /> -->
      <!-- <el-table-column label="GPU个数" align="center" prop="gpuNum" min-width="100" show-overflow-tooltip :formatter="unknownFormat" /> -->
      <!-- <el-table-column label="GPU型号" align="center" prop="gpuModel" min-width="100" show-overflow-tooltip :formatter="unknownFormat" /> -->
      <!-- <el-table-column label="规格" align="center" prop="extraInfo.specs" min-width="150" show-overflow-tooltip :formatter="unknownFormat" /> -->
      <el-table-column label="操作系统" align="center" prop="operatingSystem" min-width="100" show-overflow-tooltip :formatter="unknownFormat" />
      <el-table-column label="VPC名称" align="center" prop="vpcName" min-width="120" show-overflow-tooltip :formatter="unknownFormat" />
      <el-table-column label="手动维护的集群名称" align="center" prop="manualClusterName" min-width="150" show-overflow-tooltip :formatter="unknownFormat" />
      <!-- <el-table-column label="组件" align="center" prop="assembly" min-width="120" :formatter="unknownFormat" /> -->
      <!-- <el-table-column label="产品" align="center" prop="product" min-width="120" :formatter="unknownFormat" /> -->
      <!-- <el-table-column label="镜像操作类型" align="center" prop="extraInfo.imgOperType" min-width="130" show-overflow-tooltip :formatter="unknownFormat" /> -->
      <!-- <el-table-column label="镜像操作版本" align="center" prop="extraInfo.imgOperVersion" min-width="130" show-overflow-tooltip :formatter="unknownFormat" /> -->
      <!-- <el-table-column label="镜像发行版本信息" align="center" prop="extraInfo.imgReleaseInfo" min-width="150" show-overflow-tooltip :formatter="unknownFormat" /> -->
      <!-- <el-table-column label="镜像内核版本信息" align="center" prop="extraInfo.imgKernelInfo" min-width="150" show-overflow-tooltip :formatter="unknownFormat" /> -->
      <!-- <el-table-column label="镜像发行版本大版本号" align="center" prop="extraInfo.imgReleaseMajorVersion" min-width="170" show-overflow-tooltip :formatter="unknownFormat" /> -->
      <!-- <el-table-column label="镜像发行版本小版本号" align="center" prop="extraInfo.imgReleaseMinorVersion" min-width="170" show-overflow-tooltip :formatter="unknownFormat" /> -->
      <el-table-column label="一级部门" align="center" prop="superDeptName" min-width="120" show-overflow-tooltip :formatter="unknownFormat" />
      <el-table-column label="二级部门(资产所属部门)" align="center" prop="whichDeptName" min-width="170" show-overflow-tooltip :formatter="unknownFormat" />
      <el-table-column label="业务维护分组名称" align="center" prop="serviceDefend" min-width="150" show-overflow-tooltip :formatter="unknownFormat" />
      <el-table-column label="资产维护部门" align="center" prop="defendDeptName" min-width="120" show-overflow-tooltip :formatter="unknownFormat" />
      <el-table-column label="OS维护分组名称" align="center" prop="osDefend" min-width="150" show-overflow-tooltip :formatter="unknownFormat" />
      <el-table-column label="是否为定级备案资产" align="center" prop="gradedAssetFlag" min-width="150" show-overflow-tooltip :formatter="unknownFormat" />
      <el-table-column label="业务类型" align="center" prop="serviceType" :formatter="unknownFormat" show-overflow-tooltip />
      <el-table-column v-slot="{ row }" label="数据类型" align="center" prop="insertType" min-width="120">
        <DictTagV2 :value="row.insertType" dict-type="insert_type" remote />
      </el-table-column>
      <el-table-column label="归类" align="center" prop="extraInfo.classify" min-width="120" :formatter="unknownFormat" show-overflow-tooltip />
      <!-- <el-table-column label="缓存状态" align="center" prop="redisCache" width="120" /> -->
      <el-table-column label="上线时间" align="center" prop="onlineTime" width="180" />
      <el-table-column label="下线时间" align="center" prop="offlineTime" width="180" />
      <el-table-column v-slot="{ row }" label="运行状态" align="center" prop="offlineTime">
        <DictTagV2 :value="row.runningStatus" dict-type="res_host_running_status" remote />
      </el-table-column>
      <el-table-column label="所属租户" align="center" prop="tenName" width="150" />
      <el-table-column label="入库日期" align="center" prop="saveTime" width="180" />
      <el-table-column v-slot="{ row }" label="数据库资产数量" align="center" prop="databaseAssetCount" width="150">
        <el-link type="primary" @click="$router.push({ path:'/res/pj/assets/databaseAsset', query: { hostIp: row.serviceIp } })">
          {{ row.databaseAssetCount }}
        </el-link>
      </el-table-column>
      <el-table-column v-slot="{ row }" label="中间件资产数量" align="center" prop="middlewareAssetCount" width="150">
        <el-link type="primary" @click="$router.push({ path: '/res/pj/assets/middlewareAsset', query: { hostIp: row.serviceIp } })">
          {{ row.middlewareAssetCount }}
        </el-link>
      </el-table-column>
      <el-table-column
        v-if="!selectMode"
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        width="200"
        fixed="right"
      >
        <template slot-scope="scope">
          <el-button
            v-hasPermi="['res:host:edit']"
            type="text"
            @click="handleEdit(scope.row)"
          >编辑</el-button>
          <el-button
            v-hasPermi="['res:host:remove']"
            type="text"
            @click="handleDelete(scope.row)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <div class="sa-footer sa-row-center">
      <sa-pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </div>

    <!-- 详情 -->
    <view-detail ref="detailRef" :detail-data="detailData" />

    <UploadDialog ref="uploadDialogRef" @success="getList" />
    <UploadExtendDialog ref="uploadExtendDialogRef" @success="getList" />

    <InfoDialog ref="infoDialogRef" v-bind="{ detailData }" />

    <FormDialog ref="formDialogRef" @success="getList" />

    <ResourceDialog ref="resourceDialogRef" />
  </div>
</template>

<script>
import { addHost, delHost, exportHost, getHost, listHost, updateHost } from '@/api/res/host'
import { getToken } from '@/utils/auth'
import ViewDetail from '../detail.vue'
import request from '@/utils/request'
import UploadDialog from './components/UploadDialog/index.vue'
import UploadExtendDialog from './components/UploadExtendDialog/index.vue'
import InfoDialog from './components/InfoDialog/index.vue'
import FormDialog from './components/FormDialog/index.vue'
import ResourceDialog from './components/ResourceDialog/index.vue'

export default {
  name: 'Host',
  components: {
    ViewDetail,
    UploadDialog,
    UploadExtendDialog,
    InfoDialog,
    FormDialog,
    ResourceDialog
  },
  props: {
    selectMode: {
      type: [Boolean, String],
      default: false
    },
    selectKeys: {
      type: [Array, String],
      default: ''
    },
    defaultSearch: {
      type: Object,
      default: () => ({})
    }
  },
  dicts: [
    'res_res_group_data',
    'res_res_pool_data',
    'res_k_pod_manager_data',
    'res_host_running_status',
    'insert_type'
  ],
  data() {
    return {
      // 按钮loading
      buttonLoading: false,
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 磐基资源主机表格数据
      hostList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        outId: undefined,

        name: undefined,

        resPoolId: undefined,

        resPoolName: undefined,

        resPodId: undefined,

        resPodName: undefined,

        resGroupId: undefined,

        resGroupName: undefined,

        deptId: undefined,

        deptName: undefined,

        sysId: undefined,

        sysName: undefined,

        clusterId: undefined,

        manualClusterName: undefined,

        operatingSystem: undefined,

        hostType: undefined,

        securityDomain: undefined,

        assembly: undefined,

        product: undefined,

        memory: undefined,

        blockStorage: undefined,

        disk: undefined,

        cpuCores: undefined,

        vpcName: undefined,

        is4a: undefined,

        vip: undefined,

        managementIp: undefined,

        storageIp: undefined,

        serviceIp: undefined,

        bearerIp: undefined,

        status: undefined,

        transfer: undefined,

        createUser: undefined,

        updateUser: undefined,

        saveTime: undefined,

        runningStatus: undefined
      },

      // 表单参数
      form: {},
      // 表单校验
      rules: {
        outId: [{ required: true, message: '外部id不能为空', trigger: 'blur' }]
      },

      detailData: {
        type: 'host',
        id: null,
        formLabel: [
          {
            label: '资源主机名称',
            field: 'name'
          },
          {
            label: '资源池名称',
            field: 'resPoolName'
          },
          {
            label: '资源池Pod名称',
            field: 'resPodName'
          },
          {
            label: '资源组名称',
            field: 'resGroupName'
          },
          {
            label: '业务部门名称',
            field: 'deptName'
          },
          {
            label: '业务系统名称',
            field: 'sysName'
          },
          {
            label: '手动维护的集群名称',
            field: 'manualClusterName'
          },
          {
            label: '操作系统',
            field: 'operatingSystem'
          },
          {
            label: '主机类型',
            field: 'hostType'
          },
          {
            label: '操作系统',
            field: 'securityDomain'
          },
          {
            label: '组件',
            field: 'assembly'
          },
          {
            label: '产品',
            field: 'product'
          },
          {
            label: '内存',
            field: 'memory'
          },
          {
            label: '快存储',
            field: 'blockStorage'
          },
          {
            label: '磁盘',
            field: 'disk'
          },
          {
            label: 'cpu核数',
            field: 'cpuCores'
          },
          {
            label: 'vpc名称',
            field: 'vpcName'
          },
          {
            label: '是否4A接入',
            field: 'is4a'
          },
          {
            label: 'vip',
            field: 'vip'
          },
          {
            label: '管理网IP',
            field: 'managementIp'
          },
          {
            label: '存储网IP',
            field: 'storageIp'
          },
          {
            label: '业务网IP',
            field: 'serviceIp'
          },
          {
            label: '承载网IP',
            field: 'bearerIp'
          },
          {
            label: '状态',
            field: 'status'
          },
          {
            label: '是否交维',
            field: 'transfer'
          },
          {
            label: '数据类型',
            field: 'insertType',
            type: 'insertType'
          },
          {
            label: '入库日期',
            field: 'saveTime',
            type: 'time'
          }
        ]
      },
      systemList: [],

      cacheLoading: false
    }
  },
  created() {
    Object.assign(this.queryParams, { ...this.defaultSearch })

    this.getList()
    this.getSysList()
  },
  methods: {
    handleResource(type) {
      this.$refs.resourceDialogRef.open({
        type,
        params: {
          ...this.queryParams
        },
        success: () => {
          this.getList()
        }
      })
    },
    async toggleSelection() {
      if (!this.$props.selectKeys) {
        return false
      }

      const selectKeys = typeof this.$props.selectKeys === 'string' ? this.selectKeys.split(',') : this.$props.selectKeys

      await this.$nextTick()

      if (this.$refs.tableRef) {
        this.hostList.forEach(row => {
          this.$refs.tableRef.toggleRowSelection(row, selectKeys.includes(row.serviceIp))
        })
      }
    },
    handleInfo(row) {
      this.$refs.infoDialogRef.open({ params: row })
    },
    handleImport() {
      this.$refs.uploadDialogRef.open()
    },
    handleImportExtend() {
      this.$refs.uploadExtendDialogRef.open()
    },
    /** 查询磐基资源主机列表 */
    getList() {
      this.loading = true

      listHost(this.queryParams).then((response) => {
        this.hostList = response.rows
        this.total = response.total
        this.loading = false
        this.toggleSelection()
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        id: undefined,

        outId: undefined,

        name: undefined,

        resPoolId: undefined,

        resPoolName: undefined,

        resPodId: undefined,

        resPodName: undefined,

        resGroupId: undefined,

        resGroupName: undefined,

        deptId: undefined,

        deptName: undefined,

        sysId: undefined,

        sysName: undefined,

        clusterId: undefined,

        manualClusterName: undefined,

        operatingSystem: undefined,

        hostType: undefined,

        securityDomain: undefined,

        assembly: undefined,

        product: undefined,

        memory: undefined,

        blockStorage: undefined,

        disk: undefined,

        cpuCores: undefined,

        vpcName: undefined,

        is4a: undefined,

        vip: undefined,

        managementIp: undefined,

        storageIp: undefined,

        serviceIp: undefined,

        bearerIp: undefined,

        status: '0',

        transfer: undefined,

        createUser: undefined,

        createTime: undefined,

        updateUser: undefined,

        updateTime: undefined,

        saveTime: undefined,

        runningStatus: undefined
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 多选框选中数据
    onSelectionChange(selection) {
      this.ids = selection.map((item) => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
      this.selection = selection
      this.$emit('selection-change', selection)
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids
      this.$confirm('是否确认删除当前所选数据项？', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.loading = true
          return delHost(ids)
        })
        .then(() => {
          this.loading = false
          this.getList()
          this.msgSuccess('删除成功')
        })
        .catch(() => {})
    },
    /** 导出按钮操作 */
    handleExport() {
      if (this.total > 200000) {
        this.$modal.msgWarning('抱歉，导出数据量过大，请选择筛选条件后再进行导出')
        return false
      }

      const submitAuery = JSON.parse(JSON.stringify(this.queryParams))
      delete submitAuery.pageNum
      delete submitAuery.pageSize

      this.download(
        'res/host/export',
        {
          ...submitAuery
        },
        `导出${this.$route.meta.title}_${new Date().getTime()}.xlsx`
      )
      // const queryParams = this.queryParams;
      // this.$confirm('是否确认导出所有磐基资源主机数据项?', '警告', {
      //   confirmButtonText: '确定',
      //   cancelButtonText: '取消',
      //   type: 'warning',
      // })
      //   .then(() => {
      //     this.exportLoading = true;
      //     return exportHost(queryParams);
      //   })
      //   .then((response) => {
      //     this.download(response.msg);
      //     this.exportLoading = false;
      //   })
      //   .catch(() => {});
    },
    onDetail(row) {
      this.detailData.id = row.id
      this.$refs.detailRef.show()
    },
    handleEdit(row) {
      this.$refs.formDialogRef.open({ params: row })
    },
    unknownFormat(row, column, cellValue) {
      return cellValue == null || cellValue == '' ? '未知' : cellValue
    },
    onSynResData() {
      this.loading = true
      request({
        url: `/res/system/synResData/104`,
        method: 'get',
        timeout: 3000000
      }).then((response) => {
        if (response.code == 200) {
          this.$modal.msgSuccess(response.msg)
          this.getList()
        }
        this.loading = false
      })
    },
    onExportZipFile() {
      if (!this.queryParams.saveTime) {
        this.$modal.msgWarning('请选择入库日期')
        return false
      }
      this.download(
        '/res/host/exportZipFile',
        {
          saveTime: this.queryParams.saveTime
        },
        `导出ZIP_${new Date().getTime()}.zip`
      )
    },
    /** 格式化当前日期为指定格式的字符串*/
    formatTodayDate() {
      const today = new Date()
      const year = today.getFullYear()
      const month = `0${today.getMonth() + 1}`.slice(-2) // 月份是从0开始的，所以需要+1
      const day = `0${today.getDate()}`.slice(-2) // 获取日期
      return `${year}-${month}-${day}`
    },
    getSysList() {
      this.loading = true
      request({
        url: '/res/system/list',
        method: 'get'
      }).then((response) => {
        this.systemList = response.data
        this.loading = false
      })
    },
    async onClickCache() {
      this.cacheLoading = true

      try {
        const res = await request({
          url: '/res/host/reloadResHostCache',
          method: 'get'
        })

        if (res.code === 200) {
          this.$message.success(res.msg)
        }
      } finally {
        this.cacheLoading = false
      }
    }
  }
}
</script>
