<template>
  <div class="page-main">
    <el-form
      v-show="showSearch"
      ref="queryForm"
      class="sa-query"
      :model="queryParams"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="分区名称" prop="namespaceName">
        <el-input
          v-model="queryParams.namespaceName"
          placeholder="请输入分区名称"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="KEM名称" prop="kemName">
        <el-input
          v-model="queryParams.kemName"
          placeholder="请输入KEM名称"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <!-- <el-form-item label="业务模块名称" prop="kbizModuleName">
        <el-input
          v-model="queryParams.kbizModuleName"
          placeholder="请输入业务模块名称"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <!-- <el-form-item label="租户名称" prop="tenantName">
        <el-input
          v-model="queryParams.tenantName"
          placeholder="请输入租户名称"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item label="集群名称" prop="clusterName">
        <el-input
          v-model="queryParams.clusterName"
          placeholder="请输入集群名称"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item class="query-handle">
        <el-button type="primary" plain icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh-left" @click="resetQuery">重置</el-button>
        <EleSheetSearchMore :parent-ref="()=> $refs.queryForm" />
      </el-form-item>
    </el-form>

    <div class="sa-title">
      <sa-toolbar :show-search.sync="showSearch" @queryTable="getList" />
      <el-button
        v-hasPermi="['res:namespace:export']"
        type="primary"
        icon="el-icon-download"
        :loading="exportLoading"
        @click="handleExport"
      >导出</el-button>
      <el-button v-if="$checkPermi(['res:namespace:synchronous'])" type="warning" icon="el-icon-refresh" @click="onSynResData">同步</el-button>
    </div>

    <el-table v-loading="loading" class="sa-table" :data="namespaceList">
      <el-table-column label="数据类型" align="center">
        <template slot-scope="scope">
          {{ scope.row.dataType == 1 ? 'kem' : '磐基 2.0' }}
        </template>
      </el-table-column>
      <el-table-column label="分区名称" align="center" prop="namespaceName" width="180" />
      <!-- <el-table-column label="CPU配额" align="center" prop="cpuRequest" />
      <el-table-column label="CPU限额" align="center" prop="cpuLimit" />
      <el-table-column label="内存配额" align="center" prop="memoryRequest" />
      <el-table-column label="内存限额" align="center" prop="memoryLimit" />
      <el-table-column label="存储大小" align="center" prop="storage" /> -->
      <el-table-column label="KEM名称" align="center" prop="kemName" />
      <!-- <el-table-column label="业务模块名称" align="center" prop="kbizModuleName" width="180" /> -->
      <!-- <el-table-column label="租户名称" align="center" prop="tenantName" /> -->
      <el-table-column label="集群名称" align="center" prop="clusterName" />
      <el-table-column v-for="(item, index) in columnList" :key="index" :label="item.label" align="center" width="120">
        <template slot-scope="scope">
          <el-button type="text" @click="onColumn('namespace', scope.row, item.type)">
            {{ scope.row[item.field] }}
          </el-button>
        </template>
      </el-table-column>
      <el-table-column label="入库时间" align="center" prop="saveTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.saveTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" align="center">
        <template slot-scope="scope">
          <el-button type="text" @click="onDetail(scope.row)">详情</el-button>
        </template>
      </el-table-column>
    </el-table>

    <div class="sa-footer sa-row-center">
      <sa-pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </div>

    <!-- 详情 -->
    <view-detail ref="detailRef" :detail-data="detailData" />
  </div>
</template>

<script>
import { listNamespace } from '@/api/res/namespace'
import ViewDetail from '../detail.vue'
import request from '@/utils/request'
import { getColumnList, onColumn } from '../data'

export default {
  // name: 'Namespace',
  components: {
    ViewDetail
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 磐基分区表格数据
      namespaceList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        namespaceName: undefined,
        kemName: undefined,
        kbizModuleName: undefined,
        tenantName: undefined,
        clusterName: undefined
      },

      detailData: {
        type: 'namespace',
        id: null,
        formLabel: [
          {
            label: '分区名称',
            field: 'namespaceName'
          },
          {
            label: 'CPU配额',
            field: 'cpuRequest'
          },
          {
            label: 'CPU限额',
            field: 'cpuLimit'
          },
          {
            label: '内存配额',
            field: 'memoryRequest'
          },
          {
            label: '内存限额',
            field: 'memoryLimit'
          },
          {
            label: '存储大小',
            field: 'storage'
          },
          {
            label: 'KEM名称',
            field: 'kemName'
          },
          {
            label: '业务模块名称',
            field: 'kbizModuleName'
          },
          {
            label: '租户名称',
            field: 'tenantName'
          },
          {
            label: '集群名称',
            field: 'clusterName'
          },
          {
            label: '更新用户',
            field: 'updateUser'
          },
          {
            label: '入库时间',
            field: 'saveTime',
            type: 'time'
          }
        ]
      },

      columnList: []
    }
  },
  created() {
    this.queryParams = { ...this.queryParams, ...this.$route.query }
    this.getList()
  },
  methods: {
    /** 查询磐基分区列表 */
    getList() {
      this.loading = true
      listNamespace(this.queryParams).then((response) => {
        this.namespaceList = response.rows
        this.total = response.total

        if (response.rows.length > 0) {
          this.columnList = this.getColumnList(response.rows)
        }

        this.loading = false
      })
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 导出按钮操作 */
    handleExport() {
      if (this.total > 200000) {
        this.$modal.msgWarning('抱歉，导出数据量过大，请选择筛选条件后再进行导出')
        return false
      }

      const submitAuery = JSON.parse(JSON.stringify(this.queryParams))
      delete submitAuery.pageNum
      delete submitAuery.pageSize

      this.download(
        'res/namespace/export',
        {
          ...submitAuery
        },
        `导出${this.$route.meta.title}_${new Date().getTime()}.xlsx`
      )
    },
    onDetail(row) {
      this.detailData.id = row.id
      this.$refs.detailRef.show()
    },
    onSynResData() {
      this.loading = true
      request({
        url: `/res/system/synResData/205`,
        method: 'get',
        timeout: 3000000
      }).then((response) => {
        if (response.code == 200) {
          this.$modal.msgSuccess(response.msg)
          this.getList()
        }
        this.loading = false
      })
    },
    getColumnList,
    onColumn
  }
}
</script>
