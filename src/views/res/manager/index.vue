<template>
  <div class="page-main">
    <el-form
      v-show="showSearch"
      ref="queryForm"
      class="sa-query"
      :model="queryParams"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="POD名称" prop="ciname">
        <el-input
          v-model="queryParams.ciname"
          placeholder="请输入POD名称"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <!--      <el-form-item label="集群名称" prop="clusterName">-->
      <!--        <el-input-->
      <!--          v-model="queryParams.clusterName"-->
      <!--          placeholder="请输入集群名称"-->
      <!--          clearable-->
      <!--          size="small"-->
      <!--          @keyup.enter.native="handleQuery"-->
      <!--        />-->
      <!--      </el-form-item>-->
      <!--      <el-form-item label="服务名称" prop="serviceName">-->
      <!--        <el-input-->
      <!--          v-model="queryParams.serviceName"-->
      <!--          placeholder="请输入服务名称"-->
      <!--          clearable-->
      <!--          size="small"-->
      <!--          @keyup.enter.native="handleQuery"-->
      <!--        />-->
      <!--      </el-form-item>-->
      <!--      <el-form-item label="KEM名称" prop="kemName">-->
      <!--        <el-input-->
      <!--          v-model="queryParams.kemName"-->
      <!--          placeholder="请输入KEM名称"-->
      <!--          clearable-->
      <!--          size="small"-->
      <!--          @keyup.enter.native="handleQuery"-->
      <!--        />-->
      <!--      </el-form-item>-->
      <!--      <el-form-item label="PODIP" prop="podIp">-->
      <!--        <el-input-->
      <!--          v-model="queryParams.podIp"-->
      <!--          placeholder="请输入PODIP"-->
      <!--          clearable-->
      <!--          size="small"-->
      <!--          @keyup.enter.native="handleQuery"-->
      <!--        />-->
      <!--      </el-form-item>-->
      <el-form-item label="主机IP" prop="nodeIp">
        <el-input
          v-model="queryParams.nodeIp"
          placeholder="请输入主机IP"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <!--      <el-form-item label="POD端口" prop="podPort">-->
      <!--        <el-input-->
      <!--          v-model="queryParams.podPort"-->
      <!--          placeholder="请输入POD端口"-->
      <!--          clearable-->
      <!--          size="small"-->
      <!--          @keyup.enter.native="handleQuery"-->
      <!--        />-->
      <!--      </el-form-item>-->
      <el-form-item class="query-handle">
        <el-button type="primary" plain icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh-left" @click="resetQuery">重置</el-button>
        <EleSheetSearchMore :parent-ref="()=> $refs.queryForm" />
      </el-form-item>
    </el-form>

    <div class="sa-title">
      <sa-toolbar :show-search.sync="showSearch" @queryTable="getList" />
      <el-button
        v-hasPermi="['res:manager:export']"
        type="primary"
        icon="el-icon-download"
        :loading="exportLoading"
        @click="handleExport"
      >导出</el-button>
      <el-button v-if="$checkPermi(['res:manager:synchronous'])" type="warning" icon="el-icon-refresh" @click="onSynResData">同步</el-button>
    </div>

    <el-table v-loading="loading" class="sa-table" :data="managerList">
      <el-table-column label="POD名称" align="center" prop="ciname" width="220" />
      <el-table-column label="集群名称" align="center" prop="clusterName" />
      <el-table-column label="分区名称" align="center" prop="namespaceName" />
      <el-table-column label="服务名称" align="center" prop="serviceName" />
      <el-table-column label="KEM名称" align="center" prop="kemName" />
      <el-table-column label="PODIP" align="center" prop="podIp" width="180" />
      <el-table-column label="主机IP" align="center" prop="nodeIp" width="180" />
      <el-table-column label="POD创建时间" align="center" prop="creationTimestamp" width="180" />
      <el-table-column label="POD端口" align="center" prop="podPort" />
      <el-table-column label="容器数" align="center" prop="containerNum" />
      <el-table-column label="配额" align="center" prop="quota" />
      <el-table-column label="运行状态" align="center" prop="runStatus" />
      <el-table-column label="入库时间" align="center" prop="saveTime" width="180" />
      <!--      <el-table-column fixed="right" label="操作" align="center">-->
      <!--        <template slot-scope="scope">-->
      <!--          <el-button type="text" @click="onDetail(scope.row)">详情</el-button>-->
      <!--        </template>-->
      <!--      </el-table-column>-->
    </el-table>

    <div class="sa-footer sa-row-center">
      <sa-pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </div>

    <!-- 详情 -->
    <view-detail ref="detailRef" :detail-data="detailData" />
  </div>
</template>

<script>
import { listManager } from '@/api/res/manager'
import ViewDetail from '../detail.vue'
import request from '@/utils/request'

export default {
  // name: 'Manager',
  components: {
    ViewDetail
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 磐基Pod管理表格数据
      managerList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        ciname: undefined,
        clusterName: undefined,
        serviceName: undefined,
        kemName: undefined,
        podIp: undefined,
        nodeIp: undefined,
        podPort: undefined
      },

      detailData: {
        type: 'manager',
        id: null,
        formLabel: [
          {
            label: 'POD名称',
            field: 'ciname'
          },
          {
            label: '集群名称',
            field: 'clusterName'
          },
          {
            label: '分区名称',
            field: 'namespaceName'
          },
          {
            label: '服务名称',
            field: 'serviceName'
          },
          {
            label: 'KEM名称',
            field: 'kemName'
          },
          {
            label: 'PODIP',
            field: 'podIp'
          },
          {
            label: '主机IP',
            field: 'nodeIp'
          },
          {
            label: 'POD创建时间',
            field: 'creationTimestamp',
            type: 'time'
          },
          {
            label: 'POD端口',
            field: 'podPort'
          },
          {
            label: '容器数',
            field: 'containerNum'
          },
          {
            label: '配额',
            field: 'quota'
          },
          {
            label: '运行状态',
            field: 'runStatus'
          },
          {
            label: '入库时间',
            field: 'saveTime',
            type: 'time'
          }
        ]
      }
    }
  },
  created() {
    this.queryParams = { ...this.queryParams, ...this.$route.query }
    this.getList()
  },
  methods: {
    /** 查询磐基Pod管理列表 */
    getList() {
      this.loading = true
      listManager(this.queryParams).then((response) => {
        this.managerList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 导出按钮操作 */
    handleExport() {
      if (this.total > 200000) {
        this.$modal.msgWarning('抱歉，导出数据量过大，请选择筛选条件后再进行导出')
        return false
      }

      const submitAuery = JSON.parse(JSON.stringify(this.queryParams))
      delete submitAuery.pageNum
      delete submitAuery.pageSize

      this.download(
        'res/k8sPod/export',
        {
          ...submitAuery
        },
        `导出${this.$route.meta.title}_${new Date().getTime()}.xlsx`
      )
    },
    onDetail(row) {
      this.detailData.id = row.id
      this.$refs.detailRef.show()
    },
    onSynResData() {
      this.loading = true
      request({
        url: `/res/system/synResData/210`,
        method: 'get',
        timeout: 3000000
      }).then((response) => {
        if (response.code == 200) {
          this.$modal.msgSuccess(response.msg)
          this.getList()
        }
        this.loading = false
      })
    }
  }
}
</script>
