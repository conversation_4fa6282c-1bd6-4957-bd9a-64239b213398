<template>
  <div class="page-main">
    <el-form
      v-show="showSearch"
      ref="queryForm"
      class="sa-query"
      :model="queryParams"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="服务名称" prop="serviceName">
        <el-input
          v-model="queryParams.serviceName"
          placeholder="请输入服务名称"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <!-- <el-form-item label="应用编码" prop="applicationSourceCode">
        <el-input
          v-model="queryParams.applicationSourceCode"
          placeholder="请输入应用编码"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="磐基2.0平台编码" prop="platformCode">
        <el-input
          v-model="queryParams.platformCode"
          placeholder="请输入磐基2.0平台编码"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item class="daterange" label="时间区间" prop="daterange">
        <el-date-picker
          v-model="daterange"
          type="daterange"
          value-format="yyyy-MM-dd"
          format="yyyy-MM-dd"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="onChangeDaterange"
        />
      </el-form-item>
      <el-form-item class="query-handle">
        <el-button type="primary" plain icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh-left" @click="resetQuery">重置</el-button>
        <EleSheetSearchMore :parent-ref="()=> $refs.queryForm" />
      </el-form-item>
    </el-form>

    <div class="sa-title">
      <sa-toolbar :show-search.sync="showSearch" @queryTable="getList" />
      <el-button
        v-hasPermi="['res:service:export']"
        type="primary"
        icon="el-icon-download"
        :loading="exportLoading"
        @click="handleExport"
      >导出</el-button>
      <el-button v-if="$checkPermi(['res:service:synchronous'])" type="warning" icon="el-icon-refresh" @click="onSynResData">同步</el-button>
    </div>

    <el-table v-loading="loading" class="sa-table" :data="serviceList">
      <el-table-column label="数据类型" align="center">
        <template slot-scope="scope">
          {{ scope.row.dataType == 1 ? 'kem' : '磐基 2.0' }}
        </template>
      </el-table-column>
      <el-table-column label="服务名称" align="center" prop="serviceName" width="180" />
      <el-table-column label="服务类型" align="center" prop="serviceType" />
      <el-table-column label="KEM名称" align="center" prop="kemName" />
      <el-table-column label="租户名称" align="center" prop="tenantName" />
      <el-table-column label="集群名称" align="center" prop="clusterName" />
      <el-table-column label="分区名称" align="center" prop="namespaceName" />
      <!-- <el-table-column label="组件类型" align="center" prop="componentTypeId" /> -->
      <!-- <el-table-column label="应用编码" align="center" prop="applicationSourceCode" /> -->
      <el-table-column label="状态" align="center">
        <template slot-scope="scope">
          {{ scope.row.status == 1 ? '在用' : '注销' }}
        </template>
      </el-table-column>
      <!-- <el-table-column label="磐基2.0平台编码" align="center" prop="platformCode" width="180" /> -->
      <el-table-column label="入库时间" align="center" prop="saveTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.saveTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" align="center">
        <template slot-scope="scope">
          <el-button type="text" @click="onDetail(scope.row)">详情</el-button>
        </template>
      </el-table-column>
    </el-table>

    <div class="sa-footer sa-row-center">
      <sa-pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </div>

    <!-- 详情 -->
    <view-detail ref="detailRef" :detail-data="detailData" />
  </div>
</template>

<script>
import { listService } from '@/api/res/service'
import ViewDetail from '../detail.vue'
import request from '@/utils/request'

export default {
  // name: 'Service',
  components: {
    ViewDetail
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 磐基服务管理表格数据
      serviceList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        serviceName: undefined,
        applicationSourceCode: undefined,
        platformCode: undefined,
        saveTime: undefined
      },
      daterange: [],

      detailData: {
        type: 'service',
        id: null,
        formLabel: [
          {
            label: '服务名称',
            field: 'serviceName'
          },
          {
            label: '服务类型',
            field: 'serviceType'
          },
          {
            label: 'KEM名称',
            field: 'kemName'
          },
          {
            label: '租户名称',
            field: 'tenantName'
          },
          {
            label: '集群名称',
            field: 'clusterName'
          },
          {
            label: '分区名称',
            field: 'namespaceName'
          },
          {
            label: '添加用户',
            field: 'createUser'
          },
          {
            label: '更新用户',
            field: 'updateUser'
          },
          // {
          //   label: '组件类型',
          //   field: 'componentTypeId',
          // },
          {
            label: '应用编码',
            field: 'applicationSourceCode'
          },
          {
            label: '状态',
            field: 'status',
            type: 'status'
          },
          {
            label: '磐基2.0平台编码',
            field: 'platformCode'
          },
          {
            label: '入库时间',
            field: 'saveTime',
            type: 'time'
          }
        ]
      }
    }
  },
  created() {
    this.queryParams = { ...this.queryParams, ...this.$route.query }
    this.getList()
  },
  methods: {
    /** 查询磐基服务管理列表 */
    getList() {
      this.loading = true
      listService(this.queryParams).then((response) => {
        this.serviceList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.daterange = []
      this.onChangeDaterange()
      this.handleQuery()
    },
    /** 导出按钮操作 */
    handleExport() {
      if (this.total > 200000) {
        this.$modal.msgWarning('抱歉，导出数据量过大，请选择筛选条件后再进行导出')
        return false
      }

      const submitAuery = JSON.parse(JSON.stringify(this.queryParams))
      delete submitAuery.pageNum
      delete submitAuery.pageSize

      this.download(
        'res/service/export',
        {
          ...submitAuery
        },
        `导出${this.$route.meta.title}_${new Date().getTime()}.xlsx`
      )
    },
    onDetail(row) {
      this.detailData.id = row.id
      this.$refs.detailRef.show()
    },
    onChangeDaterange() {
      if (this.daterange && this.daterange.length > 0) {
        this.queryParams.startTime = this.daterange[0]
        this.queryParams.endTime = this.daterange[1]
      } else {
        delete this.queryParams.startTime
        delete this.queryParams.endTime
      }
    },
    onSynResData() {
      this.loading = true
      request({
        url: `/res/system/synResData/207`,
        method: 'get',
        timeout: 3000000
      }).then((response) => {
        if (response.code == 200) {
          this.$modal.msgSuccess(response.msg)
          this.getList()
        }
        this.loading = false
      })
    }
  }
}
</script>
