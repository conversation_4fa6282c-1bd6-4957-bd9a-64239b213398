<template>
  <div class="page-main">
    <el-form
      v-show="showSearch"
      ref="queryForm"
      class="sa-query"
      :model="queryParams"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="集群名称" prop="clusterName">
        <el-input
          v-model="queryParams.clusterName"
          placeholder="请输入集群名称"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="服务地址" prop="serverUrl">
        <el-input
          v-model="queryParams.serverUrl"
          placeholder="请输入服务地址"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="所属paas平台" prop="paasPlatform">
        <el-input
          v-model="queryParams.paasPlatform"
          placeholder="请输入所属paas平台"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="KEM名称" prop="kemName">
        <el-input
          v-model="queryParams.kemName"
          placeholder="请输入KEM名称"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="资源池" prop="resourcePool">
        <el-input
          v-model="queryParams.resourcePool"
          placeholder="请输入资源池"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="paas平台版本" prop="paasVersion">
        <el-input
          v-model="queryParams.paasVersion"
          placeholder="请输入paas平台版本"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="磐基2.0平台编码" prop="platformCode">
        <el-input
          v-model="queryParams.platformCode"
          placeholder="请输入磐基2.0平台编码"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item class="query-handle">
        <el-button type="primary" plain icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh-left" @click="resetQuery">重置</el-button>
        <EleSheetSearchMore :parent-ref="()=> $refs.queryForm" />
      </el-form-item>
    </el-form>

    <div class="sa-title">
      <sa-toolbar :show-search.sync="showSearch" @queryTable="getList" />
      <el-button
        v-hasPermi="['res:cluster:export']"
        type="primary"
        icon="el-icon-download"
        :loading="exportLoading"
        @click="handleExport"
      >导出</el-button>
      <el-button v-if="$checkPermi(['res:cluster:synchronous'])" type="warning" icon="el-icon-refresh" @click="onSynResData">同步</el-button>
    </div>

    <el-table v-loading="loading" class="sa-table" :data="clusterList">
      <el-table-column label="数据类型" align="center">
        <template slot-scope="scope">
          {{ scope.row.dataType == 1 ? 'kem' : '磐基 2.0' }}
        </template>
      </el-table-column>
      <el-table-column label="集群名称" align="center" prop="clusterName" />
      <el-table-column label="集群版本" align="center" prop="clusterVersion" />
      <el-table-column label="服务地址" align="center" width="120">
        <template slot-scope="scope">
          <sa-tooltip :content="scope.row.serverUrl" />
        </template>
      </el-table-column>
      <el-table-column label="节点数" align="center" prop="nodeCount" />
      <el-table-column v-for="(item, index) in columnList" :key="index" :label="item.label" align="center" width="120">
        <template slot-scope="scope">
          <el-button type="text" @click="onColumn('cluster', scope.row, item.type)">
            {{ scope.row[item.field] }}
          </el-button>
        </template>
      </el-table-column>
      <!-- <el-table-column label="CPU核数" align="center" prop="cpu" />
      <el-table-column label="内存大小" align="center" prop="memory" />
      <el-table-column label="存储大小" align="center" prop="storage" /> -->
      <el-table-column label="所属paas平台" align="center" prop="paasPlatform" width="120" />
      <!-- <el-table-column label="paas平台版本" align="center" prop="paasVersion" width="120" /> -->
      <el-table-column label="KEM名称" align="center" prop="kemName" />
      <!-- <el-table-column label="资源池" align="center" prop="resourcePool" />
      <el-table-column label="网络域" align="center" prop="networkDomain" />
      <el-table-column label="可用区" align="center" prop="availableArea" /> -->
      <el-table-column label="k8s版本" align="center" prop="kubernetesVersion" />
      <el-table-column label="磐基2.0平台编码" align="center" prop="platformCode" width="180" />
      <el-table-column label="入库时间" align="center" prop="saveTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.saveTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" align="center">
        <template slot-scope="scope">
          <el-button size="mini" type="text" @click="onDetail(scope.row)">详情</el-button>
        </template>
      </el-table-column>
    </el-table>

    <div class="sa-footer sa-row-center">
      <sa-pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </div>

    <!-- 详情 -->
    <view-detail ref="detailRef" :detail-data="detailData" />
  </div>
</template>

<script>
import { listCluster } from '@/api/res/cluster'
import ViewDetail from '../detail.vue'
import request from '@/utils/request'
import { getColumnList, onColumn } from '../data'

export default {
  name: 'Cluster',
  components: {
    ViewDetail
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 集群资源表格数据
      clusterList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        clusterName: undefined,
        serverUrl: undefined,
        paasPlatform: undefined,
        kemName: undefined,
        resourcePool: undefined,
        paasVersion: undefined,
        platformCode: undefined
      },

      detailData: {
        type: 'cluster',
        id: null,
        formLabel: [
          {
            label: '集群名称',
            field: 'clusterName'
          },
          {
            label: '集群版本',
            field: 'clusterVersion'
          },
          {
            label: '服务地址',
            field: 'serverUrl'
          },
          {
            label: '节点数',
            field: 'nodeCount'
          },
          {
            label: 'CPU核数',
            field: 'cpu'
          },
          {
            label: '内存大小',
            field: 'memory'
          },
          {
            label: '存储大小',
            field: 'storage'
          },
          {
            label: '所属paas平台',
            field: 'paasPlatform'
          },
          {
            label: 'KEM名称',
            field: 'kemName'
          },
          {
            label: '资源池',
            field: 'resourcePool'
          },
          {
            label: '网络域',
            field: 'networkDomain'
          },
          {
            label: '可用区',
            field: 'availableArea'
          },
          {
            label: 'k8s版本',
            field: 'kubernetesVersion'
          },
          {
            label: '磐基2.0平台编码',
            field: 'platformCode'
          },
          {
            label: '入库时间',
            field: 'saveTime',
            type: 'time'
          }
        ]
      },

      columnList: []
    }
  },
  created() {
    this.queryParams = { ...this.queryParams, ...this.$route.query }
    this.getList()
  },
  methods: {
    /** 查询集群资源列表 */
    getList() {
      this.loading = true
      listCluster(this.queryParams).then((response) => {
        this.clusterList = response.rows
        this.total = response.total

        if (response.rows.length > 0) {
          this.columnList = this.getColumnList(response.rows)
        }

        this.loading = false
      })
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 导出按钮操作 */
    handleExport() {
      if (this.total > 200000) {
        this.$modal.msgWarning('抱歉，导出数据量过大，请选择筛选条件后再进行导出')
        return false
      }

      const submitAuery = JSON.parse(JSON.stringify(this.queryParams))
      delete submitAuery.pageNum
      delete submitAuery.pageSize

      this.download(
        '/res/cluster/export',
        {
          ...submitAuery
        },
        `导出${this.$route.meta.title}_${new Date().getTime()}.xlsx`
      )
    },
    onDetail(row) {
      this.detailData.id = row.id
      this.$refs.detailRef.show()
    },
    onSynResData() {
      this.loading = true
      request({
        url: `/res/system/synResData/201`,
        method: 'get',
        timeout: 3000000
      }).then((response) => {
        if (response.code == 200) {
          this.$modal.msgSuccess(response.msg)
          this.getList()
        }
        this.loading = false
      })
    },
    getColumnList,
    onColumn
  }
}
</script>
