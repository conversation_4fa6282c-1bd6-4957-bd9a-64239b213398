<template>
  <div class="page-main">
    <el-form
      v-show="showSearch"
      ref="queryForm"
      class="sa-query"
      :model="queryParams"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="租户名称" prop="tenantName">
        <el-input
          v-model="queryParams.tenantName"
          placeholder="请输入租户名称"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="租户id" prop="outId">
        <el-input
          v-model="queryParams.outId"
          placeholder="请输入租户id"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <!-- <el-form-item label="租户编码" prop="tenantCode">
        <el-input
          v-model="queryParams.tenantCode"
          placeholder="请输入租户编码"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <!-- <el-form-item label="磐基2.0平台编码" prop="platformCode">
        <el-input
          v-model="queryParams.platformCode"
          placeholder="请输入磐基2.0平台编码"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item class="query-handle">
        <el-button type="primary" plain icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh-left" @click="resetQuery">重置</el-button>
        <EleSheetSearchMore :parent-ref="()=> $refs.queryForm" />
      </el-form-item>
    </el-form>

    <div class="sa-title">
      <sa-toolbar :show-search.sync="showSearch" @queryTable="getList" />
      <el-button
        v-hasPermi="['res:tenant:export']"
        type="primary"
        icon="el-icon-download"
        :loading="exportLoading"
        @click="handleExport"
      >导出</el-button>
      <el-button v-if="$checkPermi(['res:tenant:synchronous'])" type="warning" icon="el-icon-refresh" @click="onSynResData">同步</el-button>
    </div>

    <el-table v-loading="loading" class="sa-table" :data="tenantList">
      <el-table-column label="数据类型" align="center">
        <template slot-scope="scope">
          {{ scope.row.dataType == 1 ? 'kem' : '磐基 2.0' }}
        </template>
      </el-table-column>
      <el-table-column label="租户名称" align="center" prop="tenantName" />
      <el-table-column label="租户id" align="center" prop="outId" />
      <!-- <el-table-column label="租户编码" align="center" prop="tenantCode" /> -->
      <el-table-column label="租户描述" align="center" prop="tenantDesc" />
      <el-table-column v-for="(item, index) in columnList" :key="index" :label="item.label" align="center" width="120">
        <template slot-scope="scope">
          <el-button type="text" @click="onColumn('tenant', scope.row, item.type)">
            {{ scope.row[item.field] }}
          </el-button>
        </template>
      </el-table-column>
      <el-table-column label="租户区域" align="center">
        <template slot-scope="scope">
          {{ scope.row.tenantArea == 1 ? 'O 域' : scope.row.tenantArea == 2 ? 'B 域' : 'M 域' }}
        </template>
      </el-table-column>
      <el-table-column label="租户状态" align="center">
        <template slot-scope="scope">
          {{ scope.row.tenantStatus == 1 ? '在用' : '注销' }}
        </template>
      </el-table-column>
      <!-- <el-table-column label="磐基2.0平台编码" align="center" prop="platformCode" width="180" /> -->
      <el-table-column label="入库时间" align="center" prop="saveTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.saveTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" align="center">
        <template slot-scope="scope">
          <el-button type="text" @click="onDetail(scope.row)">详情</el-button>
        </template>
      </el-table-column>
    </el-table>

    <div class="sa-footer sa-row-center">
      <sa-pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </div>

    <!-- 详情 -->
    <view-detail ref="detailRef" :detail-data="detailData" />
  </div>
</template>

<script>
import { listTenant } from '@/api/res/tenant'
import ViewDetail from '../detail.vue'
import request from '@/utils/request'
import { getColumnList, onColumn } from '../data'

export default {
  // name: 'Tenant',
  components: {
    ViewDetail
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 磐基租户资源表格数据
      tenantList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        tenantName: undefined,
        tenantCode: undefined,
        outId: undefined,
        platformCode: undefined
      },

      detailData: {
        type: 'tenant',
        id: null,
        formLabel: [
          {
            label: '租户名称',
            field: 'tenantName'
          },
          {
            label: '租户编码',
            field: 'tenantCode'
          },
          {
            label: '租户描述',
            field: 'tenantDesc'
          },
          {
            label: '租户区域',
            field: 'tenantArea',
            type: 'tenantArea'
          },
          {
            label: 'KEM名称',
            field: 'kemId'
          },
          {
            label: '添加用户',
            field: 'createUser'
          },
          {
            label: '租户状态',
            field: 'tenantStatus',
            type: 'status'
          },
          {
            label: '磐基2.0平台编码',
            field: 'platformCode'
          },
          {
            label: '入库时间',
            field: 'saveTime',
            type: 'time'
          }
        ]
      },

      columnList: []
    }
  },
  created() {
    this.queryParams = { ...this.queryParams, ...this.$route.query }
    this.getList()
  },
  methods: {
    /** 查询磐基租户资源列表 */
    getList() {
      this.loading = true
      listTenant(this.queryParams).then((response) => {
        this.tenantList = response.rows
        this.total = response.total

        if (response.rows.length > 0) {
          this.columnList = this.getColumnList(response.rows)
        }

        this.loading = false
      })
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 导出按钮操作 */
    handleExport() {
      if (this.total > 200000) {
        this.$modal.msgWarning('抱歉，导出数据量过大，请选择筛选条件后再进行导出')
        return false
      }

      const submitAuery = JSON.parse(JSON.stringify(this.queryParams))
      delete submitAuery.pageNum
      delete submitAuery.pageSize

      this.download(
        'res/tenant/export',
        {
          ...submitAuery
        },
        `导出${this.$route.meta.title}_${new Date().getTime()}.xlsx`
      )
    },
    onDetail(row) {
      this.detailData.id = row.id
      this.$refs.detailRef.show()
    },
    onSynResData() {
      this.loading = true
      request({
        url: `/res/system/synResData/202`,
        method: 'get',
        timeout: 3000000
      }).then((response) => {
        if (response.code == 200) {
          this.$modal.msgSuccess(response.msg)
          this.getList()
        }
        this.loading = false
      })
    },
    getColumnList,
    onColumn
  }
}
</script>
