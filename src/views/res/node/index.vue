<template>
  <div class="page-main">
    <el-form
      v-show="showSearch"
      ref="queryForm"
      class="sa-query"
      :model="queryParams"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="节点名称" prop="nodeName">
        <el-input
          v-model="queryParams.nodeName"
          placeholder="请输入节点名称"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="集群名称" prop="clusterName">
        <el-input
          v-model="queryParams.clusterName"
          placeholder="请输入集群名称"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <!-- <el-form-item label="操作系统" prop="operationSystem">
        <el-input
          v-model="queryParams.operationSystem"
          placeholder="请输入操作系统"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="所在资源池" prop="resourcePool">
        <el-input
          v-model="queryParams.resourcePool"
          placeholder="请输入所在资源池"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item label="节点IP" prop="nodeIp">
        <el-input
          v-model="queryParams.nodeIp"
          placeholder="请输入节点IP"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="KEM名称" prop="kemName">
        <el-input
          v-model="queryParams.kemName"
          placeholder="请输入KEM名称"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <!-- <el-form-item label="网络域" prop="networkDomain">
        <el-input
          v-model="queryParams.networkDomain"
          placeholder="请输入网络域"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="业务IP" prop="serviceIp">
        <el-input
          v-model="queryParams.serviceIp"
          placeholder="请输入业务IP"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="承载网IP" prop="bearerNetworkIp">
        <el-input
          v-model="queryParams.bearerNetworkIp"
          placeholder="请输入承载网IP"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="管理网IP" prop="managementNetworkIp">
        <el-input
          v-model="queryParams.managementNetworkIp"
          placeholder="请输入管理网IP"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="租户名称" prop="tenantName">
        <el-input
          v-model="queryParams.tenantName"
          placeholder="请输入租户名称"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <!-- <el-form-item label="磐基2.0平台编码" prop="platformCode">
        <el-input
          v-model="queryParams.platformCode"
          placeholder="请输入磐基2.0平台编码"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item class="query-handle">
        <el-button type="primary" plain icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh-left" @click="resetQuery">重置</el-button>
        <EleSheetSearchMore :parent-ref="()=> $refs.queryForm" />
      </el-form-item>
    </el-form>

    <div class="sa-title">
      <sa-toolbar :show-search.sync="showSearch" @queryTable="getList" />
      <el-button
        v-hasPermi="['res:node:export']"
        type="primary"
        icon="el-icon-download"
        :loading="exportLoading"
        @click="handleExport"
      >导出</el-button>
      <el-button v-if="$checkPermi(['res:node:synchronous'])" type="warning" icon="el-icon-refresh" @click="onSynResData">同步</el-button>
    </div>

    <el-table v-loading="loading" class="sa-table" :data="nodeList">
      <el-table-column label="数据类型" align="center">
        <template slot-scope="scope">
          {{ scope.row.dataType == 1 ? 'kem' : '磐基 2.0' }}
        </template>
      </el-table-column>
      <el-table-column label="节点名称" align="center" prop="nodeName" width="180" />
      <el-table-column label="集群名称" align="center" prop="clusterName" />
      <!-- <el-table-column label="物理位置" align="center" prop="location" />
      <el-table-column label="操作系统" align="center" prop="operationSystem" />
      <el-table-column label="主机类型" align="center" prop="hostType" />
      <el-table-column label="所在资源池" align="center" prop="resourcePool" width="180" />
      <el-table-column label="节点内存" align="center" prop="nodeMemory" />
      <el-table-column label="节点cpu" align="center" prop="nodeCpu" />
      <el-table-column label="节点存储" align="center" prop="nodeStorage" /> -->
      <el-table-column label="节点IP" align="center" prop="nodeIp" />
      <el-table-column label="KEM名称" align="center" prop="kemName" />
      <!-- <el-table-column label="网络域" align="center" prop="networkDomain" />
      <el-table-column label="可用区" align="center" prop="availableArea" />
      <el-table-column label="业务IP" align="center" prop="serviceIp" />
      <el-table-column label="承载网IP" align="center" prop="bearerNetworkIp" />
      <el-table-column label="管理网IP" align="center" prop="managementNetworkIp" />
      <el-table-column label="租户名称" align="center" prop="tenantName" /> -->
      <!-- <el-table-column label="磐基2.0平台编码" align="center" prop="platformCode" width="180" /> -->
      <el-table-column label="数据保存时间" align="center" prop="saveTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.saveTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" align="center">
        <template slot-scope="scope">
          <el-button type="text" @click="onDetail(scope.row)">详情</el-button>
        </template>
      </el-table-column>
    </el-table>

    <div class="sa-footer sa-row-center">
      <sa-pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </div>

    <!-- 详情 -->
    <view-detail ref="detailRef" :detail-data="detailData" />
  </div>
</template>

<script>
import { listNode } from '@/api/res/node'
import { getToken } from '@/utils/auth'
import ViewDetail from '../detail.vue'
import request from '@/utils/request'

export default {
  // name: 'Node',
  components: {
    ViewDetail
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 节点资源表格数据
      nodeList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        nodeName: undefined,
        clusterName: undefined,
        operationSystem: undefined,
        resourcePool: undefined,
        nodeIp: undefined,
        kemName: undefined,
        networkDomain: undefined,
        serviceIp: undefined,
        bearerNetworkIp: undefined,
        managementNetworkIp: undefined,
        tenantName: undefined,
        platformCode: undefined
      },

      detailData: {
        type: 'node',
        id: null,
        formLabel: [
          {
            label: '节点名称',
            field: 'nodeName'
          },
          {
            label: '集群名称',
            field: 'clusterName'
          },
          {
            label: '物理位置',
            field: 'location'
          },
          {
            label: '操作系统',
            field: 'operationSystem'
          },
          {
            label: '主机类型',
            field: 'hostType'
          },
          {
            label: '所在资源池',
            field: 'resourcePool'
          },
          {
            label: '节点内存',
            field: 'nodeMemory'
          },
          {
            label: '节点cpu',
            field: 'nodeCpu'
          },
          {
            label: '节点存储',
            field: 'nodeStorage'
          },
          {
            label: '节点IP',
            field: 'nodeIp'
          },
          {
            label: 'KEM名称',
            field: 'kemName'
          },
          {
            label: '网络域',
            field: 'networkDomain'
          },
          {
            label: '可用区',
            field: 'availableArea'
          },
          {
            label: '业务IP',
            field: 'serviceIp'
          },
          {
            label: '承载网IP',
            field: 'bearerNetworkIp'
          },
          {
            label: '管理网IP',
            field: 'managementNetworkIp'
          },
          {
            label: '租户名称',
            field: 'tenantName'
          },
          {
            label: '磐基2.0平台编码',
            field: 'platformCode'
          },
          {
            label: '入库时间',
            field: 'saveTime',
            type: 'time'
          }
        ]
      }
    }
  },
  created() {
    this.queryParams = { ...this.queryParams, ...this.$route.query }
    this.getList()
  },
  methods: {
    /** 查询节点资源列表 */
    getList() {
      this.loading = true
      listNode(this.queryParams).then((response) => {
        this.nodeList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 导出按钮操作 */
    handleExport() {
      if (this.total > 200000) {
        this.$modal.msgWarning('抱歉，导出数据量过大，请选择筛选条件后再进行导出')
        return false
      }

      const submitAuery = JSON.parse(JSON.stringify(this.queryParams))
      delete submitAuery.pageNum
      delete submitAuery.pageSize

      this.download(
        'res/node/export',
        {
          ...submitAuery
        },
        `导出${this.$route.meta.title}_${new Date().getTime()}.xlsx`
      )
    },
    onDetail(row) {
      this.detailData.id = row.id
      this.$refs.detailRef.show()
    },
    onSynResData() {
      this.loading = true
      request({
        url: `/res/system/synResData/206`,
        method: 'get',
        timeout: 3000000
      }).then((response) => {
        if (response.code == 200) {
          this.$modal.msgSuccess(response.msg)
          this.getList()
        }
        this.loading = false
      })
    }
  }
}
</script>
