<template>
  <EleSheet ref="sheetRef" v-bind="sheetProps" class="page-main">
    <template #before> </template>

    <template #table:action:after="{ row }">
      <el-button v-if="$checkPermi(['res:mirror:repository:imageList'])" size="mini" type="text" @click="onMirrorClick(row)">镜像列表</el-button>
    </template>

    <template #after> </template>
  </EleSheet>
</template>

<script>
import {
  addMirrorRepository,
  delMirrorRepository,
  getMirrorRepository,
  listMirrorRepository,
  updateMirrorRepository
} from '@/api/res/mirror/repository.js'

import request from '@/utils/request.js'

export default {
  data() {
    return {}
  },
  computed: {
    params() {
      return this.$attrs.params || this.$route.query
    },
    sheetProps() {
      const value = {
        title: '镜像仓库',

        lazy: false,

        api: {
          // add: (params) => addMirrorRepository({ ...params }),
          // edit: (params) => updateMirrorRepository({ ...params }),
          list: async(params) => listMirrorRepository({ ...this.params, ...params }),
          info: getMirrorRepository,
          remove: delMirrorRepository,
          export: '/res/pz/imageRepo/export',
          // import: '/res/pz/imageRepo/import',
          template: '/res/pz/imageRepo/importTemplate'
        },

        infoProps: {
          title: true
        },

        hiddenActions: {
          add: true,
          edit: true,
          remove: true,
          export: !this.$checkPermi(['res:mirror:repository:export'])
        },

        model: {
          repoName: {
            type: 'text',
            label: '镜像库名称',
            table: {
            },
            search: {
              hidden: false
            },
            form: {
              rules: true
            }
          },
          repoType: {
            label: '镜像库类型',
            table: {
            },
            search: {
              hidden: false
            },
            form: {
              rules: true
            }
          },
          isTenant: {
            type: 'select',
            label: '是否项目群可见',
            table: {
            },
            search: {
              hidden: false
            },
            form: {
              rules: false
            }
          },
          describeInfo: {
            type: 'text',
            label: '描述信息',
            table: {
            },
            search: {
              hidden: false
            },
            form: {
              rules: false,
              type: 'textarea'
            }
          },
          createUser: {
            type: 'text',
            label: '创建人',
            table: {
            },
            search: {
              hidden: false
            },
            form: {
              hidden: true,
              rules: false
            }
          },
          dataType: {
            type: 'text',
            label: '数据类型',
            table: {
            },
            form: {
              hidden: true
            },
            search: {
            }
          },
          createTime: {
            type: 'text',
            label: '入库时间',
            search: {
              type: 'date-range',
              parameter: (data) => {
                return {
                  startTime: data?.[0],
                  endTime: data?.[1]
                }
              }
            },
            table: {
              width: 200
            },
            form: {
              hidden: true
            }
          },
          showRepoName: {
            type: 'text',
            label: '仓库显示名称',
            table: {
              hidden: true
            },
            search: {
              hidden: true
            },
            form: {
              rules: false
            },
            group: 'detail'
          },
          mirrorUrl: {
            type: 'text',
            label: '仓库地址',
            table: {
              hidden: true
            },
            search: {
              hidden: true
            },
            form: {
              rules: false
            },
            group: 'detail'
          },
          systemTag: {
            type: 'text',
            label: '项目标识',
            table: {
              hidden: true
            },
            search: {
              hidden: true,
              value: this.params.systemTag
            },
            form: {
              rules: false
            },
            group: 'detail'
          },
          repoKey: {
            type: 'text',
            label: '镜像库唯一标识',
            table: {
              hidden: true
            },
            search: {
              hidden: true
            },
            form: {
              hidden: true,
              rules: false
            },
            group: 'detail'
          },
          remarks: {
            type: 'text',
            label: '备注',
            table: {
              hidden: true
            },
            search: {
              hidden: true
            },
            form: {
              type: 'textarea',
              rules: false
            },
            group: 'detail'
          },
          updateUser: {
            type: 'text',
            label: '更新者',
            table: {
              hidden: true
            },
            search: {
              hidden: true
            },
            form: {
              hidden: true,
              rules: false
            },
            group: 'detail'
          },
          updateTime: {
            label: '更新时间',
            table: {
              width: 200,
              hidden: true
            },
            search: {
              hidden: true
            },
            form: {
              hidden: true,
              rules: false
            },
            group: 'detail'
          }

        }
      }

      return value
    }
  },
  methods: {
    onMirrorClick(row) {
      this.$router.push({
        path: '/res/mirror/detail',
        query: {
          repoKey: row.repoKey
        }
      })
    }
  }
}
</script>

<style></style>
