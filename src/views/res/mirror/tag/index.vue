<template>
  <EleSheet ref="sheetRef" v-bind="sheetProps" class="page-main">
    <template #before> </template>

    <template #form:deptName:simple="{ model }">
      <CommonDepartmentSelect
        v-model="model.deptName"
        placeholder="请输入"
        clearable
        return-name
        @change="() => (model.systemName = void 0)"
      />
    </template>

    <template #form:systemName:simple="{ model }">
      <CommonSystemSelect
        v-model="model.systemName"
        placeholder="请输入"
        return-name
        clearable
        :params="{
          deptName: model.deptName,
        }"
      />
    </template>

    <template #after> </template>
  </EleSheet>
</template>

<script>
import {
  addMirrorTag,
  delMirrorTag,
  getMirrorTag,
  listMirrorTag,
  updateMirrorTag
} from '@/api/res/mirror/tag.js'

import request from '@/utils/request.js'

export default {
  dicts: ['current_path_type'],
  data() {
    return {}
  },
  computed: {
    params() {
      return this.$attrs.params || this.$route.query
    },
    sheetProps() {
      const value = {
        title: '镜像Tag',

        lazy: false,

        api: {
          // add: (params) => addMirrorTag({ ...params }),
          edit: (params) => updateMirrorTag({ ...params }),
          list: async(params) => listMirrorTag({ ...this.params, ...params }),
          info: getMirrorTag,
          remove: delMirrorTag,
          export: '/res/pz/imageTag/export',
          // import: '/res/pz/imageTag/import',
          template: '/res/pz/imageTag/importTemplate'
        },

        infoProps: {
          title: true
        },

        hiddenActions: {
          add: true,
          remove: true,
          edit: !this.$checkPermi(['res:mirror:tag:edit']),
          export: !this.$checkPermi(['res:mirror:tag:export'])
        },

        model: {
          imageName: {
            type: 'text',
            label: '镜像名称',
            table: {
            },
            search: {
              value: this.params.imageName
            },
            form: {
              rules: true
            }
          },
          repoKey: {
            type: 'text',
            label: '镜像库名称',
            table: {
            },
            search: {
              value: this.params.repoKey
            },
            form: {
            }
          },
          deptName: {
            type: 'text',
            label: '部门名称',
            table: {
              hidden: true
            },
            search: {
              hidden: true
            },
            form: {
              rules: false
            }
          },
          systemName: {
            type: 'text',
            label: '业务系统',
            table: {
              hidden: true
            },
            search: {
              hidden: true
            },
            form: {
              rules: false
            }
          },
          projectName: {
            type: 'text',
            label: '项目名称',
            table: {
              hidden: true
            },
            search: {
              hidden: true
            },
            form: {
              rules: false
            }
          },
          text: {
            type: 'text',
            label: '镜像版本',
            table: {
            },
            search: {
              hidden: true
            },
            form: {
              rules: false
            }
          },
          type: {
            type: 'select',
            label: '仓库类型',
            table: {
            },
            search: {
              hidden: false
            },
            form: {
              hidden: true,
              rules: false
            },
            options: this.dict.type.current_path_type
          },
          digest: {
            type: 'text',
            label: '镜像digest签名',
            table: {
            },
            search: {
              hidden: true
            },
            form: {
              rules: false
            }
          },
          tags: {
            type: 'text',
            label: '镜像标签',
            table: {
            },
            search: {
              hidden: true
            },
            form: {
              hidden: true,
              rules: false
            }
          },
          createUser: {
            type: 'text',
            label: '创建人',
            table: {
              hidden: true
            },
            search: {
              hidden: true
            },
            form: {
              hidden: true
            }
          },
          dataType: {
            type: 'text',
            label: '数据类型',
            table: {
            },
            form: {
              hidden: true
            },
            search: {
            }
          },
          createTime: {
            type: 'text',
            label: '入库时间',
            table: {
              width: 200
            },
            search: {
              type: 'date-range',
              parameter: (data) => {
                return {
                  startTime: data?.[0],
                  endTime: data?.[1]
                }
              }
            },
            form: {
              hidden: true
            }
          },
          imageUrl: {
            type: 'text',
            label: '镜像地址',
            table: {
              hidden: true
            },
            search: {
              hidden: true
            },
            form: {
            },
            group: 'detail'
          },
          repoType: {
            label: '镜像库类型',
            table: {
              hidden: true
            },
            search: {
              hidden: true
            },
            form: {
              hidden: true
            },
            group: 'detail'
          },
          systemTag: {
            type: 'text',
            label: '项目标识',
            table: {
              hidden: true
            },
            search: {
              hidden: true
            },
            form: {
              hidden: true
            },
            group: 'detail'
          },
          distribution: {
            type: 'text',
            label: '操作系统发行版',
            table: {
              hidden: true
            },
            search: {
              hidden: true
            },
            form: {
              hidden: true
            },
            group: 'detail'
          },
          local: {
            type: 'select',
            label: '是否为本地镜像',
            table: {
              hidden: true
            },
            search: {
              hidden: true
            },
            form: {
              hidden: true
            },
            options: [
              {
                label: '是',
                value: 'true'
              },
              {
                label: '否',
                value: 'false'
              }
            ],
            group: 'detail'
          },
          path: {
            type: 'text',
            label: '镜像路径',
            table: {
              hidden: true
            },
            search: {
              hidden: true
            },
            form: {
              hidden: true
            },
            group: 'detail'
          },
          mediaType: {
            type: 'text',
            label: '镜像的媒介类型',
            table: {
              hidden: true
            },
            search: {
              hidden: true
            },
            form: {
              hidden: true
            },
            group: 'detail'
          },
          manifestMediaType: {
            type: 'text',
            label: '镜像媒介类型清单',
            table: {
              hidden: true
            },
            search: {
              hidden: true
            },
            form: {
              hidden: true
            },
            group: 'detail'
          },
          trash: {
            type: 'select',
            label: '是否是无用镜像',
            table: {
              hidden: true
            },
            search: {
              hidden: true
            },
            form: {
              hidden: true
            },
            options: [
              {
                label: '是',
                value: 'true'
              },
              {
                label: '否',
                value: 'false'
              }
            ],
            group: 'detail'
          },
          size: {
            type: 'text',
            label: '镜像大小',
            table: {
              hidden: true
            },
            search: {
              hidden: true
            },
            form: {
              hidden: true
            },
            group: 'detail'
          },
          hasChild: {
            type: 'select',
            label: '是否有子文件夹',
            table: {
              hidden: true
            },
            search: {
              hidden: true
            },
            form: {
              hidden: true
            },
            options: [
              {
                label: '是',
                value: 'true'
              },
              {
                label: '否',
                value: 'false'
              }
            ],
            group: 'detail'
          },
          imageDomainPublic: {
            type: 'text',
            label: '公共网镜像库域名地址',
            table: {
              hidden: true
            },
            search: {
              hidden: true
            },
            form: {
              hidden: true
            },
            group: 'detail'
          },
          imageDomainPrivate: {
            type: 'text',
            label: '办公网镜像库域名地址',
            table: {
              hidden: true
            },
            search: {
              hidden: true
            },
            form: {
              hidden: true
            },
            group: 'detail'
          },
          remarks: {
            label: '备注',
            table: {
              hidden: true
            },
            search: {
              hidden: true
            },
            form: {
              rules: false
            },
            group: 'detail'
          },

          updateUser: {
            type: 'text',
            label: '更新者',
            table: {
              hidden: true
            },
            search: {
              hidden: true
            },
            form: {
              hidden: true
            },
            group: 'detail'
          },
          updateTime: {
            label: '更新时间',
            table: {
              width: 200,
              hidden: true
            },
            search: {
              hidden: true
            },
            form: {
              hidden: true
            },
            group: 'detail'
          }
        }
      }

      return value
    }
  },
  methods: {}
}
</script>

<style></style>
