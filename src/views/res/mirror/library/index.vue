<template>
  <EleSheet ref="sheetRef" v-bind="sheetProps" class="page-main">
    <template #before> </template>

    <template #search:businessDepartment:simple="{ model }">
      <CommonDepartmentSelect
        v-model="model.businessDepartment"
        placeholder="请输入"
        clearable
        return-name
        @change="() => (model.businessSystem = void 0)"
      />
    </template>

    <template #search:businessSystem:simple="{ model }">
      <CommonSystemSelect
        v-model="model.businessSystem"
        placeholder="请输入"
        return-name
        clearable
        :params="{
          deptName: model.businessDepartment,
        }"
      />
    </template>

    <template #after> </template>
  </EleSheet>
</template>

<script>
import {
  addMirrorLibrary,
  delMirrorLibrary,
  getMirrorLibrary,
  listMirrorLibrary,
  updateMirrorLibrary
} from '@/api/res/mirror/library.js'

import request from '@/utils/request.js'

export default {
  data() {
    return {}
  },
  computed: {
    sheetProps() {
      const value = {
        title: '镜像库',

        lazy: false,

        api: {
          // add: (params) => addMirrorLibrary({ ...params }),
          // edit: (params) => updateMirrorLibrary({ ...params }),
          list: async(params) => listMirrorLibrary({ ...params }),
          info: getMirrorLibrary,
          remove: delMirrorLibrary,
          export: '/risk/imageInspect/container/export',
          // import: '/risk/imageInspect/container/import',
          template: '/risk/imageInspect/container/importTemplate'
        },

        infoProps: {
          title: true
        },

        hiddenActions: {
          add: true,
          edit: true,
          remove: true,
          export: !this.$checkPermi(['res:mirror:detail:export'])
        },

        model: {
          name: {
            type: 'text',
            label: '镜像名称',
            table: {
            },
            search: {
              hidden: false
            },
            form: {
              rules: true
            }
          },
          businessDepartment: {
            label: '业务部门',
            table: {
            },
            search: {
              hidden: false
            },
            form: {
              rules: true
            }
          },
          businessSystem: {
            label: '业务系统',
            table: {
            },
            search: {
              hidden: false
            },
            form: {
              rules: true
            }
          },
          version: {
            type: 'text',
            label: '镜像版本',
            table: {
            },
            search: {
              hidden: false
            },
            form: {
              rules: true
            }
          },
          riskLevel: {
            label: '风险等级',
            table: {
            },
            search: {
              hidden: false
            },
            form: {
              rules: false
            }
          },
          imageState: {
            type: 'select',
            label: '运行状态',
            table: {},
            search: {
              hidden: false
            },
            form: {
              rules: false
            },
            options: [
              { label: '运行中', value: '0', raw: { listClass: 'success' }},
              { label: '未运行', value: '1', raw: { listClass: 'info' }}
            ]
          },
          createDate: {
            label: '入库时间',
            table: {
              width: 200
            },
            search: {
              type: 'date-time-range',
              parameter: (data) => {
                return {
                  startTime: data?.[0],
                  endTime: data?.[1]
                }
              },
              hidden: false
            },
            form: {
              rules: false
            }
          },
          registry: {
            type: 'text',
            label: '仓库名称',
            table: {
              hidden: true
            },
            search: {
              hidden: true
            },
            group: 'detail',
            form: {
              rules: false
            }
          },
          os: {
            type: 'text',
            label: '镜像OS信息',
            table: {
              hidden: true
            },
            search: {
              hidden: true
            },
            group: 'detail',
            form: {
              rules: false
            }
          },
          features: {
            type: 'text',
            label: '基础镜像',
            table: {
              hidden: true
            },
            search: {
              hidden: true
            },
            group: 'detail',
            form: {
              rules: false
            }
          },
          fromBaseImage: {
            type: 'select',
            label: '是否使用指定基础镜像',
            table: {
              hidden: true
            },
            search: {
              hidden: true
            },
            group: 'detail',
            form: {
              rules: false
            }
          },
          packageCount: {
            label: '包含软件包数量',
            table: {
              hidden: true
            },
            search: {
              hidden: true
            },
            group: 'detail',
            form: {
              type: 'number',
              rules: false
            }
          },
          containerCount: {
            label: '关联容器数量',
            table: {
              hidden: true
            },
            search: {
              hidden: true
            },
            group: 'detail',
            form: {
              type: 'number',
              rules: false
            }
          },
          host: {
            type: 'text',
            label: '节点名称',
            table: {
              hidden: true
            },
            search: {
              hidden: true
            },
            group: 'detail',
            form: {
              rules: false
            }
          },
          securityIssue: {
            type: 'text',
            label: '安全问题',
            table: {
              hidden: true
            },
            search: {
              hidden: true
            },
            group: 'detail',
            form: {
              rules: false
            }
          },
          imageHashId: {
            type: 'text',
            label: '镜像哈希Id',
            table: {
              hidden: true
            },
            search: {
              hidden: true
            },
            group: 'detail',
            form: {
              rules: false
            }
          },
          scanState: {
            label: '扫描状态',
            table: {
              hidden: true
            },
            search: {
              hidden: true
            },
            group: 'detail',
            form: {
              rules: false
            }
          },
          scanCount: {
            label: '扫描次数',
            table: {
              hidden: true
            },
            search: {
              hidden: true
            },
            group: 'detail',
            form: {
              type: 'number',
              rules: false
            }
          },
          discoveryTime: {
            label: '发现时间',
            table: {
              hidden: true
            },
            search: {
              hidden: true
            },
            group: 'detail',
            form: {
              type: 'date-time',
              rules: false
            }
          },
          updateDate: {
            label: '更新时间',
            table: {
              hidden: true
            },
            search: {
              hidden: true
            },
            group: 'detail',
            form: {
              type: 'date-time',
              rules: false
            }
          },
          trust: {
            type: 'select',
            label: '是否信任',
            table: {
              hidden: true
            },
            search: {
              hidden: true
            },
            group: 'detail',
            form: {
              rules: false
            }
          },
          isHistory: {
            type: 'select',
            label: '是否是历史镜像',
            table: {
              hidden: true
            },
            search: {
              hidden: true
            },
            group: 'detail',
            form: {
              rules: false
            }
          },
          blocked: {
            type: 'select',
            label: '是否已阻断',
            table: {
              hidden: true
            },
            search: {
              hidden: true
            },
            group: 'detail',
            form: {
              rules: false
            }
          },
          clusterName: {
            type: 'text',
            label: '集群名称',
            table: {
              hidden: true
            },
            search: {
              hidden: true
            },
            group: 'detail',
            form: {
              rules: false
            }
          },
          autoPushResult: {
            type: 'text',
            label: '自动推送结果',
            table: {
              hidden: true
            },
            search: {
              hidden: true
            },
            group: 'detail',
            form: {
              rules: false
            }
          },
          autoPushFailRes: {
            type: 'text',
            label: '自动推送失败原因',
            table: {
              hidden: true
            },
            search: {
              hidden: true
            },
            group: 'detail',
            form: {
              rules: false
            }
          }
          // createTime: {
          //   type: 'text',
          //   label: '创建时间',
          //   search: {
          //     type: 'date-range',
          //     parameter: (data) => {
          //       return {
          //         startTime: data?.[0],
          //         endTime: data?.[1]
          //       }
          //     }
          //   },
          //   form: {
          //     hidden: true
          //   },
          // }
        }
      }

      return value
    }
  },
  methods: {}
}
</script>

<style></style>
