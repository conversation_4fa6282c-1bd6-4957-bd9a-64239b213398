<template>
  <EleSheet ref="sheetRef" v-bind="sheetProps" class="page-main">
    <template #before> </template>

    <template #table:action:after="{ row }">
      <el-button v-if="$checkPermi(['res:mirror:detail:imageTagList'])" size="mini" type="text" @click="onTagClick(row)">镜像Tag列表</el-button>
    </template>

    <template #after> </template>
  </EleSheet>
</template>

<script>
import {
  addMirrorDetail,
  delMirrorDetail,
  getMirrorDetail,
  listMirrorDetail,
  updateMirrorDetail
} from '@/api/res/mirror/detail.js'

import request from '@/utils/request.js'

export default {
  dicts: ['current_path_type'],
  data() {
    return {}
  },
  computed: {
    params() {
      return this.$attrs.params || this.$route.query
    },
    sheetProps() {
      const value = {
        title: '镜像详情',

        lazy: false,

        api: {
          // add: (params) => addMirrorDetail({ ...params }),
          // edit: (params) => updateMirrorDetail({ ...params }),
          list: async(params) => listMirrorDetail({ ...this.params, ...params }),
          info: getMirrorDetail,
          remove: delMirrorDetail,
          export: '/res/image/export',
          // import: '/res/image/import',
          template: '/res/image/importTemplate'
        },

        infoProps: {
          title: true
        },

        hiddenActions: {
          add: true,
          edit: true,
          remove: true,
          export: !this.$checkPermi(['res:mirror:detail:export'])
        },

        model: {
          name: {
            type: 'text',
            label: '镜像名称',
            table: {
              align: 'left'
            },
            form: {
              rules: true
            },
            search: {
              hidden: false
            }
          },
          repoKey: {
            type: 'text',
            label: '仓库名称',
            table: {
            },
            form: {
              rules: true

            },
            search: {
              hidden: false,
              value: this.params.repoKey
            }
          },
          path: {
            type: 'text',
            label: '当前层路径',
            table: {
              width: 100
            },
            form: {
              rules: false
            },
            search: {
              hidden: true
            }
          },
          type: {
            type: 'select',
            label: '当前层路径类型',
            table: {
            },
            form: {
              rules: false
            },
            search: {
              hidden: true
            },
            options: this.dict.type.current_path_type
          },
          hasChild: {
            type: 'select',
            label: '是否存在下层路径',
            table: {
            },
            form: {
              rules: false
            },
            search: {
              hidden: true
            },
            options: [
              {
                label: '是',
                value: 'true'
              },
              {
                label: '否',
                value: 'false'
              }
            ]
          },
          repoType: {
            type: 'select',
            label: '镜像仓库类型',
            table: {
              hidden: true
            },
            form: {
              rules: true
            },
            search: {
              hidden: false
            },
            options: [
            ]
          },
          remarks: {
            type: 'textarea',
            label: '备注',
            table: {
              hidden: true
            },
            form: {
              rules: false
            },
            search: {
              hidden: true
            }
          },
          createUser: {
            type: 'text',
            label: '创建人',
            table: {
              hidden: true
            },
            form: {
              hidden: true
            },
            search: {
              hidden: true
            }
          },
          dataType: {
            type: 'text',
            label: '数据类型',
            table: {
            },
            form: {
              hidden: true
            },
            search: {
            }
          },
          createTime: {
            type: 'text',
            label: '入库时间',
            search: {
              type: 'date-range',
              parameter: (data) => {
                return {
                  startTime: data?.[0],
                  endTime: data?.[1]
                }
              }
            },
            table: {
              width: 200
            },
            form: {
              hidden: true
            }
          },
          updateUser: {
            type: 'text',
            label: '更新者',
            table: {
              hidden: true
            },
            form: {
              hidden: true
            },
            search: {
              hidden: true
            }
          },
          updateTime: {
            label: '更新时间',
            table: {
              width: 200,
              hidden: true
            },
            form: {
              hidden: true
            },
            search: {
              hidden: true
            }
          }

        }
      }

      return value
    }
  },
  methods: {
    onTagClick(row) {
      this.$router.push({
        path: '/res/mirror/tag',
        query: {
          repoKey: row.repoKey,
          imageName: row.path
        }
      })
    }
  }
}
</script>

<style></style>
