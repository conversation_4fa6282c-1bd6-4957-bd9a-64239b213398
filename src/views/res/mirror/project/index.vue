<template>
  <EleSheet ref="sheetRef" v-bind="sheetProps" class="page-main">
    <template #before> </template>

    <template #table:action:after="{ row }">
      <el-button v-if="$checkPermi(['res:mirror:project:imageWarehouse'])" size="mini" type="text" @click="onRepositoryClick(row)">镜像仓库</el-button>
    </template>

    <template #after> </template>
  </EleSheet>
</template>

<script>
import {
  addMirrorProject,
  delMirrorProject,
  getMirrorProject,
  listMirrorProject,
  updateMirrorProject
} from '@/api/res/mirror/project.js'

import request from '@/utils/request.js'

export default {
  data() {
    return {}
  },
  computed: {
    sheetProps() {
      const value = {
        title: '项目',

        lazy: false,

        api: {
          // add: (params) => addMirrorProject({ ...params }),
          // edit: (params) => updateMirrorProject({ ...params }),
          list: async(params) => listMirrorProject({ ...params }),
          info: getMirrorProject,
          remove: delMirrorProject,
          export: '/res/project/export',
          // import: '/res/project/import',
          template: '/res/project/importTemplate'
        },

        infoProps: {
          title: true
        },

        hiddenActions: {
          add: true,
          edit: true,
          remove: true,
          export: !this.$checkPermi(['res:mirror:project:export'])
        },

        model: {
          tenantTag: {
            type: 'text',
            label: '项目群标识',
            table: {
            },
            search: {
              hidden: true
            },
            form: {
              rules: false
            }
          },
          tenantName: {
            type: 'text',
            label: '项目群名称',
            table: {
            },
            search: {
              hidden: false
            },
            form: {
              rules: true
            }
          },
          systemName: {
            type: 'text',
            label: '项目名称',
            table: {
            },
            search: {
              hidden: false
            },
            form: {
              rules: true
            }
          },
          systemTag: {
            type: 'text',
            label: '项目标识',
            table: {
            },
            search: {
              hidden: true
            },
            form: {
              rules: false
            }
          },
          projectTemplate: {
            type: 'text',
            label: '项目模板',
            table: {
              hidden: true
            },
            search: {
              hidden: true
            },
            form: {
              rules: false
            }
          },
          pmUserName: {
            type: 'text',
            label: '项目管理者名称',
            table: {
            },
            search: {
              hidden: false
            },
            form: {
              rules: true
            }
          },
          pmUserAccount: {
            type: 'text',
            label: '项目管理者账号',
            table: {
              hidden: true
            },
            search: {
              hidden: true
            },
            form: {
              rules: false
            }
          },
          remarks: {
            type: 'textarea',
            label: '备注',
            table: {
              hidden: true
            },
            search: {
              hidden: true
            },
            form: {
              rules: false
            }
          },
          createUser: {
            type: 'text',
            label: '创建人',
            table: {
              hidden: true
            },
            search: {
              hidden: true
            },
            form: {
              rules: false,
              hidden: true
            }
          },
          dataType: {
            type: 'text',
            label: '数据类型',
            table: {
            },
            form: {
              hidden: true
            },
            search: {
            }
          },
          createTime: {
            type: 'text',
            label: '入库时间',
            search: {
              type: 'date-range',
              parameter: (data) => {
                return {
                  startTime: data?.[0],
                  endTime: data?.[1]
                }
              }
            },
            form: {
              hidden: true
            },
            table: {
              width: 200
            }
          },
          updateUser: {
            type: 'text',
            label: '更新人',
            table: {
              hidden: true
            },
            search: {
              hidden: true
            },
            form: {
              rules: false
            }
          },
          updateTime: {
            type: 'datetime',
            label: '更新时间',
            table: {
              width: 200,
              hidden: true
            },
            search: {
              hidden: true
            },
            form: {
              rules: false
            }
          }

        }
      }

      return value
    }
  },
  methods: {
    onRepositoryClick(row) {
      this.$router.push({
        path: '/res/mirror/repository',
        query: {
          systemTag: row.systemTag
        }
      })
    }
  }
}
</script>

<style></style>
