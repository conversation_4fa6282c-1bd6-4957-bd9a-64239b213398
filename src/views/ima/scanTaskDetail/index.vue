<template>
  <div class="page-main">
    <el-form
      v-show="showSearch"
      ref="queryForm"
      class="sa-query"
      :model="queryParams"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="镜像名称" prop="imageName">
        <el-input
          v-model="queryParams.imageName"
          placeholder="请输入镜像名称"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="镜像地址" prop="finallyPushUrl">
        <el-input
          v-model="queryParams.finallyPushUrl"
          placeholder="请输入镜像地址"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="构成镜像地址" prop="pushUrl">
        <el-input
          v-model="queryParams.pushUrl"
          placeholder="请输入构成镜像地址"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="构建状态" prop="buildStatus">
        <el-select
          v-model="queryParams.buildStatus"
          placeholder="请选择构建状态"
          clearable
          @change="handleQuery"
        >
          <el-option
            v-for="dict in dict.type.image_build_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="构建结果" prop="buildResult">
        <EleSelectDict v-model="queryParams.buildResult" :options="dict.type.image_build_result" />
      </el-form-item>
      <el-form-item label="扫描状态" prop="scanStatus">
        <EleSelectDict v-model="queryParams.scanStatus" :options="dict.type.scan_status" />
      </el-form-item>

      <el-form-item label="扫描分数" prop="scanScore">
        <el-select v-model="scanScore" placeholder="请选择扫描分数" clearable @change="handleQuery">
          <el-option label="90-100" value="90-100" />
          <el-option label="90以下" value="0-90" />
        </el-select>
      </el-form-item>

      <el-form-item label="单镜像推送状态" prop="isPushPz">
        <EleSelectDict v-model="queryParams.isPushPz" :options="dict.type.is_push_pz" />
      </el-form-item>
      <el-form-item label="合并推送状态" prop="isManifestPush">
        <EleSelectDict v-model="queryParams.isManifestPush" :options="dict.type.is_push_pz" />
      </el-form-item>
      <!-- <el-form-item label="修复建议" prop="repairSuggest">
        <el-input
          v-model="queryParams.repairSuggest"
          placeholder="请输入修复建议"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item class="daterange" label="时间区间" prop="daterange">
        <el-date-picker
          v-model="daterange"
          type="daterange"
          value-format="yyyy-MM-dd"
          format="yyyy-MM-dd"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="onChangeDaterange"
        />
      </el-form-item>
      <el-form-item class="query-handle">
        <el-button type="primary" plain icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh-left" @click="resetQuery">重置</el-button>
        <EleSheetSearchMore :parent-ref="()=> $refs.queryForm" />
      </el-form-item>
    </el-form>

    <div class="sa-title space-x-3">
      <sa-toolbar :show-search.sync="showSearch" @queryTable="getList" />
      <el-button
        v-hasPermi="['ima:scanTaskDetail:export']"
        type="primary"
        icon="el-icon-download"
        :loading="exportLoading"
        @click="handleExport"
      >导出</el-button>
      <el-button
        v-hasPermi="['ima:scanTaskDetail:remove']"
        type="danger"
        plain
        :disabled="ids.length == 0"
        @click="handleDelete"
      ><i class="iconfont icon-piliangshanchu" />批量删除</el-button>

      <el-button v-if="$checkPermi(['ima:scan'])" type="primary" icon="el-icon-full-screen" :disabled="ids.length == 0" @click="onBatchSan()">镜像扫描</el-button>

      <el-button v-if="$checkPermi(['ima:merge:push'])" type="warning" icon="el-icon-position" :disabled="ids.length < 2" @click="onPushImaPz()">合并推送</el-button>

      <el-button v-if="$checkPermi(['ima:scanTaskDetail:loopholeExport'])" type="primary" icon="el-icon-download" @click="onClickExport('all')">镜像漏洞全部导出</el-button>
      <el-button v-if="$checkPermi(['ima:scanTaskDetail:loopholeExport'])" type="primary" icon="el-icon-download" :disabled="!ids.length" @click="onClickExport()">镜像漏洞批量导出</el-button>
    </div>

    <!-- <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['ima:scanTaskDetail:add']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['ima:scanTaskDetail:edit']"
          >修改</el-button
        >
      </el-col>
      <el-col :span="1.5">

      </el-col>
      <el-col :span="1.5">

      </el-col>
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-upload2"
          size="mini"
          @click="handleImport"
          v-hasPermi="['ima:scanTaskDetail:export']"
          >导入</el-button
        >
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row> -->

    <el-table v-loading="loading" class="sa-table" :data="list">
      <el-table-column width="100" align="center">
        <template slot="header" slot-scope="scope">
          <el-checkbox
            :value="isSelectAll"
            :indeterminate="isIndeterminate"
            @change="onSelectAll"
          />
        </template>
        <template slot-scope="scope">
          <el-checkbox
            :value="ids.includes(scope.row.id)"
            @change="onSelect($event, scope.row)"
          />
        </template>
      </el-table-column>
      <el-table-column label="镜像名称" align="center" prop="imageName" />
      <el-table-column label="镜像类型" align="center">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.image_platform_type" :value="scope.row.platformType" />
        </template>
      </el-table-column>
      <el-table-column label="镜像地址" align="center" prop="finallyPushUrl" min-width="120">
        <template v-if="$checkPermi(['ima:info:imageUrl'])" slot-scope="scope">
          <template v-if="scope.row.platformType == 1">
            <span
              v-if="scope.row.finallyPushUrl"
              class="finally-push-url sa-table-line-1"
              @click="onOpenPushImaReName(scope.row, 'finallyPushUrl')"
            >
              {{ scope.row.finallyPushUrl }}
            </span>
            <el-button
              v-else
              size="mini"
              type="text"
              @click="onOpenPushImaReName(scope.row, 'finallyPushUrl')"
            >
              重命名
            </el-button>
          </template>
          <template v-if="scope.row.platformType == 2">
            <sa-tooltip :content="scope.row.finallyPushUrl" :is-copy="true" />
          </template>
        </template>
      </el-table-column>
      <el-table-column label="构成镜像地址" align="center" prop="pushUrl" min-width="120">
        <template v-if="$checkPermi(['ima:info:imageUrl'])" slot-scope="scope">
          <template v-if="scope.row.platformType == 1">/</template>
          <template v-if="scope.row.platformType == 2">
            <span
              v-if="scope.row.pushUrl"
              class="push-url sa-table-line-1"
              @click="onOpenPushImaReName(scope.row, 'pushUrl')"
            >
              {{ scope.row.pushUrl }}
            </span>
            <el-button
              v-else
              size="mini"
              type="text"
              @click="onOpenPushImaReName(scope.row, 'pushUrl')"
            >
              重命名
            </el-button>
          </template>
        </template>
      </el-table-column>
      <el-table-column label="备份地址" align="center" min-width="120">
        <template slot-scope="scope">
          <sa-tooltip :content="scope.row.backUrl" />
        </template>
      </el-table-column>
      <el-table-column label="平台类型" align="center" prop="imageType" />
      <el-table-column label="风险级别" align="center">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.ima_risk_level" :value="scope.row.level" />
        </template>
      </el-table-column>
      <el-table-column label="镜像扫描分数" align="center" prop="scanScore" min-width="120" />
      <!-- <el-table-column label="修复建议" align="center" prop="repairSuggest" /> -->
      <el-table-column label="环境建议" align="center">
        <template slot-scope="scope">
          <sa-tooltip :content="scope.row.adviceEnv" :is-copy="true" />
        </template>
      </el-table-column>
      <el-table-column label="文件建议" align="center">
        <template slot-scope="scope">
          <sa-tooltip :content="scope.row.adviceFile" :is-copy="true" />
        </template>
      </el-table-column>
      <el-table-column label="软件建议" align="center" min-width="200">
        <template slot-scope="scope">
          <sa-tooltip :content="scope.row.advicePkg" :is-copy="true" />
        </template>
      </el-table-column>
      <el-table-column label="漏洞数" align="center" prop="loopholeNum" />
      <el-table-column label="构建状态" align="center">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.image_build_status" :value="scope.row.buildStatus" />
        </template>
      </el-table-column>
      <el-table-column label="构建结果" align="center">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.image_build_result" :value="scope.row.buildResult" />
        </template>
      </el-table-column>
      <!-- <el-table-column label="扫描结果" align="center">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.scan_result" :value="scope.row.scanResult" />
        </template>
      </el-table-column> -->
      <el-table-column label="扫描状态" align="center">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.scan_status" :value="scope.row.scanStatus" />
        </template>
      </el-table-column>
      <el-table-column label="上次构建地址" align="center" min-width="120">
        <template slot-scope="scope">
          <sa-tooltip :content="scope.row.pzLastUrl" :is-copy="true" />
        </template>
      </el-table-column>
      <el-table-column label="单镜像推送状态" align="center" min-width="120">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.is_push_pz" :value="scope.row.isPushPz" />
        </template>
      </el-table-column>
      <el-table-column label="合并推送状态" align="center" min-width="120">
        <template slot-scope="scope">
          {{ scope.row.isManifestPush == 1 ? '未推送' : '已推送' }}
        </template>
      </el-table-column>
      <el-table-column label="备注" align="center" prop="remarks" />
      <el-table-column label="创建时间" align="center" prop="createTime" />
      <el-table-column label="操作" align="center" fixed="right" min-width="200">
        <template slot-scope="scope">
          <el-button
            v-if="$checkPermi(['risk:mirror:management:mirror:edit:dockerfile'])"
            size="mini"
            type="text"
            @click="onReadDockerfileContent(scope.row)"
          >编辑dockerfile</el-button>

          <el-button
            v-if="$checkPermi(['risk:mirror:management:mirror:construct:scan'])"
            size="mini"
            type="text"
            @click="onBuildSan(scope.row.id)"
          >
            构建并扫描
          </el-button>

          <el-button
            v-if="$checkPermi(['res:loophole:list'])"
            size="mini"
            type="text"
            @click="onLoophole(scope.row.id)"
          >漏洞列表</el-button>
          <!-- <el-button size="mini" type="text" @click="onLog(scope.row)">构建记录</el-button> -->

          <el-button
            v-if="$checkPermi(['risk:mirror:management:mirror:push'])"
            size="mini"
            type="text"
            @click="onPushImaPz(scope.row)"
          >推送重命名镜像</el-button>
          <!--          <el-button size="mini" type="text" @click="onPushFinallyImaPz(scope.row)"-->
          <!--            >推送最终镜像</el-button-->
          <!--          >-->

          <el-button
            v-if="$checkPermi(['risk:mirror:management:mirror:log'])"
            size="mini"
            type="text"
            @click="handleLog(scope.row)"
          >查看日志</el-button>

          <!-- <el-button
            v-if="$checkPermi(['risk:mirror:management:mirror:errorLog'])"
            size="mini"
            type="text"
            @click="onErrorLog(scope.row)"
            >错误日志</el-button
          > -->

          <el-button
            v-if="$checkPermi(['risk:mirror:management:mirror:upload:dependency'])"
            size="mini"
            type="text"
            @click="dockerfileData.depend.visible = true"
          >上传依赖包</el-button>
          <el-button
            v-if="$checkPermi(['risk:mirror:management:mirror:backup:copy'])"
            size="mini"
            type="text"
            @click="onOpenBackImageInfo(scope.row)"
          >备份镜像</el-button>
        </template>
      </el-table-column>
    </el-table>

    <div class="sa-footer sa-row-center">
      <sa-pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </div>

    <!-- 添加或修改镜像扫描流水详情对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="任务id" prop="taskId">
          <el-input v-model="form.taskId" placeholder="请输入任务id" />
        </el-form-item>
        <el-form-item label="镜像id" prop="imageId">
          <el-input v-model="form.imageId" placeholder="请输入镜像id" />
        </el-form-item>
        <el-form-item label="镜像名称" prop="imageName">
          <el-input v-model="form.imageName" placeholder="请输入镜像名称" />
        </el-form-item>
        <el-form-item label="镜像地址" prop="finallyPushUrl">
          <el-input v-model="form.finallyPushUrl" placeholder="请输入镜像地址" />
        </el-form-item>
        <el-form-item label="镜像扫描分数" prop="scanScore">
          <el-input v-model="form.scanScore" placeholder="请输入镜像扫描分数" />
        </el-form-item>
        <!-- <el-form-item label="修复建议" prop="repairSuggest">
          <el-input v-model="form.repairSuggest" type="textarea" placeholder="请输入内容" />
        </el-form-item> -->
        <el-form-item label="漏洞数" prop="loopholeNum">
          <el-input v-model="form.loopholeNum" placeholder="请输入漏洞数" />
        </el-form-item>
        <el-form-item label="编辑后的dockerfile" prop="dockerfileUrl">
          <el-input v-model="form.dockerfileUrl" placeholder="请输入编辑后的dockerfile" />
        </el-form-item>
        <el-form-item label="截图报告" prop="screenshotReportUrl">
          <el-input v-model="form.screenshotReportUrl" placeholder="请输入截图报告" />
        </el-form-item>
        <el-form-item label="构建结果：0：失败 1：成功" prop="buildResult">
          <el-input v-model="form.buildResult" placeholder="请输入构建结果：0：失败 1：成功" />
        </el-form-item>
        <el-form-item label="扫描结果  0 ：不存在问题  1：存在问题" prop="scanResult">
          <el-input
            v-model="form.scanResult"
            placeholder="请输入扫描结果  0 ：不存在问题  1：存在问题"
          />
        </el-form-item>
        <el-form-item label="修复结果: 0：失败 1: 成功  2:不可修复" prop="repairResult">
          <el-input
            v-model="form.repairResult"
            placeholder="请输入修复结果: 0：失败 1: 成功  2:不可修复"
          />
        </el-form-item>
        <el-form-item label="创建人" prop="createUser">
          <el-input v-model="form.createUser" placeholder="请输入创建人" />
        </el-form-item>
        <el-form-item label="创建时间" prop="createTime">
          <el-date-picker
            v-model="form.createTime"
            clearable
            size="small"
            type="datetime"
            value-format="yyyy-MM-dd HH:mm:ss"
            placeholder="选择创建时间"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
        <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
      </div>
    </el-dialog>
    <!-- 导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload" />
        <div class="el-upload__text">
          将文件拖到此处，或
          <em>点击上传</em>
        </div>
        <div slot="tip" class="el-upload__tip">
          <el-link
            type="info"
            style="font-size: 14px; color: green"
            @click="importTemplate"
          >点击下载模板</el-link>
        </div>
        <div
          slot="tip"
          class="el-upload__tip"
          style="color: red"
        >提示：仅允许导入“xls”或“xlsx”格式文件！</div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button @click="upload.open = false">取 消</el-button>
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
      </div>
    </el-dialog>

    <el-dialog
      title="dockerfile文件"
      :visible.sync="dockerfileData.visible"
      width="400px"
      append-to-body
      :close-on-click-modal="false"
    >
      <editor v-model="dockerfileData.str" :min-height="192" />
      <div slot="footer" class="dialog-footer">
        <el-button @click="dockerfileData.visible = false">取 消</el-button>
        <el-button type="primary" @click="onEditDockerfile">确 定</el-button>
      </div>
    </el-dialog>

    <!-- <log ref="logRef" :logData="logData" /> -->

    <el-dialog
      v-if="pushImaReNameData.visible"
      class="push-ima-reName-dialog"
      :title="pushImaReNameData.type == 'pushUrl' ? '构成镜像地址重命名' : '镜像地址重命名'"
      :visible.sync="pushImaReNameData.visible"
      width="500px"
      append-to-body
    >
      <el-form
        ref="form"
        :model="pushImaReNameData.form"
        :rules="pushImaReNameData.rules"
        label-width="120px"
      >
        <el-form-item :label="pushImaReNameData.type == 'pushUrl' ? '构成镜像地址' : '镜像地址'">
          <span class="url" @click="useClip(`${pushImaReNameData.url}`)">
            {{ pushImaReNameData.url }}
          </span>
        </el-form-item>
        <el-form-item label="仓库名" prop="repository">
          <el-input v-model="pushImaReNameData.form.repository" placeholder="请输入仓库名" />
        </el-form-item>
        <el-form-item label="Tag" prop="tag">
          <el-input v-model="pushImaReNameData.form.tag" placeholder="请输入Tag" />
        </el-form-item>
        <!--        <el-form-item label="是否推送" prop="isPush">-->
        <!--          <el-radio-group v-model="pushImaReNameData.form.isPush">-->
        <!--            <el-radio :label="false">否</el-radio>-->
        <!--            <el-radio :label="true">是</el-radio>-->
        <!--          </el-radio-group>-->
        <!--        </el-form-item>-->
      </el-form>
      <div v-if="pushImaReNameData.loading" class="dialog-loading">
        <i class="el-icon-loading" />
        <span class="text">重命名中</span>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="pushImaReNameData.visible = false">取 消</el-button>
        <el-button type="primary" @click="onPushImaReName">确 定</el-button>
      </div>
    </el-dialog>

    <el-dialog
      v-if="errorLogData.visible"
      title="错误日志"
      :visible.sync="errorLogData.visible"
      width="500px"
      append-to-body
    >
      {{ errorLogData.form.buildErrorMsg }}
    </el-dialog>

    <el-dialog
      v-if="dockerfileData.depend.visible"
      title="上传依赖包"
      :visible.sync="dockerfileData.depend.visible"
      width="400px"
      append-to-body
    >
      <el-upload
        ref="dependUpload"
        class="depend-upload"
        :limit="1"
        :headers="upload.headers"
        :action="`${baseApi}/ima/info/uploadImaRelyOn`"
        :on-progress="onProgressDependUpload"
        :on-success="onSuccessDependUpload"
        :auto-upload="false"
      >
        <el-button type="primary">选择文件</el-button>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="onConfirmDependUpload">确 定</el-button>
      </div>
    </el-dialog>

    <el-dialog
      v-if="backImageInfoData.visible"
      class="push-ima-reName-dialog"
      title="备份镜像"
      :visible.sync="backImageInfoData.visible"
      width="500px"
      append-to-body
    >
      <el-form
        ref="form"
        :model="backImageInfoData.form"
        :rules="backImageInfoData.rules"
        label-width="120px"
      >
        <el-form-item label="备份方式">
          <el-radio-group v-model="backImageInfoData.form.backType">
            <el-radio label="1">默认方式</el-radio>
            <el-radio label="2">自定义方式</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="['2'].includes(backImageInfoData.form.backType)" label="仓库名" prop="repository">
          <el-input v-model="backImageInfoData.form.repository" placeholder="请输入仓库名" />
        </el-form-item>
        <el-form-item v-if="['2'].includes(backImageInfoData.form.backType)" label="Tag" prop="tag">
          <el-input v-model="backImageInfoData.form.tag" placeholder="请输入Tag" />
        </el-form-item>
        <!-- <el-form-item label="平台类型" prop="platformType">
          <el-radio-group v-model="backImageInfoData.form.platformType" disabled>
            <el-radio
              v-for="dict in dict.type.image_platform_type"
              :key="dict.value"
              :label="dict.value"
              >{{ dict.label }}</el-radio
            >
          </el-radio-group>
        </el-form-item> -->
        <!-- <el-form-item
          v-if="backImageInfoData.form.platformType == 2"
          label="镜像类型"
          prop="imageType"
        >
          <el-radio-group v-model="backImageInfoData.form.imageType">
            <el-radio label="amd">amd</el-radio>
            <el-radio label="arm">arm</el-radio>
            <el-radio label="all">全部</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备份方式">
          <el-radio-group v-model="backImageInfoData.form.backType">
            <el-radio label="1">默认方式</el-radio>
            <el-radio label="2">自定义方式</el-radio>
          </el-radio-group>
        </el-form-item>
        <template v-if="backImageInfoData.form.backType == 2">
          <template
            v-if="
              backImageInfoData.form.imageType == 'amd' || backImageInfoData.form.imageType == 'all'
            "
          >
            <el-form-item label="amd 仓库名" prop="repository">
              <el-input
                v-model="backImageInfoData.form.repository"
                placeholder="请输入amd 仓库名"
              />
            </el-form-item>
            <el-form-item label="amd Tag" prop="tag">
              <el-input v-model="backImageInfoData.form.tag" placeholder="请输入amd Tag" />
            </el-form-item>
          </template>
          <template
            v-if="
              backImageInfoData.form.imageType == 'arm' || backImageInfoData.form.imageType == 'all'
            "
          >
            <el-form-item label="arm 仓库名" prop="armRepository">
              <el-input
                v-model="backImageInfoData.form.armRepository"
                placeholder="请输入arm 仓库名"
              />
            </el-form-item>
            <el-form-item label="arm Tag" prop="armTag">
              <el-input v-model="backImageInfoData.form.armTag" placeholder="请输入arm Tag" />
            </el-form-item>
          </template>
        </template> -->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="backImageInfoData.visible = false">取 消</el-button>
        <el-button type="primary" @click="onBackImageInfo">确 定</el-button>
      </div>
    </el-dialog>

    <BuildScanDialog ref="buildScanDialogRef" @success="getList" />

    <LogDialog ref="logDialogRef" />
  </div>
</template>

<script>
import {
  addScanTaskDetail,
  delScanTaskDetail,
  exportScanTaskDetail,
  getScanTaskDetail,
  listScanTaskDetail,
  updateScanTaskDetail
} from '@/api/ima/scanTaskDetail'
import { getToken } from '@/utils/auth'
import request from '@/utils/request'
import Log from './log.vue'
import useClip from '@/utils/clipboard'

import BuildScanDialog from './components/BuildScanDialog/index.vue'

import LogDialog from './components/LogDialog/index.vue'

export default {
  // name: 'ScanTaskDetail',
  components: {
    Log,
    BuildScanDialog,
    LogDialog
  },
  dicts: [
    'image_build_status',
    'scan_result',
    'image_build_result',
    'scan_status',
    'image_platform_type',
    'ima_risk_level',
    'is_push_pz'
  ],
  data() {
    return {
      baseApi: process.env.VUE_APP_BASE_API,
      // 按钮loading
      buttonLoading: false,
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 镜像扫描流水详情表格数据
      list: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 导入参数
      upload: {
        // 是否显示弹出层
        open: false,
        // 弹出层标题
        title: '',
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: { Authorization: 'Bearer ' + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + '/ima/scanTaskDetail/import'
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        taskId: this.$route.query.taskId,

        imageId: undefined,

        imageName: undefined,

        imageUrl: undefined,

        scanScore: undefined,

        // repairSuggest: undefined,

        loopholeNum: undefined,

        dockerfileUrl: undefined,

        screenshotReportUrl: undefined,

        buildStatus: undefined,

        scanResult: undefined,

        repairResult: undefined,

        createUser: undefined,

        pushUrl: undefined,

        finallyPushUrl: undefined,

        buildResult: void 0,
        scanStatus: void 0,
        isPushPz: void 0,
        isManifestPush: void 0
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        taskId: [{ required: true, message: '任务id不能为空', trigger: 'blur' }]
      },

      scanScore: '',
      daterange: [],

      dockerfileData: {
        visible: false,
        imageId: '',
        str: '',
        depend: {
          visible: false
        }
      },

      logData: {
        id: null
      },

      indexTimeId: null,

      selectedItem: [],
      isSelectAll: false,
      isIndeterminate: false,

      pushImaReNameData: {
        visible: false,
        loading: false,
        type: '',
        url: '',
        form: {
          repository: '',
          tag: ''
        },
        rules: {}
      },

      errorLogData: {
        visible: false,
        form: {}
      },

      backImageInfoData: {
        visible: false,
        loading: false,
        form: {},
        rules: {}
      }
    }
  },
  created() {
    this.getList()
    this.indexTimeId = setInterval(() => {
      this.getList(0)
    }, 10000)
  },
  destroyed() {
    clearInterval(this.indexTimeId)
  },
  methods: {
    handleLog(row) {
      this.$refs.logDialogRef.open({ params: row })
    },
    /** 查询镜像扫描流水详情列表 */
    getList(loading) {
      this.loading = loading !== 0
      if (this.scanScore) {
        this.queryParams.startScanScore = this.scanScore.split('-')[0]
        this.queryParams.endScanScore = this.scanScore.split('-')[1]
      } else {
        delete this.queryParams.startScanScore
        delete this.queryParams.endScanScore
      }
      listScanTaskDetail(this.queryParams).then((response) => {
        this.list = response.rows
        this.total = response.total

        this.calculateSelect()

        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        id: undefined,

        taskId: undefined,

        imageId: undefined,

        imageName: undefined,

        imageUrl: undefined,

        scanScore: undefined,

        // repairSuggest: undefined,

        loopholeNum: undefined,

        dockerfileUrl: undefined,

        screenshotReportUrl: undefined,

        buildStatus: 0,

        buildResult: undefined,

        scanResult: undefined,

        repairResult: undefined,

        createUser: undefined,

        createTime: undefined
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.scanScore = ''
      this.daterange = []
      this.onChangeDaterange()
      this.handleQuery()
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = '添加镜像扫描流水详情'
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.loading = true
      this.reset()
      const id = row.id || this.ids
      getScanTaskDetail(id).then((response) => {
        this.loading = false
        this.form = response.data
        this.open = true
        this.title = '修改镜像扫描流水详情'
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          this.buttonLoading = true
          if (this.form.id != null) {
            updateScanTaskDetail(this.form)
              .then((response) => {
                this.$modal.msgSuccess('修改成功')
                this.open = false
                this.getList()
              })
              .finally(() => {
                this.buttonLoading = false
              })
          } else {
            addScanTaskDetail(this.form)
              .then((response) => {
                this.$modal.msgSuccess('新增成功')
                this.open = false
                this.getList()
              })
              .finally(() => {
                this.buttonLoading = false
              })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids
      this.$confirm('是否确认删除当前所选数据项？', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.loading = true
          return delScanTaskDetail(ids)
        })
        .then(() => {
          this.loading = false
          this.getList()
          this.$modal.msgSuccess('删除成功')
        })
        .catch(() => {})
    },
    /** 导出按钮操作 */
    handleExport() {
      if (this.total > 200000) {
        this.$modal.msgWarning('抱歉，导出数据量过大，请选择筛选条件后再进行导出')
        return false
      }

      const submitAuery = JSON.parse(JSON.stringify(this.queryParams))
      delete submitAuery.pageNum
      delete submitAuery.pageSize

      this.download(
        '/ima/scanTaskDetail/export',
        {
          ...submitAuery
        },
        `导出台账明细_${new Date().getTime()}.xlsx`
      )
      // const queryParams = this.queryParams;
      // this.$confirm('是否确认导出所有镜像扫描流水详情数据项?', '警告', {
      //   confirmButtonText: '确定',
      //   cancelButtonText: '取消',
      //   type: 'warning',
      // })
      //   .then(() => {
      //     this.exportLoading = true;
      //     return exportScanTaskDetail(queryParams);
      //   })
      //   .then((response) => {
      //     this.download(response.msg);
      //     this.exportLoading = false;
      //   })
      //   .catch(() => {});
    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = '镜像扫描流水详情导入'
      this.upload.open = true
    },
    /** 下载模板操作 */
    importTemplate() {
      importTemplate().then((response) => {
        this.download(response.msg)
      })
    },
    /** 文件上传中处理*/
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true
    },
    /** 文件上传成功处理*/
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false
      this.upload.isUploading = false
      this.$refs.upload.clearFiles()
      this.$alert(response.msg, '导入结果', { dangerouslyUseHTMLString: true })
      this.getList()
    },
    /** 提交上传文件*/
    submitFileForm() {
      this.$refs.upload.submit()
    },
    onChangeDaterange() {
      if (this.daterange && this.daterange.length > 0) {
        this.queryParams.startTime = this.daterange[0]
        this.queryParams.endTime = this.daterange[1]
      } else {
        delete this.queryParams.startTime
        delete this.queryParams.endTime
      }
    },
    onBatchSan() {
      this.$refs.buildScanDialogRef.open({ params: { ids: this.ids }})

      // request({
      //   url: '/ima/scanTaskDetail/batch/san',
      //   method: 'get',
      //   params: {
      //     ids: this.ids.join(','),
      //   },
      // }).then((response) => {
      //   if (response.code == 200) {
      //     this.$modal.msgSuccess(response.msg);
      //     this.getList();
      //   }
      // });
    },
    onBuildSan(id) {
      this.$refs.buildScanDialogRef.open({ params: { id }})

      // request({
      //   url: '/ima/scanTaskDetail/build/san',
      //   method: 'get',
      //   params: {
      //     imageDetailId: id,
      //   },
      // }).then((response) => {
      //   this.getList();
      // });
    },

    onLoophole(id) {
      this.$router.push({ path: '/goldIma/loophole', query: { id }})
    },
    onLog(row) {
      this.logData.taskId = row.taskId
      this.logData.imageId = row.imageId
      this.$refs.logRef.show()
    },
    onReadDockerfileContent(row) {
      this.dockerfileData.imageId = row.id
      request({
        url: '/ima/info/readDockerfileContent',
        method: 'post',
        data: {
          fileUrl: row.dockerfileUrl
        }
      }).then((response) => {
        this.dockerfileData.visible = true
        this.dockerfileData.str = response.data
      })
    },
    onEditDockerfile() {
      const editContent = this.dockerfileData.str
        .replace(/<p>/g, '')
        .replace(new RegExp('</p>', 'g'), '\n')
      request({
        url: '/ima/info/editTaskDetailFileContent',
        method: 'post',
        data: {
          id: this.dockerfileData.imageId,
          editContent: editContent
        }
      }).then((response) => {
        this.dockerfileData.visible = false
        this.$modal.msgSuccess(response.msg)
        this.getList()
      })
    },
    onPushImaPz(row) {
      if (!row) {
        // 合并推送
        let flag = false
        let flag2 = false
        let finallyPushUrl
        let amdPushUrl
        let armPushUrl
        if (this.selectedItem.length > 0) {
          finallyPushUrl = this.selectedItem[0].finallyPushUrl
          flag = this.selectedItem.every((item) => item.finallyPushUrl == finallyPushUrl)

          flag2 = this.selectedItem.every((item) => item.isPushPz == 2)
        }

        if (!flag2) {
          this.$modal.msgError('请先推送单镜像后再执行合并推送操作！')
          return false
        }

        if (flag) {
          this.selectedItem.forEach((item) => {
            if (item.imageType == 'arm') {
              armPushUrl = item.pushUrl
            } else if (item.imageType == 'amd') {
              amdPushUrl = item.pushUrl
            }
          })

          request({
            url: '/ima/scanTaskDetail/dualPlatformManifestPush',
            method: 'post',
            data: {
              ids: this.ids,
              finallyPushUrl,
              amdPushUrl,
              armPushUrl
            }
          }).then((response) => {
            this.$modal.msgSuccess(response.msg)
            this.ids = []
            this.getList()
          })
        } else {
          this.$modal.msgError('合并地址不一致，请检查！')
        }
      } else {
        request({
          url: '/ima/scanTaskDetail/pushImaPz',
          method: 'get',
          params: {
            id: row.id,
            pushUrl: row.pushUrl
          }
        }).then((response) => {
          this.$modal.msgSuccess(response.msg)
          this.getList()
        })
      }
    },
    onPushFinallyImaPz(row) {
      request({
        url: '/ima/scanTaskDetail/pushFinallyImaPz',
        method: 'get',
        params: {
          id: row.id,
          finallyPushUrl: row.finallyPushUrl
        }
      }).then((response) => {
        this.$modal.msgSuccess(response.msg)
        this.getList()
      })
    },
    onOpenPushImaReName(row, type) {
      this.pushImaReNameData.visible = true
      this.pushImaReNameData.loading = false
      this.pushImaReNameData.type = type
      this.pushImaReNameData.url = row[type]
      this.pushImaReNameData.form = {
        id: row.id,
        repository: '',
        tag: ''
      }
      if (type == 'pushUrl') {
        this.pushImaReNameData.form.isPush = false
      }
    },
    onPushImaReName() {
      this.pushImaReNameData.loading = true
      request({
        url:
            this.pushImaReNameData.type == 'pushUrl'
              ? '/ima/scanTaskDetail/pushImaReName'
              : '/ima/scanTaskDetail/finallyPushImaReName',
        method: 'get',
        params: this.pushImaReNameData.form
      }).then((response) => {
        this.$modal.msgSuccess(response.msg)
        this.pushImaReNameData.loading = false
        this.pushImaReNameData.visible = false
        this.getList()
      })
    },
    useClip,
    onSelect(type, row) {
      if (type) {
        this.ids.push(row.id)
        this.selectedItem.push(row)
      } else {
        const findIndex = this.ids.findIndex((id) => id == row.id)
        this.ids.splice(findIndex, 1)
        this.selectedItem.splice(findIndex, 1)
      }
      this.calculateSelect()
    },
    onSelectAll(type) {
      if (type) {
        this.list.forEach((item) => {
          this.ids.push(item.id)
          this.selectedItem.push(item)
        })
        this.ids = Array.from(new Set(this.ids))
      } else {
        this.list.forEach((item) => {
          if (this.ids.findIndex((id) => id == item.id) !== -1) {
            this.ids.splice(
              this.ids.findIndex((id) => id == item.id),
              1
            )
            this.selectedItem.splice(
              this.selectedItem.findIndex((i) => i.flagArr == item.flagArr),
              1
            )
          }
        })
      }
      this.calculateSelect()
    },
    calculateSelect() {
      this.isSelectAll = false
      this.isIndeterminate = false
      if (this.list.every((item) => this.ids.includes(item.id))) {
        this.isSelectAll = true
        this.isIndeterminate = false
      } else if (this.list.some((item) => this.ids.includes(item.id))) {
        this.isSelectAll = false
        this.isIndeterminate = true
      }

      if (this.list.length === 0) {
        this.isSelectAll = false
        this.isIndeterminate = false
      }
    },
    onErrorLog(row) {
      this.errorLogData.form = row
      this.errorLogData.visible = true
    },
    onProgressDependUpload() {
      // this.upload.isUploading = true;
    },
    onSuccessDependUpload(response) {
      this.dockerfileData.depend.visible = false
        this.$refs.dependUpload?.clearFiles()
        if (response.code === 200) {
          this.$modal.msgSuccess('操作成功')
          this.getList()
        } else {
          this.$modal.msgError(response.msg)
        }
    },
    onConfirmDependUpload() {
      if (this.$refs.dependUpload.uploadFiles.length > 0) {
          this.$refs.dependUpload?.submit()
      } else {
        this.dockerfileData.depend.visible = false
          this.$refs.dependUpload?.clearFiles()
      }
    },
    onOpenBackImageInfo(row) {
      this.backImageInfoData.visible = true
      this.backupLoading?.close?.()

      this.backImageInfoData.form = {
        id: row.imageId,
        backType: '1',
        platformType: row.platformType,
        imageType: row.imageType,
        repository: '',
        tag: ''
        // platformType: row.platformType,
        // armRepository: '',
        // armTag: '',
      }
    },
    onBackImageInfo(row) {
      this.backupLoading = this.$loading({
        text: '备份中'
      })

      const params = {
        ...this.backImageInfoData.form
      }

      if (params.imageType === 'arm') {
        params.armRepository = params.repository
        params.armTag = params.tag
        delete params.repository
        delete params.tag
      }

      request({
        url: '/ima/info/backImageInfo',
        method: 'get',
        timeout: 80000,
        params
      }).then((response) => {
        this.$modal.msgSuccess(response.msg)
        this.backImageInfoData.visible = false
        this.getList()
      }).finally(() => {
        this.backupLoading.close()
      })
    },
    onClickExport(type) {
      const params = {
        taskId: this.$route.query.taskId
      }

      if (type === 'all') {
        Object.assign(params, {
          isAll: 'all'
        })
      } else {
        Object.assign(params, {
          taskDetailIds: this.ids
        })
      }

      this.download('/ima/info/imageLoopholeExportInTask', params, `导出_镜像漏洞_${Date.now()}.xlsx`, {
        method: 'post',
        headers: { 'Content-Type': 'application/json;charset=utf-8' }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
  .push-url,
  .finally-push-url,
  .url {
    color: rgba(var(--primary-color), 1);
    cursor: pointer;
  }
</style>

<style lang="scss">
  .push-ima-reName-dialog {
    .el-dialog__body {
      position: relative;
      .dialog-loading {
        position: absolute;
        width: 200px;
        height: 200px;
        background: #eee;
        top: 50%;
        right: 50%;
        margin-top: -100px;
        margin-right: -100px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        .text {
          position: absolute;
        }
        .el-icon-loading {
          font-size: 150px;
        }
      }
    }
  }
</style>
