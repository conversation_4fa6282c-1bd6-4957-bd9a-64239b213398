<template>
  <EleSheet ref="sheetRef" v-bind="sheetProps" class="page-main">
    <template #before />
    <template #after />

    <!-- <template #search:deptName:simple="{ model }">
      <DeptTreeSelect
        v-model="model.deptId"
        placeholder="请选择"
        @input="(value) => onDepTreeInput(value, model)"
        @select="(event) => onDeptTreeSelect(event, model)"
      ></DeptTreeSelect>
    </template>
    <template #search:sysName:simple="{ model }">
      <SystemSelect
        v-model="model.sysId"
        :deptId="model.deptId"
        @change="(value, label) => onSystemSelect(value, label, model)"
        placeholder="请选择"
      />
    </template> -->
  </EleSheet>
</template>

<script>
// import request from '@/utils/request.js';

import {
  listMateriel,
  getMateriel,
  addMateriel,
  updateMateriel,
  delMateriel
} from '@/api/ima/materiel.js'

export default {
  data() {
    return {}
  },
  computed: {
    params() {
      return this.$attrs.params || this.$route.query
    },
    sheetProps() {
      const value = {
        title: '物料信息',

        lazy: false,

        hiddenActions: {
          export: !this.$checkPermi(['material:library:export'])
        },

        api: {
          // add: (params) => addMateriel({ ...params }),
          // edit: (params) => updateMateriel({ ...params }),
          // remove: delMateriel,

          list: async(params) => {
            const res = await listMateriel({
              ...this.params,
              ...params
            })

            return res
          },
          info: getMateriel,
          export: (handler) => {
            handler('/risk/imageMaterial/export', {
              parameter: (params) => ({
                ...this.params,
                ...params
              })
            })
          },
          import: '',
          template: ''
        },

        infoProps: {
          title: true
        },

        model: {
          compName: { label: '组件名称', type: 'text', width: 200 },
          compVersion: { label: '组件版本', type: 'text', width: 150 },
          moduleName: { label: '模块名称', type: 'text', width: 200, search: { hidden: true }},
          moduleVersion: {
            label: '模块版本',
            type: 'text',
            width: 150,
            search: { hidden: true }
          },
          moduleLanguages: {
            label: '模块语言',
            type: 'text',
            width: 150,
            search: { hidden: true }
          },
          moduleKind: { label: '模块种类', type: 'text', width: 150, search: { hidden: true }},
          moduleAssociatedFilePath: {
            label: '模块路径',
            type: 'text',
            width: 150,
            search: { hidden: true }
          },
          repository: { label: '仓库', type: 'text', search: { hidden: true }}

          // createTime: {
          //   type: 'text',
          //   label: '创建时间',
          //   search: {
          //     type: 'date-range',
          //     parameter: (data) => {
          //       return {
          //         startTime: data?.[0],
          //         endTime: data?.[1],
          //       };
          //     },
          //   },
          //   form: {
          //     hidden: true,
          //   },
          //   width: 200,
          // },
        }
      }

      return value
    }
  },
  methods: {}
}
</script>

<style></style>
