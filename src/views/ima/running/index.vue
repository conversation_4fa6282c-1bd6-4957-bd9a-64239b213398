<template>
  <EleSheet ref="sheetRef" v-bind="sheetProps" class="page-main page-main--full">

    <template #search:deptName:simple="{ model }">
      <CommonDepartmentSelect
        v-model="model.deptName"
        placeholder="请输入"
        clearable
        return-name
        @change="() => (model.systemName = void 0)"
      />
    </template>

    <template #search:systemName:simple="{ model }">
      <CommonSystemSelect
        v-model="model.systemName"
        placeholder="请输入"
        return-name
        clearable
        :params="{
          deptName: model.deptName,
        }"
      />
    </template>

    <template #table:action:after="{ row }">
      <el-button v-if="$checkPermi(['risk:mirrorEffect:loophole'])" type="text" size="mini" @click="onLoopholeClick(row)">漏洞列表</el-button>
      <el-button v-if="$checkPermi(['risk:mirrorEffect:material'])" type="text" size="mini" @click="onMaterialClick(row)">物料列表</el-button>
    </template>

    <template #after>
      <LoopholeDialog ref="loopholeDialogRef" show-stats>
      </LoopholeDialog>
      <MaterialDialog ref="materialDialogRef" />
    </template>
  </EleSheet>
</template>

<script>
import request from '@/utils/request.js'
import LoopholeDialog from '@/views/risk/report/task/components/MirrorListDialog/index.vue'
import MaterialDialog from './components/MaterialDialog/index.vue'

export default {
  components: {
    LoopholeDialog,
    MaterialDialog
  },
  data() {
    return {}
  },
  computed: {
    params() {
      return {
        sourceType: 2
      }
    },
    sheetProps() {
      const value = {
        tableProps: {
          height: '100%'
        },
        api: {
          list: (params) => request({
            url: '/risk/riskBasicImageLadingData/list',
            method: 'get',
            params: {
              ...this.params,
              ...params
            }
          }),
          info: (id) => request({
            url: `/risk/riskBasicImageLadingData/${id}`,
            method: 'get'
          }),
          export: 'risk/riskBasicImageLadingData/export'
        },

        hiddenActions: {
          export: !this.$checkPermi(['run:image:library:export'])
        },
        model: {
          imageName: {
            label: '镜像名称',
            search: {},
            table: {
              width: 200,
              align: 'left'
            },
            form: {}
          },
          deptName: {
            label: '业务部门',
            search: {},
            table: {},
            form: {}
          },
          systemName: {
            label: '业务系统',
            search: {},
            table: {},
            form: {}
          },
          nodeIps: {
            label: '镜像所在资源IP',
            search: {},
            table: {},
            form: {}
          },
          imageUrl: {
            label: '镜像地址',
            search: {
              hidden: true
            },
            table: {},
            form: {}
          },

          imageHashId: {
            label: '镜像哈希Id',
            search: {
              hidden: true
            },
            table: {},
            form: {}
          },
          digest: {
            label: '镜像digest',
            search: {
              hidden: true
            },
            table: {},
            form: {}
          },
          createTime: {
            label: '创建时间',
            search: {
              hidden: false,
              type: 'date-range',
              parameter: (data) => {
                return {
                  startTime: data?.[0],
                  endTime: data?.[1]
                }
              }
            },
            table: {
              width: 200
            },
            form: {
              hidden: true
            }
          }
        }
      }

      return value
    }
  },
  methods: {
    onLoopholeClick(row) {
      this.$refs.loopholeDialogRef.open({
        params: {
          ...this.params,
          loopholeSource: 2,
          imageId: row.id,
          taskId: row.taskId
        }
      })
    },
    onMaterialClick(row) {
      this.$refs.materialDialogRef.open({
        params: {
          ...this.params,
          imageId: row.id
        }
      })
    }
  }
}
</script>

<style></style>
