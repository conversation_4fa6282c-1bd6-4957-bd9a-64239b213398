<template>
  <div class="page-main">
    <el-form
      v-show="showSearch"
      ref="queryForm"
      class="sa-query"
      :model="queryParams"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="镜像名称" prop="imageName">
        <el-input
          v-model="queryParams.imageName"
          placeholder="请输入镜像名称"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="平台类型" prop="platformType">
        <el-select
          v-model="queryParams.platformType"
          placeholder="请选择平台类型"
          clearable
          @change="handleQuery"
        >
          <el-option
            v-for="dict in dict.type.image_platform_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="镜像地址" prop="actualImageName">
        <el-input
          v-model="queryParams.actualImageName"
          placeholder="请输入镜像地址"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="构成镜像地址" prop="buildUrl">
        <el-input
          v-model="queryParams.buildUrl"
          placeholder="请输入构成镜像地址"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="更新状态" prop="updateStatus">
        <EleSelectDict v-model="queryParams.updateStatus" :options="dict.type.ima_update_status" />
      </el-form-item>
      <el-form-item class="query-handle">
        <el-button type="primary" plain icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh-left" @click="resetQuery">重置</el-button>
        <EleSheetSearchMore :parent-ref="()=> $refs.queryForm" />
      </el-form-item>
    </el-form>

    <div class="sa-title">
      <sa-toolbar :show-search.sync="showSearch" @queryTable="getList" />
      <el-button
        v-hasPermi="['ima:info:add']"
        type="primary"
        icon="el-icon-plus"
        @click="handleAdd"
      >新增
      </el-button>
      <!-- v-hasPermi="['ima:info:export']" -->
      <el-button
        v-hasPermi="['res:info:export']"
        type="primary"
        icon="el-icon-download"
        :loading="exportLoading"
        @click="handleExport"
      >导出
      </el-button>
      <el-button
        v-hasPermi="['ima:info:export']"
        type="primary"
        icon="el-icon-upload2"
        @click="handleImport"
      >导入
      </el-button>

      <el-button v-if="$checkPermi(['ima:scan:add'])" :disabled="ids.length == 0" type="primary" @click="onScanTask"><IconDocumentScannerOutlineRounded class="" />扫描</el-button>

      <el-button
        v-if="$checkPermi(['ima:batch:update'])"
        type="primary"
        :disabled="ids.length == 0"
        @click="onUpdateImportImageUrl(false)"
      ><IconUpdate class="" />批量更新
      </el-button>
      <el-button v-if="$checkPermi(['ima:complete:update'])" type="primary" @click="onUpdateImportImageUrl(true)"><IconSync class="" />全部更新</el-button>

      <el-button type="primary" icon="el-icon-view" :disabled="ids.length == 0" @click="onOpenSelected">查看勾选</el-button>

      <el-button
        v-hasPermi="['ima:info:remove']"
        type="danger"
        :disabled="ids.length == 0"
        @click="handleDelete"
      ><i class="iconfont icon-piliangshanchu" />批量删除
      </el-button>

      <LoopholeExport v-if="$checkPermi(['ima:info:exportLoophole'])" v-bind="{ ids }" :disabled="!ids.length" />
    </div>

    <el-table v-loading="loading" class="sa-table" :data="list">
      <el-table-column width="100" align="center">
        <template slot="header" slot-scope="scope">
          <el-checkbox
            :value="isSelectAll"
            :indeterminate="isIndeterminate"
            @change="onSelectAll"
          />
        </template>
        <template slot-scope="scope">
          <el-checkbox
            :value="ids.includes(scope.row.id)"
            @change="onSelect($event, scope.row)"
          />
        </template>
      </el-table-column>
      <el-table-column label="镜像名称" align="center" prop="imageName" min-width="160" />
      <el-table-column label="平台类型" align="center">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.image_platform_type" :value="scope.row.platformType" />
        </template>
      </el-table-column>
      <el-table-column label="镜像地址" align="center" min-width="160">
        <template slot-scope="scope">
          <sa-tooltip :content="scope.row.actualImageName" :is-copy="true" />
        </template>
      </el-table-column>
      <el-table-column label="构成镜像地址" align="center" min-width="120">
        <template slot-scope="scope">
          <template v-if="scope.row.platformType == 1">/</template>
          <template v-if="scope.row.platformType == 2">
            <sa-tooltip :content="scope.row.buildAmdUrl" :is-copy="true" />
            <sa-tooltip :content="scope.row.buildArmUrl" :is-copy="true" />
          </template>
        </template>
      </el-table-column>
      <el-table-column label="镜像大小" align="center" min-width="160">
        <template slot-scope="scope">
          <div v-if="scope.row.imageSize">
            <div v-for="item in scope.row.imageSize.split(',')">
              {{ item }}
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="备份镜像名称" align="center" min-width="120">
        <template slot-scope="scope">
          <div v-if="scope.row.backImageUrl">
            <template v-for="item in scope.row.backImageUrl.split(',')">
              <sa-tooltip :content="item" :is-copy="true" />
            </template>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="更新状态" align="center">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.ima_update_status" :value="scope.row.updateStatus" />
        </template>
      </el-table-column>
      <el-table-column v-if="$checkPermi(['ima:dockerfile'])" label="dockerfile地址" align="center" prop="dockerfileUrl" min-width="200">
        <template slot-scope="scope">
          <template v-if="scope.row.platformType == 1">
            <el-button
              v-if="scope.row.dockerfileUrl"
              size="mini"
              type="text"
              @click="onReadDockerfileContent(scope.row, 'amd')"
            >dockerfile
            </el-button>
            <el-upload
              v-if="!scope.row.dockerfileUrl"
              ref="upload"
              class="table-upload"
              :limit="1"
              :headers="upload.headers"
              :action="`${baseApi}/ima/info/upload?imageId=${scope.row.id}&platformType=1`"
              :disabled="upload.isUploading"
              :on-progress="onProgressInfoImport"
              :on-success="onSuccessInfoImport"
            >
              <el-button size="mini" type="text">上传</el-button>
            </el-upload>
          </template>
          <template v-if="scope.row.platformType == 2">
            <div>
              <el-button
                v-if="scope.row.dockerfileUrl"
                size="mini"
                type="text"
                @click="onReadDockerfileContent(scope.row, 'amd')"
              >amd平台dockerfile
              </el-button>
            </div>
            <div>
              <el-button
                v-if="scope.row.dockerfileArmUrl"
                size="mini"
                type="text"
                @click="onReadDockerfileContent(scope.row, 'arm')"
              >arm平台dockerfile
              </el-button>
            </div>
            <div>
              <el-button
                v-if="!scope.row.dockerfileUrl || !scope.row.dockerfileArmUrl"
                size="mini"
                type="text"
                @click="onUploaderDockerfileAmdArmUrl(scope.row)"
              >上传
              </el-button>
            </div>
          </template>
        </template>
      </el-table-column>
      <el-table-column label="导入方式" align="center">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.ima_import_type" :value="scope.row.generateType" />
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" min-width="180" />
      <el-table-column label="操作" align="center" fixed="right" min-width="200">
        <template #default="scope">
          <el-button v-if="$checkPermi(['ima:info:query'])" size="mini" type="text" @click="onDetail(scope.row)">详情</el-button>
          <el-button
            v-hasPermi="['ima:info:edit']"
            size="mini"
            type="text"
            @click="handleUpdate(scope.row)"
          >修改
          </el-button>
          <el-button
            v-hasPermi="['ima:info:remove']"
            type="text"
            size="mini"
            @click="handleDelete(scope.row)"
          >删除
          </el-button>
          <el-dropdown
            :key="scope.row.id"
            :tabindex="Number(scope.row.id)"
            @command="(command) => handleCommand(command, scope.row)"
          >
            <el-button class="sa-m-l-12" size="mini" type="text">更多</el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item v-if="$checkPermi(['ima:dockerfile:version:record'])" command="log"> dockerfile版本记录</el-dropdown-item>
              <el-dropdown-item v-if="scope.row.dockerfileUrl && $checkPermi(['ima:dockerfile:download'])" command="downloadDockerfile">
                下载dockerfile
              </el-dropdown-item>
              <el-dropdown-item v-if="$checkPermi(['ima:upload:dependency:package'])" command="uploadDockerfile">
                上传依赖包
              </el-dropdown-item>
              <el-dropdown-item v-if="$checkPermi(['ima:import:image'])" command="importImagePackageOrUrl"> 导入镜像</el-dropdown-item>
              <el-dropdown-item command="imageLog"> 镜像版本记录</el-dropdown-item>
              <el-dropdown-item v-if="$checkPermi(['ima:backups:image'])" command="backImageInfo"> 备份镜像</el-dropdown-item>
              <el-dropdown-item command="scanRecords"> 扫描记录</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>
    <div class="sa-footer sa-row-center">
      <sa-pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </div>

    <!-- 添加或修改镜像信息对话框 -->
    <el-dialog v-if="open" :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="formRef" :model="form" :rules="rules" label-width="140px">
        <el-form-item label="漏洞库环境" prop="loopholeLibrary">
          <el-select v-model="form.loopholeLibrary" placeholder="请选择漏洞库环境" clearable>
            <el-option
              v-for="dict in dict.type.loophole_library"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="镜像名称" prop="imageName">
          <el-input v-model="form.imageName" placeholder="请输入镜像名称" />
        </el-form-item>
        <el-form-item label="镜像地址" prop="actualImageName">
          <el-input v-model="form.actualImageName" placeholder="请输入镜像地址" />
        </el-form-item>
        <el-form-item label="平台类型" prop="platformType">
          <el-select v-model="form.platformType" placeholder="请选择平台类型" clearable>
            <el-option
              v-for="dict in dict.type.image_platform_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item v-if="form.platformType == 1" label="构成镜像地址" prop="buildAmdUrl">
          <el-input v-model="form.buildAmdUrl" placeholder="请输入构成镜像地址" />
        </el-form-item>
        <template v-if="form.platformType == 2">
          <el-form-item label="构成镜像amd地址" prop="buildAmdUrl">
            <el-input v-model="form.buildAmdUrl" placeholder="请输入构成镜像amd地址" />
          </el-form-item>
          <el-form-item label="构成镜像arm地址" prop="buildArmUrl">
            <el-input v-model="form.buildArmUrl" placeholder="请输入构成镜像arm地址" />
          </el-form-item>
        </template>
        <el-form-item label="原镜像地址" prop="initImageUrl">
          <el-input v-model="form.initImageUrl" placeholder="请输入原镜像地址" />
        </el-form-item>
        <el-form-item label="备注" prop="remarks">
          <el-input v-model="form.remarks" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
        <el-button type="primary" @click="submitForm">确 定</el-button>
      </div>
    </el-dialog>
    <!-- 导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload" />
        <div class="el-upload__text">
          将文件拖到此处，或
          <em>点击上传</em>
        </div>
        <div slot="tip" class="el-upload__tip">
          <el-link
            type="info"
            style="font-size: 14px; color: green"
            @click="importTemplate"
          >点击下载模板
          </el-link>
        </div>
        <div
          slot="tip"
          class="el-upload__tip"
          style="color: red"
        >提示：仅允许导入“xls”或“xlsx”格式文件！
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button @click="upload.open = false">取 消</el-button>
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
      </div>
    </el-dialog>

    <el-dialog
      v-if="importImagePackageOrUrlData.visible"
      title="导入镜像"
      :visible.sync="importImagePackageOrUrlData.visible"
      width="400px"
      append-to-body
    >
      <el-form
        ref="form"
        :model="importImagePackageOrUrlData.form"
        :rules="importImagePackageOrUrlData.rules"
        label-width="120px"
      >
        <!-- <el-form-item label="平台类型" prop="platformType">
          <el-radio-group v-model="importImagePackageOrUrlData.form.platformType" disabled>
            <el-radio
              v-for="dict in dict.type.image_platform_type"
              :key="dict.value"
              :label="dict.value"
              >{{ dict.label }}</el-radio
            >
          </el-radio-group>
        </el-form-item> -->
        <template v-if="importImagePackageOrUrlData.form.platformType == 2">
          <el-form-item label="镜像类别" prop="imageCategory">
            <el-radio-group
              v-model="importImagePackageOrUrlData.form.imageCategory"
              @change="onChangeImageCategory"
            >
              <el-radio label="1">单镜像</el-radio>
              <el-radio label="2">合并镜像</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item
            v-if="importImagePackageOrUrlData.form.imageCategory == 1"
            label="镜像类型"
            prop="imageType"
          >
            <el-radio-group v-model="importImagePackageOrUrlData.form.imageType">
              <el-radio label="amd">amd</el-radio>
              <el-radio label="arm">arm</el-radio>
            </el-radio-group>
          </el-form-item>
        </template>
        <el-form-item label="导入镜像" prop="generateType">
          <el-radio-group
            v-model="importImagePackageOrUrlData.form.generateType"
            @change="onChangeGenerateType"
          >
            <el-radio :label="0">镜像地址</el-radio>
            <el-radio :label="1">镜像包</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item
          v-if="importImagePackageOrUrlData.form.generateType == 0"
          label="镜像地址"
          prop="url"
        >
          <el-input v-model="importImagePackageOrUrlData.form.url" />
          <el-progress
            v-if="importImagePackageOrUrlData.progressPercentage"
            class="sa-m-t-8"
            :percentage="importImagePackageOrUrlData.progressPercentage"
            :stroke-width="4"
          />
        </el-form-item>
        <el-form-item v-if="importImagePackageOrUrlData.form.generateType == 1" label="镜像包">
          <el-upload
            ref="importImageRef"
            :limit="1"
            :headers="upload.headers"
            :action="`${baseApi}/ima/info/importImagePackageOrUrl?id=${importImagePackageOrUrlData.form.id}&platformType=${importImagePackageOrUrlData.form.platformType}&imageName=${importImagePackageOrUrlData.form.imageName}&generateType=${importImagePackageOrUrlData.form.generateType}&url=${importImagePackageOrUrlData.form.url}&imageCategory=${importImagePackageOrUrlData.form.imageCategory}&imageType=${importImagePackageOrUrlData.form.imageType}`"
            :on-progress="onProgressImportImage"
            :on-success="onSuccessImportImage"
            :auto-upload="false"
          >
            <el-button type="primary">上传</el-button>
          </el-upload>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="importImagePackageOrUrlData.visible = false">取 消</el-button>
        <el-button type="primary" @click="onImportImagePackageOrUrl">确 定</el-button>
      </div>
    </el-dialog>

    <el-dialog
      title="dockerfile文件"
      :visible.sync="dockerfileData.visible"
      width="400px"
      append-to-body
    >
      <editor v-model="dockerfileData.str" :min-height="192" o-type="info" />
      <div slot="footer" class="dialog-footer">
        <el-button @click="dockerfileData.visible = false">取 消</el-button>
        <el-button type="primary" @click="onEditDockerfile">确 定</el-button>
      </div>
    </el-dialog>

    <el-dialog
      v-if="dockerfileData.depend.visible"
      title="上传依赖包"
      :visible.sync="dockerfileData.depend.visible"
      width="400px"
      append-to-body
    >
      <el-upload
        ref="dependUpload"
        class="depend-upload"
        :limit="1"
        :headers="upload.headers"
        :action="`${baseApi}/ima/info/uploadImaRelyOn`"
        :on-progress="onProgressDependUpload"
        :on-success="onSuccessDependUpload"
        :auto-upload="false"
      >
        <el-button type="primary">选择文件</el-button>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="onConfirmDependUpload">确 定</el-button>
      </div>
    </el-dialog>

    <log ref="logRef" :log-data="logData" />

    <sa-detail ref="detailRef" :detail-data="detailData" />

    <el-dialog title="扫描" :visible.sync="scanTask.visible" width="500px" append-to-body>
      <el-form ref="form" :model="scanTask.form" :rules="scanTask.rules" label-width="80px">
        <el-form-item label="任务名称" prop="taskName">
          <el-input v-model="scanTask.form.taskName" placeholder="请输入任务名称" />
        </el-form-item>
        <el-form-item label="备注" prop="remarks">
          <el-input v-model="scanTask.form.remarks" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="scanTask.visible = false">取 消</el-button>
        <el-button
          :loading="buttonLoading"
          type="primary"
          @click="onConfirmScanTask"
        >确 定
        </el-button>
      </div>
    </el-dialog>

    <image-log ref="imageLogRef" :image-log-data="imageLogData" />

    <el-dialog
      v-if="dockerfileAmdArmUrl.visible"
      title="上传dockerfile文件"
      :visible.sync="dockerfileAmdArmUrl.visible"
      width="400px"
      append-to-body
    >
      <el-form
        ref="form"
        :model="dockerfileAmdArmUrl.form"
        :rules="dockerfileAmdArmUrl.rules"
        label-width="120px"
      >
        <el-form-item label="平台类型" prop="platformType">
          <el-radio-group v-model="dockerfileAmdArmUrl.form.platformType">
            <el-radio label="1">amd平台</el-radio>
            <el-radio label="2">arm平台</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer sa-flex sa-row-right">
        <el-button class="sa-m-l-12" @click="dockerfileAmdArmUrl.visible = false">取 消</el-button>
        <el-upload
          ref="upload"
          class="table-upload"
          :limit="1"
          :headers="upload.headers"
          :action="`${baseApi}/ima/info/upload?imageId=${dockerfileAmdArmUrl.form.id}&platformType=${dockerfileAmdArmUrl.form.platformType}`"
          :disabled="upload.isUploading"
          :on-progress="onProgressDockerfileAmdArmUrl"
          :on-success="onSuccessDockerfileAmdArmUrl"
        >
          <el-button type="primary">上传</el-button>
        </el-upload>
      </div>
    </el-dialog>

    <el-dialog
      v-if="backImageInfoData.visible"
      class="push-ima-reName-dialog"
      title="备份镜像"
      :visible.sync="backImageInfoData.visible"
      width="500px"
      append-to-body
    >
      <el-form
        ref="form"
        :model="backImageInfoData.form"
        :rules="backImageInfoData.rules"
        label-width="120px"
      >
        <!-- <el-form-item label="平台类型" prop="platformType">
          <el-radio-group v-model="backImageInfoData.form.platformType" disabled>
            <el-radio
              v-for="dict in dict.type.image_platform_type"
              :key="dict.value"
              :label="dict.value"
              >{{ dict.label }}</el-radio
            >
          </el-radio-group>
        </el-form-item> -->
        <el-form-item
          v-if="backImageInfoData.form.platformType == 2"
          label="镜像类型"
          prop="imageType"
        >
          <el-radio-group v-model="backImageInfoData.form.imageType">
            <el-radio label="amd">amd</el-radio>
            <el-radio label="arm">arm</el-radio>
            <el-radio label="all">全部</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备份方式">
          <el-radio-group v-model="backImageInfoData.form.backType">
            <el-radio label="1">默认方式</el-radio>
            <el-radio label="2">自定义方式</el-radio>
          </el-radio-group>
        </el-form-item>
        <template v-if="backImageInfoData.form.backType == 2">
          <template
            v-if="
              backImageInfoData.form.imageType == 'amd' || backImageInfoData.form.imageType == 'all'
            "
          >
            <el-form-item label="amd 仓库名" prop="repository">
              <el-input
                v-model="backImageInfoData.form.repository"
                placeholder="请输入amd 仓库名"
              />
            </el-form-item>
            <el-form-item label="amd Tag" prop="tag">
              <el-input v-model="backImageInfoData.form.tag" placeholder="请输入amd Tag" />
            </el-form-item>
          </template>
          <template
            v-if="
              backImageInfoData.form.imageType == 'arm' || backImageInfoData.form.imageType == 'all'
            "
          >
            <el-form-item label="arm 仓库名" prop="armRepository">
              <el-input
                v-model="backImageInfoData.form.armRepository"
                placeholder="请输入arm 仓库名"
              />
            </el-form-item>
            <el-form-item label="arm Tag" prop="armTag">
              <el-input v-model="backImageInfoData.form.armTag" placeholder="请输入arm Tag" />
            </el-form-item>
          </template>
        </template>
      </el-form>
      <div v-if="backImageInfoData.loading" class="dialog-loading">
        <i class="el-icon-loading" />
        <span class="text">备份中</span>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="backImageInfoData.visible = false">取 消</el-button>
        <el-button type="primary" @click="onBackImageInfo">确 定</el-button>
      </div>
    </el-dialog>

    <el-dialog title="查看勾选" :visible.sync="selectedData.visible" width="500px" append-to-body>
      <el-table class="sa-table" :data="selectedData.idList">
        <el-table-column label="镜像名称" align="center" prop="imageName" min-width="160" />
        <el-table-column label="平台类型" align="center">
          <template slot-scope="scope">
            <dict-tag :options="dict.type.image_platform_type" :value="scope.row.platformType" />
          </template>
        </el-table-column>
        <el-table-column label="镜像地址" align="center" min-width="160">
          <template slot-scope="scope">
            <sa-tooltip :content="scope.row.actualImageName" :is-copy="true" />
          </template>
        </el-table-column>
        <el-table-column label="构成镜像地址" align="center" min-width="120">
          <template slot-scope="scope">
            <template v-if="scope.row.platformType == 1">/</template>
            <template v-if="scope.row.platformType == 2">
              <sa-tooltip :content="scope.row.buildAmdUrl" :is-copy="true" />
              <sa-tooltip :content="scope.row.buildArmUrl" :is-copy="true" />
            </template>
          </template>
        </el-table-column>
        <el-table-column label="镜像大小" align="center" min-width="160">
          <template slot-scope="scope">
            <div v-if="scope.row.imageSize">
              <div v-for="item in scope.row.imageSize.split(',')">
                {{ item }}
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="备份镜像名称" align="center" min-width="120">
          <template slot-scope="scope">
            <div v-if="scope.row.backImageUrl">
              <template v-for="item in scope.row.backImageUrl.split(',')">
                <sa-tooltip :content="item" :is-copy="true" />
              </template>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="更新状态" align="center">
          <template slot-scope="scope">
            <dict-tag :options="dict.type.ima_update_status" :value="scope.row.updateStatus" />
          </template>
        </el-table-column>
        <el-table-column label="dockerfile地址" align="center" prop="dockerfileUrl" min-width="200">
          <template slot-scope="scope">
            <template v-if="scope.row.platformType == 1">
              {{ scope.row.dockerfileUrl }}
            </template>
            <template v-if="scope.row.platformType == 2">
              <div v-if="scope.row.dockerfileUrl">amd平台dockerfile</div>
              <div v-if="scope.row.dockerfileArmUrl">arm平台dockerfile</div>
            </template>
          </template>
        </el-table-column>
        <el-table-column label="导入方式" align="center">
          <template slot-scope="scope">
            <dict-tag :options="dict.type.ima_import_type" :value="scope.row.generateType" />
          </template>
        </el-table-column>
        <el-table-column label="创建时间" align="center" prop="createTime" min-width="180" />
        <el-table-column label="操作" align="center" fixed="right" min-width="80">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              @click="onDeleteIdsList(scope.$index, scope.row)"
            >删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>

    <InfoDialog ref="infoDialogRef" />
    <ScanRecordDialog ref="scanRecordDialogRef" />
  </div>
</template>

<script>
import { addInfo, delInfo, exportInfo, getInfo, listInfo, updateInfo } from '@/api/ima/info'
import { getToken } from '@/utils/auth'
import request from '@/utils/request'
import Log from './log.vue'
import ImageLog from './ImageLog.vue'
import InfoDialog from './components/InfoDialog/index.vue'
import LoopholeExport from './components/LoopholeExport/index.vue'
import ScanRecordDialog from './components/ScanRecordDialog/index.vue'
import { omit } from 'lodash-es'

export default {
  name: 'Info',
  components: {
    Log,
    ImageLog,
    InfoDialog,
    LoopholeExport,
    ScanRecordDialog
  },
  dicts: ['image_platform_type', 'loophole_library', 'ima_update_status', 'ima_import_type'],
  data() {
    return {
      baseApi: process.env.VUE_APP_BASE_API,
      // 按钮loading
      buttonLoading: false,
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 选中镜像类型
      imgType: '',
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 镜像信息表格数据
      list: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 导入参数
      upload: {
        // 是否显示弹出层
        open: false,
        // 弹出层标题
        title: '',
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: { Authorization: 'Bearer ' + getToken(), timeout: 160000 },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + '/ima/info/import'
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        platformType: undefined,

        loopholeLibrary: undefined,

        imageName: undefined,

        imageUrl: undefined,

        moduleVersion: undefined,

        initImageUrl: undefined,

        baseImageUrl: undefined,

        rollbacVersion: undefined,

        imagePrimaryFileUrl: undefined,

        imageVersion: undefined,

        dockerfileUrl: undefined,

        imageTag: undefined,

        imageSize: undefined,

        remarks: undefined,

        uploadTime: undefined,

        createUser: undefined,

        actualImageName: undefined,

        buildUrl: void 0
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        loopholeLibrary: [{ required: true, message: '不能为空', trigger: 'change' }],
        imageName: [
          {
            required: true,
            validator: (rule, value, callback) => {
              if (value) {
                var reg1 = new RegExp('[\\u4E00-\\u9FFF]+', 'g')
                var reg2 = /[A-Z]/
                if (
                  reg1.test(value) ||
                  reg2.test(value) ||
                  value.includes(':') ||
                  value.includes('：')
                ) {
                  callback(new Error(`不能输入汉字、大写字母、:、：`))
                } else {
                  callback()
                }
              } else {
                callback(new Error(`不能为空`))
              }
            },
            trigger: 'change'
          }
        ],
        actualImageName: [{ required: true, message: '不能为空', trigger: 'change' }],
        platformType: [{ required: true, message: '不能为空', trigger: 'change' }]
        // initImageUrl: [{ required: true, message: '不能为空', trigger: 'change' }],
        // baseImageUrl: [{ required: true, message: '不能为空', trigger: 'change' }],
        // imageVersion: [{ required: true, message: '不能为空', trigger: 'change' }],
      },

      importImagePackageOrUrlData: {
        visible: false,
        progressTimer: null,
        progressPercentage: 0,
        form: {
          id: '',
          imageName: '',
          generateType: 0,
          url: '',
          platformType: ''
        },
        rules: {}
      },

      dockerfileData: {
        visible: false,
        imageId: '',
        str: '',
        depend: {
          visible: false
        }
      },

      logData: {
        baseId: null
      },

      detailData: {
        id: '',
        data: {},
        formLabel: [
          {
            label: '平台类型',
            field: 'platformType',
            type: 'image_platform_type'
          },
          {
            label: '漏洞库环境',
            field: 'loopholeLibrary',
            type: 'loophole_library'
          },
          // {
          //   label: '镜像名称',
          //   field: 'imageName',
          // },
          {
            label: '镜像地址',
            field: 'actualImageName'
          },
          {
            label: '备份镜像地址',
            field: 'backImageUrl'
          },
          {
            label: '组件版本',
            field: 'moduleVersion'
          },
          {
            label: '原镜像地址',
            field: 'initImageUrl'
          },
          {
            label: '根镜像地址',
            field: 'baseImageUrl'
          },
          {
            label: '回退版本',
            field: 'rollbacVersion'
          },
          {
            label: '首次镜像地址',
            field: 'firstImageUrl'
          },
          {
            label: '镜像版本',
            field: 'imageVersion'
          },
          {
            label: 'dockerfile地址',
            field: 'dockerfileUrl'
          },
          {
            label: 'dockerfile-arm地址',
            field: 'dockerfileArmUrl'
          },
          {
            label: '镜像标签',
            field: 'imageTag'
          },
          {
            label: '镜像大小',
            field: 'imageSize'
          },
          {
            label: '是否首次版本',
            field: 'isFirstVersion'
          },
          {
            label: '镜像描述',
            field: 'remarks'
          },
          {
            label: '构成镜像amd地址',
            field: 'buildAmdUrl'
          },
          {
            label: '构成镜像arm地址',
            field: 'buildArmUrl'
          },
          {
            label: '更新状态',
            field: 'updateStatus',
            type: 'ima_update_status'
          },
          {
            label: '上传仓库时间',
            field: 'uploadTime'
          },
          {
            label: '创建时间',
            field: 'createTime'
          },
          {
            label: '更新时间',
            field: 'updateTime'
          }
        ]
      },

      scanTask: {
        visible: false,
        form: {},
        rules: {}
      },

      imageLogData: {},

      dockerfileAmdArmUrl: {
        visible: false,
        form: {
          id: '',
          imageName: '',
          platformType: '1'
        },
        rules: {}
      },

      indexTimeId: null,

      selectedItem: [],
      isSelectAll: false,
      isIndeterminate: false,

      backImageInfoData: {
        visible: false,
        loading: false,
        form: {},
        rules: {}
      },

      selectedData: {
        visible: false,
        idList: []
      }
    }
  },
  created() {
    this.getList()
    this.indexTimeId = setInterval(() => {
      setTimeout(this.getList(0), 0)
    }, 2000)
  },
  destroyed() {
    clearInterval(this.indexTimeId)
  },
  methods: {
    /** 查询镜像信息列表 */
    getList(loading) {
      this.loading = loading !== 0
      listInfo(this.queryParams).then((response) => {
        this.list = response.rows
        this.total = response.total

        this.calculateSelect()

        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        id: undefined,

        platformType: undefined,

        loopholeLibrary: undefined,

        imageName: undefined,

        imageUrl: undefined,

        moduleVersion: undefined,

        initImageUrl: undefined,

        baseImageUrl: undefined,

        rollbacVersion: undefined,

        imagePrimaryFileUrl: undefined,

        imageVersion: undefined,

        dockerfileUrl: undefined,

        imageTag: undefined,

        imageSize: undefined,

        remarks: undefined,

        uploadTime: undefined,

        createUser: undefined,

        createTime: undefined,

        actualImageName: undefined
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = '添加镜像信息'
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.loading = true
      this.reset()
      const id = row.id || this.ids
      getInfo(id).then((response) => {
        this.loading = false
        this.form = response.data
        this.open = true
        this.title = '修改镜像信息'
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['formRef'].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateInfo(this.form)
              .then((response) => {
                this.$modal.msgSuccess('修改成功')
                this.open = false
                this.getList()
              })
              .finally(() => {
              })
          } else {
            addInfo(this.form)
              .then((response) => {
                this.$modal.msgSuccess('新增成功')
                this.open = false
                this.getList()
              })
              .finally(() => {
              })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids
      this.$confirm('是否确认删除当前所选数据项？', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.loading = true
          return delInfo(ids)
        })
        .then(() => {
          this.loading = false
          this.getList()
          this.$modal.msgSuccess('删除成功')
        })
        .catch(() => {
        })
    },
    /** 导出按钮操作 */
    handleExport() {
      if (this.total > 200000) {
        this.$modal.msgWarning('抱歉，导出数据量过大，请选择筛选条件后再进行导出')
        return false
      }

      const submitAuery = JSON.parse(JSON.stringify(this.queryParams))
      delete submitAuery.pageNum
      delete submitAuery.pageSize

      this.download(
        '/ima/info/export',
        {
          ...submitAuery
        },
        `导出${this.$route.meta.title}_${new Date().getTime()}.xlsx`
      )
      // const queryParams = this.queryParams;
      // this.$confirm('是否确认导出所有镜像信息数据项?', '警告', {
      //   confirmButtonText: '确定',
      //   cancelButtonText: '取消',
      //   type: 'warning',
      // })
      //   .then(() => {
      //     this.exportLoading = true;
      //     return exportInfo(queryParams);
      //   })
      //   .then((response) => {
      //     this.download(response.msg);
      //     this.exportLoading = false;
      //   })
      //   .catch(() => {});
    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = '镜像信息导入'
      this.upload.open = true
    },
    /** 下载模板操作 */
    importTemplate() {
      // importTemplate().then((response) => {
      //   this.download(response.msg);
      // });
      this.download(
        '/ima/info/importTemplate',
        {},
        `${this.$route.meta.title}模板_${new Date().getTime()}.xlsx`
      )
    },
    /** 文件上传中处理*/
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true
    },
    /** 文件上传成功处理*/
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false
      this.upload.isUploading = false
      this.$refs.upload.clearFiles()
      this.$alert(response.msg, '导入结果', { dangerouslyUseHTMLString: true })
      this.getList()
    },
    /** 提交上传文件*/
    submitFileForm() {
      this.$refs.upload.submit()
    },
    onReadDockerfileContent(row, type) {
      this.dockerfileData.imageId = row.id
      this.imgType = type
      request({
        url: '/ima/info/readDockerfileContent',
        method: 'post',
        data: {
          fileUrl: type == 'amd' ? row.dockerfileUrl : row.dockerfileArmUrl
        }
      }).then((response) => {
        this.dockerfileData.visible = true
        this.dockerfileData.str = response.data
      })
    },
    onEditDockerfile() {
      const editContent = this.dockerfileData.str
        .replace(/<p>/g, '')
        .replace(new RegExp('</p>', 'g'), '\n')
      request({
        url: '/ima/info/editFileContent',
        method: 'post',
        data: {
          imageId: this.dockerfileData.imageId,
          editContent: editContent,
          imageType: this.imgType
        }
      }).then((response) => {
        if (response.code === 200) {
          this.dockerfileData.visible = false
          this.$modal.msgSuccess(response.msg)
          this.getList()

          // this.dockerfileData.depend.visible = true;
        }
      })
    },
    onProgressDependUpload() {
      // this.upload.isUploading = true;
    },
    onSuccessDependUpload(response) {
      this.dockerfileData.depend.visible = false
      this.$refs.dependUpload?.clearFiles()
      if (response.code === 200) {
        this.$modal.msgSuccess('操作成功')
        this.getList()
      } else {
        this.$modal.msgError(response.msg)
      }
    },
    onConfirmDependUpload() {
      if (this.$refs.dependUpload.uploadFiles.length > 0) {
        this.$refs.dependUpload?.submit()
      } else {
        this.dockerfileData.depend.visible = false
        this.$refs.dependUpload?.clearFiles()
      }
    },
    onProgressInfoImport() {
      this.upload.isUploading = true
    },
    onSuccessInfoImport(response) {
      this.upload.isUploading = false
      this.$refs.upload?.clearFiles()
      if (response.code == 200) {
        this.$modal.msgSuccess('操作成功')
        this.getList()
      } else {
        this.$modal.msgError(response.msg)
      }
    },
    onInfoDownload(row) {
      this.download(
        `${row.dockerfileUrl}`,
        {},
        row.dockerfileUrlName
      )
    },
    onOpenImportImagePackageOrUrl(row) {
      this.importImagePackageOrUrlData.visible = true
      this.importImagePackageOrUrlData.form = {
        id: row.id,
        platformType: row.platformType,
        imageName: row.imageName,
        generateType: 0,
        url: '',
        imageCategory: '1',
        imageType: 'amd'
      }
      this.importImagePackageOrUrlData.progressPercentage = 0
      clearInterval(this.importImagePackageOrUrlData.progressTimer)
    },
    onChangeImageCategory(val) {
      if (val == 1) {
        this.importImagePackageOrUrlData.form.imageType = 'amd'
      } else if (val == 2) {
        this.importImagePackageOrUrlData.form.imageType = ''
      }
    },
    onChangeGenerateType() {
      this.importImagePackageOrUrlData.form.url = ''
      this.$refs.importImageRef?.clearFiles()
    },
    onProgressImportImage() {
      this.upload.isUploading = true
    },
    onSuccessImportImage(response, file, fileList) {
      this.importImagePackageOrUrlData.visible = false
      this.upload.isUploading = false
      this.$refs.importImageRef?.clearFiles()
      if (response.code == 200) {
        this.$modal.msgSuccess(response.data)
        this.getList()
      } else {
        this.$modal.msgError(response.msg)
      }
    },
    onImportImagePackageOrUrl() {
      if (this.importImagePackageOrUrlData.form.generateType == 0) {
        this.importImagePackageOrUrlData.progressPercentage = 0
        this.startProgress()
        request({
          url: '/ima/info/importImagePackageOrUrl',
          method: 'post',
          params: {
            ...this.importImagePackageOrUrlData.form
          },
          timeout: 160000
        }).then((response) => {
          if (response.code == 200) {
            this.importImagePackageOrUrlData.progressPercentage = 100
            clearInterval(this.importImagePackageOrUrlData.progressTimer)
            setTimeout(() => {
              this.onSuccessImportImage(response)
            }, 500)
          } else {
            this.importImagePackageOrUrlData.progressPercentage = 0
            clearInterval(this.importImagePackageOrUrlData.progressTimer)
          }
        })
      } else {
        this.$refs.importImageRef?.submit()
      }
    },
    startProgress() {
      clearInterval(this.importImagePackageOrUrlData.progressTimer)
      this.importImagePackageOrUrlData.progressTimer = setInterval(() => {
        if (this.importImagePackageOrUrlData.progressPercentage == 90) {
          return false
        }
        this.importImagePackageOrUrlData.progressPercentage++
        if (this.importImagePackageOrUrlData.progressPercentage == 100) {
          clearInterval(this.importImagePackageOrUrlData.progressTimer)
        }
      }, 500)
    },
    onLog(row) {
      this.logData.baseId = row.id
      this.$refs.logRef.show()
    },
    // onSuccessDockerfileUrl(response, file, fileList) {
    //   console.log(response, file, fileList, 'response, file, fileList');
    // },
    onDetail(row) {
      this.$refs.infoDialogRef.open(row)
      // this.detailData.data = row
      // this.$refs.detailRef.show()
    },
    onScanTask() {
      this.scanTask.visible = true
      this.scanTask.form = {
        taskName: '',
        imageIds: this.ids,
        remarks: '',
        taskType: '2'
      }
    },
    onConfirmScanTask() {
      request({
        url: '/risk/report/task',
        method: 'post',
        data: this.scanTask.form
      }).then((response) => {
        this.$modal.msgSuccess(response.msg)
        this.scanTask.visible = false
        this.ids = []
        this.getList()
      })
    },
    onImageLog(row) {
      this.imageLogData.id = row.id
      this.$refs.imageLogRef.show()
    },
    // 更多操作触发
    handleCommand(command, row) {
      switch (command) {
        case 'log':
          this.onLog(row)
          break
        case 'downloadDockerfile':
          this.onInfoDownload(row)
          break
        case 'uploadDockerfile':
          this.dockerfileData.depend.visible = true
          break
        case 'importImagePackageOrUrl':
          this.onOpenImportImagePackageOrUrl(row)
          break
        case 'imageLog':
          this.onImageLog(row)
          break
        case 'backImageInfo':
          this.onOpenBackImageInfo(row)
          break
        case 'scanRecords':
          this.$refs.scanRecordDialogRef.open({
            params: {
              imageId: row.id,
              ...omit(this.queryParams, ['pageNum', 'pageSize'])
            }
          })
          break
        default:
          break
      }
    },

    onUploaderDockerfileAmdArmUrl(row) {
      this.dockerfileAmdArmUrl.visible = true
      this.dockerfileAmdArmUrl.form.id = row.id
      this.dockerfileAmdArmUrl.form.imageName = row.imageName
      this.dockerfileAmdArmUrl.form.platformType = '1'
    },
    onProgressDockerfileAmdArmUrl() {
      this.upload.isUploading = true
    },
    onSuccessDockerfileAmdArmUrl(response) {
      this.upload.isUploading = false
      this.$refs.upload?.clearFiles()
      if (response.code == 200) {
        this.dockerfileAmdArmUrl.visible = false
        this.$modal.msgSuccess('操作成功')
        this.getList()
      } else {
        this.$modal.msgError(response.msg)
      }
    },
    onOpenBackImageInfo(row) {
      this.backImageInfoData.visible = true
      this.backImageInfoData.loading = false
      this.backImageInfoData.form = {
        id: row.id,
        backType: '1',
        repository: '',
        tag: '',
        platformType: row.platformType,
        imageType: 'amd',
        armRepository: '',
        armTag: ''
      }
    },
    onBackImageInfo(row) {
      this.backImageInfoData.loading = true
      request({
        url: '/ima/info/backImageInfo',
        method: 'get',
        params: this.backImageInfoData.form
      }).then((response) => {
        this.$modal.msgSuccess(response.msg)
        this.backImageInfoData.loading = false
        this.backImageInfoData.visible = false
        this.getList()
      })
    },
    onUpdateImportImageUrl(isAll) {
      const params = {
        isAll,
        ids: ''
      }
      if (!isAll) {
        params.ids = this.ids.join(',')
      }
      request({
        url: '/ima/info/updateImportImageUrl',
        method: 'get',
        params: params
      }).then((response) => {
        this.$modal.msgSuccess(response.msg)
        this.ids = []
        this.getList()
      })
    },
    onSelect(type, row) {
      if (type) {
        this.ids.push(row.id)
        this.selectedItem.push(row)
      } else {
        const findIndex = this.ids.findIndex((id) => id == row.id)
        this.ids.splice(findIndex, 1)
        this.selectedItem.splice(findIndex, 1)
      }
      this.calculateSelect()
    },
    onSelectAll(type) {
      if (type) {
        this.list.forEach((item) => {
          this.ids.push(item.id)
          this.selectedItem.push(item)
        })
        this.ids = Array.from(new Set(this.ids))
      } else {
        this.list.forEach((item) => {
          if (this.ids.findIndex((id) => id == item.id) !== -1) {
            this.ids.splice(
              this.ids.findIndex((id) => id == item.id),
              1
            )
            this.selectedItem.splice(
              this.selectedItem.findIndex((i) => i.flagArr == item.flagArr),
              1
            )
          }
        })
      }
      this.calculateSelect()
    },
    calculateSelect() {
      this.isSelectAll = false
      this.isIndeterminate = false
      if (this.list.every((item) => this.ids.includes(item.id))) {
        this.isSelectAll = true
        this.isIndeterminate = false
      } else if (this.list.some((item) => this.ids.includes(item.id))) {
        this.isSelectAll = false
        this.isIndeterminate = true
      }

      if (this.list.length === 0) {
        this.isSelectAll = false
        this.isIndeterminate = false
      }
    },
    onOpenSelected() {
      this.selectedData.visible = true
      this.selectedData.idList = []
      request({
        url: '/ima/info/idsList',
        method: 'get',
        params: {
          ids: this.ids.join(',')
        }
      }).then((response) => {
        this.selectedData.idList = response.data
      })
    },
    onDeleteIdsList(index, row) {
      this.selectedData.idList.splice(index, 1)
      const idx = this.ids.findIndex((id) => id == row.id)
      this.ids.splice(idx, 1)
    }
  }
}
</script>

<style lang="scss">
.table-upload .el-upload-list {
  display: none;
}

.push-ima-reName-dialog {
  .el-dialog__body {
    position: relative;

    .dialog-loading {
      position: absolute;
      width: 200px;
      height: 200px;
      background: #eee;
      top: 50%;
      right: 50%;
      margin-top: -100px;
      margin-right: -100px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;

      .text {
        position: absolute;
      }

      .el-icon-loading {
        font-size: 150px;
      }
    }
  }
}
</style>
