<template>
  <EleSheet
    ref="sheetRef"
    v-bind="sheetProps"
    class="page-main page-main--full"
    @list-success="onListSuccess"
  >
    <template #before> </template>

    <template #search:dataScopeId:simple="{ model }">
      <RoleSelect
        :key="model.tenId"
        v-model="model.dataScopeId"
        v-bind="{ placeholder: model.tenId ? '请选择' : '请先选择所属租户' }"
        :params="{ tenId: model.tenId }"
        multiple
        collapse-tags
        class="el-select--nowrap"
        @label-change="(label) => $set(model, 'dataScopeName', label.join(','))"
      />
    </template>

    <template #table:isEnabled:simple="{ row }">
      <el-switch
        v-model="row.isEnabled"
        active-value="1"
        inactive-value="0"
        :disabled="!$checkPermi(['review:flow:updateStatus'])"
        @change="(value) => onSwitchChange(value, row)"
      />
    </template>

    <template #table:orderNum:simple="{ row }">
      <el-input-number
        v-model="row.orderNum"
        :min="0"
        class="!w-full"
        :disabled="!$checkPermi(['review:flow:updateStatus'])"
        @change="(value) => onInputNumberChange(value, row)"
      />
    </template>

    <template #form:dataScopePerms:simple="{ model }">
      <EleSelectDict
        v-model="model.dataScopePerms"
        :disabled="!model.dictType"
        v-bind="{ placeholder: model.dictType ? '请选择' : '请先选择绑定状态字典' }"
        :options="dict.type[model.dictType] || []"
        multiple
        @label-change="(label) => $set(model, 'dataScopePermsName', label.join(','))"
      />
    </template>

    <template #form:doAuditStatus:simple="{ model }">
      <EleSelectDict
        v-model="model.doAuditStatus"
        :disabled="!model.dictType"
        :options="dict.type[model.dictType] || []"
        multiple
      />
    </template>

    <template #form:auditStatusPass:simple="{ model }">
      <EleSelectDict
        v-model="model.auditStatusPass"
        :disabled="!model.dictType"
        :options="dict.type[model.dictType] || []"
      />
    </template>

    <template #form:auditStatusNotPass:simple="{ model }">
      <EleSelectDict
        v-model="model.auditStatusNotPass"
        :disabled="!model.dictType"
        :options="dict.type[model.dictType] || []"
      />
    </template>

    <template #form:tenId:simple="{ model }">
      <TenantSelect
        v-model="model.tenId"
        global-value
        @label-change="(label) => $set(model, 'tenName', label)"
      />
    </template>

    <template #form:dataScopeId:simple="{ model }">
      <RoleSelect
        :key="model.tenId"
        v-model="model.dataScopeId"
        v-bind="{ placeholder: model.tenId ? '请选择' : '请先选择所属租户' }"
        stringify
        :disabled="!model.tenId"
        :params="{ tenId: model.tenId }"
        multiple
        @label-change="(label) => $set(model, 'dataScopeName', label.join(','))"
      />
    </template>

    <template #table:doAuditStatus:simple="{ row }">
      <EleTagDict :value="row.doAuditStatus ? row.doAuditStatus.split(',') : []" class="flex" :options="dict.type[row.dictType]" />
    </template>
    <template #table:auditStatusPass:simple="{ row }">
      <EleTagDict :value="row.auditStatusPass" :options="dict.type[row.dictType]" />
    </template>
    <template #table:auditStatusNotPass:simple="{ row }">
      <EleTagDict :value="row.auditStatusNotPass" :options="dict.type[row.dictType]" />
    </template>

    <template #after> </template>
  </EleSheet>
</template>

<script>
import {
  addReviewFlow,
  delReviewFlow,
  getReviewFlow,
  listReviewFlow,
  updateReviewFlow
} from '@/api/review/flow.js'

import request from '@/utils/request.js'

import { dataMixin } from '@/mixins/index.js'

import { optionselect as getDictOptions } from '@/api/system/dict/type'

import RoleSelect from '@/components/business/RoleSelect/index.vue'

export default {
  dicts: ['known_basic_type'],
  components: {
    RoleSelect
  },
  mixins: [
    dataMixin({
      dictTypeList: {
        default: [],
        async load() {
          const res = await getDictOptions()

          const value = (res.data || []).map((item) => ({
            ...item,
            label: `${item.dictName}（${item.dictType}）`,
            value: item.dictType,
            raw: {
              listClass: 'default'
            }
          }))

          return value
        }
      }
    })
  ],
  data() {
    return {}
  },
  computed: {
    sheetProps() {
      const value = {
        title: '审核流程',

        lazy: false,

        api: {
          add: (params) => addReviewFlow({ ...params }),
          edit: (params) => updateReviewFlow({ ...params }),
          list: async(params) => listReviewFlow({ ...params }),
          info: getReviewFlow,
          remove: delReviewFlow,
          // export: '/sys/audit/export',
          import: '',
          template: ''
        },

        infoProps: {
          title: true
        },

        tableProps: {
          showIndex: false,
          height: '100%'
        },

        hiddenActions: {
          add: !this.$checkPermi(['review:flow:add']),
          remove: !this.$checkPermi(['review:flow:remove']),
          edit: !this.$checkPermi(['review:flow:edit']),
          info: true
        },

        model: {
          riskBasicType: {
            type: 'select',
            label: '业务类型',
            table: {
              width: 150,
              align: 'left'
            },
            search: {
              hidden: false,
              value: this.$route.query.riskBasicType
            },
            form: {
              rules: true
            },
            options: this.dict.type.known_basic_type
          },
          dictType: {
            type: 'select',
            label: '绑定状态字典',
            table: {
              width: 150,
              showOverflowTooltip: true,
              fieldProps: {
                class: 'truncate'
              }
            },
            search: {
              hidden: true
            },
            form: {
              rules: true,
              fieldProps: {
                on: {
                  selected: (value, selected, ctx) => {
                    this.$set(ctx.model, 'dictId', selected?.$attrs?.data?.dictId)
                    this.dict.init([value])
                    ctx.model.dataScopePerms = []
                    ctx.model.dataScopePerms = void 0
                    ctx.model.auditStatusPass = void 0
                    ctx.model.auditStatusNotPass = void 0
                  }
                }
              }
            },
            options: this.dataMixin.dictTypeList
          },
          dataScopePerms: {
            type: 'text',
            label: '状态数据权限',
            table: {
              width: 180
            },
            search: {
              hidden: true
            },
            form: {
              rules: true,
              formatter: (data) => this.string2Array(data.dataScopePerms),
              parameter: (value) => (value || []).join(',')
            },
            formatter: (data) => data.dataScopePermsName
          },
          // codeEmbeddingPointCode: {
          //   type: 'text',
          //   label: '当前节点编码',
          //   table: {
          //     width: 120
          //   },
          //   search: {
          //     hidden: true
          //   },
          //   form: {
          //     rules: true
          //   }
          // },
          currentButtonName: {
            type: 'text',
            label: '当前节点',
            table: {
              width: 150
            },
            search: {
              hidden: false
            },
            form: {
              rules: true
            }
          },
          nextButtonName: {
            type: 'text',
            label: '下一节点',
            table: {
              width: 120
            },
            search: {
              hidden: true
            },
            form: {
              rules: true
            }
          },
          doAuditStatus: {
            label: '待审核标志',
            search: {
              hidden: true
            },
            form: {
              rules: true
            },
            table: {
              width: 150,
              showOverflowTooltip: true
            },
            formatter: (data) => data.doAuditStatus ? data.doAuditStatus.split(',') : [],
            parameter: (value) => (value || []).join(',')
          },
          auditStatusPass: {
            label: '审核通过标志',
            search: {
              hidden: true
            },
            form: {
              rules: true
            }
          },
          auditStatusNotPass: {
            label: '审核不通过标志',
            search: {
              hidden: true
            },
            form: {
              rules: true
            }
          },
          orderNum: {
            label: '优先级',
            table: {
              width: 150
            },
            search: {
              hidden: true
            },
            form: {
              type: 'input-number',
              value: 0,
              rules: true
            }
          },
          tenId: {
            type: 'text',
            label: '所属租户',
            table: {
              hidden: true,
              formatter: (data) => data.tenName
            },
            search: {
              hidden: true
            },
            form: {
              rules: true,
              value: this.$store.getters.userInfo.tenId
            }
          },
          dataScopeId: {
            label: '角色权限',
            table: {
              width: 150,
              formatter: (data) => data.dataScopeName
            },
            search: {
              hidden: false,
              parameter: (value) => (value || []).join(',')
            },
            form: {
              rules: true,
              formatter: (data) => data.dataScopeId ? data.dataScopeId.split(',') : [],
              parameter: (value) => (value || []).join(',')
            }

          },
          isEnabled: {
            type: 'select',
            label: '状态',
            table: {
              width: 100
            },
            search: {
              hidden: true
            },
            form: {
              hidden: true
            }
          },
          remark: {
            type: 'textarea',
            label: '备注',
            table: {
              hidden: true
            },
            search: {
              hidden: true
            },
            form: {
              rules: false
            }
          },
          updateTime: {
            type: 'text',
            label: '修改时间',
            table: {
              width: 160
            },
            search: {
              hidden: true
            },
            form: {
              hidden: true
            }
          },
          createTime: {
            type: 'text',
            label: '创建时间',
            search: {
              type: 'date-range',
              parameter: (data) => {
                return {
                  startTime: data?.[0],
                  endTime: data?.[1]
                }
              }
            },
            form: {
              hidden: true
            },
            width: 200
          }
        }
      }

      return value
    }
  },
  methods: {
    async onSwitchChange(isEnabled, row) {
      const params = {
        id: row.id,
        isEnabled
      }

      const res = await this.sheetProps.api.edit(params)

      if (res.code === 200) {
        this.$message.success(res.msg)
      }
    },
    async onInputNumberChange(orderNum, row) {
      const params = {
        id: row.id,
        orderNum
      }

      const res = await this.sheetProps.api.edit(params)

      if (res.code === 200) {
        this.$message.success(res.msg)
      }
    },
    string2Array(value) {
      return value ? value.split(',') : []
    },
    onListSuccess(data) {
      const dictKeys = data.reduce((arr, item) => {
        if (item.dictType) {
          arr.push(item.dictType)
        }
        return arr
      }, [])

      this.dict.init(dictKeys)
    }
  }
}
</script>

<style></style>
