<template>
  <ModelSheet
    class=""
    v-bind="{
      title: '研判模型',
      params: {
        modelType: '2'
      },
      source: {
        type: 'judgment',
        riskStatusDict: dict.type.risk_status,
      }
    }"
  ></ModelSheet>
</template>

<script>
import ModelSheet from '@/views/model/components/ModelSheet/index.vue'

export default {
  dicts: ['risk_status'],
  components: {
    ModelSheet
  }
}
</script>

<style>

</style>
