<template>
  <div class="" :class="autoHeight ? 'page-main--full' : ''">
    <div v-if="layout.includes('stats')" class="flex-none pb-4">
      <StatsOverview
        ref="statsOverviewRef"
        v-bind="{
          api: knowAdapter.api,
          statusDict: dict.type[statusDictType]
        }"
      />
    </div>

    <div v-full:height="autoHeight" class="overflow-hidden">
      <EleSheet :key="sheetKey" ref="sheetRef" v-bind="sheetProps" class="page-main" :class="pageMainFlat ? 'page-main--flat' : ''" @list-success="onListSuccess">
        <template #before>
        </template>

        <template v-if="!onlyRefresh" #toolbar:after="{ selected, selectionIds }">
          <el-button v-for="(item,index) of dataMixin.reviewConfigList" :key="index" :disabled="!selected" icon="el-icon-s-check" type="success" @click="onReviewClick(item, selectionIds)">
            {{ item.currentButtonName }}
          </el-button>

          <el-button v-if="dataMixin.reviewConfigList.length" type="success" icon="iconfont icon-piliangshenhe" @click="onMergeClick">归并审核</el-button>

          <el-button v-if="$checkPermi(['model:know:back'])" type="warning" :disabled="!selected" @click="onBackClick(selectionIds)"><IconUndo class="el-icon" />退回</el-button>

          <el-button v-if="$checkPermi(['model:know:dispose'])" type="primary" @click="onDisposeClick(selectionIds)"><IconHandyman class="" />处置</el-button>

          <el-button v-if="$checkPermi(['model:know:delay'])" type="warning" icon="el-icon-date" @click="onDelayClick(selectionIds)">自动延期</el-button>

        </template>

        <template #table:riskAuditFileName:simple="{ row }">
          <div class="hover:underline text-primary-500 cursor-pointer truncate" @click="onFileClick(row)">
            {{ row.riskAuditFileName || row.riskAuditFileUrl }}
          </div>
        </template>

        <template #info:riskAuditFileName:simple="{ data }">
          <div class="hover:underline text-primary-500 cursor-pointer truncate" @click="onFileClick(data)">
            {{ data.riskAuditFileName || data.riskAuditFileUrl }}
          </div>
        </template>

        <template #table:riskRangeInfluence="{ columnProps }">
          <el-table-column v-slot="{ row }" v-bind="{ ...columnProps }">
            <EleFloating :text="row.riskRangeInfluence" :floating-text="(row.riskRangeInfluence || '').replaceAll(',', '<br />')"></EleFloating>
          </el-table-column>
        </template>

        <template v-if="!onlyRefresh" #toolbar:quick>
          <EleTooltipButton
            v-if="$checkPermi(['rules:field:config'])"
            type="default"
            content="字段配置"
            icon="el-icon-notebook-2"
            class="flex-none"
            @click="onFieldClick"
          />

          <EleTooltipButton
            v-if="$checkPermi(['review:flow:list'])"
            type="default"
            content="审核流程配置"
            icon="el-icon-coordinate"
            class="flex-none"
            @click="onFlowClick"
          />
        </template>

        <template v-if="['default'].includes(source.type)" #table:action:before="{ row }">
          <el-button v-if="$checkPermi(['model:know:analyze'])" type="text" size="mini" @click="onAnalyzeClick(row)">关联分析</el-button>
        </template>

      </EleSheet>
    </div>

    <ReviewDialog
      ref="reviewDialogRef"
    />
    <MergeDialog
      ref="mergeDialogRef"
      v-bind="{ dataMixin, sheetProps, knowAdapter, getDynamicModel, imageViewerRef, onFileClick }"
    />
    <FieldDialog ref="fieldDialogRef" />

    <SystemDialog ref="systemDialogRef" />
    <LineDialog ref="lineDialogRef" />
    <DisposeDialog ref="disposeDialogRef" />

    <DelayDialog ref="delayDialogRef" />

    <el-image-viewer v-if="imageViewerRef.visible" v-bind="imageViewerRef.props" />
  </div>

</template>

<script>
import { camelCase } from 'lodash-es'
import { dataMixin } from '@/mixins'
import request from '@/utils/request.js'

import { listReviewFlow } from '@/api/review/flow.js'
import { presetKnowColumnMap } from '@/configs/column/index.js'

import StatsOverview from './components/StatsOverview/index.vue'
import ReviewDialog from './components/ReviewDialog/index.vue'
import MergeDialog from './components/MergeDialog/index.vue'
import FieldDialog from '@/views/rules/field/components/MappingDialog/index.vue'
import SystemDialog from './components/SystemDialog/index.vue'
import LineDialog from './components/LineDialog/index.vue'
import DisposeDialog from './components/DisposeDialog/index.vue'
import DelayDialog from './components/DelayDialog/index.vue'

export default {
  dicts: [],
  components: {
    StatsOverview,
    ReviewDialog,
    MergeDialog,
    FieldDialog,
    SystemDialog,
    LineDialog,
    DisposeDialog,
    DelayDialog
  },
  mixins: [
    dataMixin({
      fieldConfigInfo: {
        default: {
          tableFieldInfoList: []
        },
        async load() {
          const res = await request({
            url: '/common/ruleTableInfo/getInfoByRiskBasicType',
            method: 'post',
            data: {
              riskBasicType: this.riskBasicType
            }
          })

          const value = res.data

          return value
        },
        async success() {
          const dictKeys = this.dataMixin.fieldConfigInfo.tableFieldInfoList.reduce((arr, item) => {
            if (item.dictType) {
              arr.push(item.dictType)
            }
            return arr
          }, [])

          this.dict.init(dictKeys)

          ++this.sheetKey

          await this.$nextTick()

          this.$refs.sheetRef.getTableData()
        }
      },
      reviewConfigList: {
        default: [],
        async load() {
          const res = await listReviewFlow({
            pageNum: 1,
            pageSize: 20,
            riskBasicType: this.riskBasicType,
            dataScopeId: this.$store.getters.userInfo.roleId
          })

          const value = res.rows

          return value
        },
        async success() {
          this.statusDictType = this.dataMixin.reviewConfigList[0]?.dictType
          this.dict.init([this.statusDictType])
        }
      }
    })
  ],
  props: {
    params: {
      type: Object,
      default: () => ({})
    },
    layout: {
      type: String,
      default: 'stats, search,toolbar,table,paging'
    },
    autoHeight: {
      type: Boolean,
      default: true
    },
    pageMainFlat: {
      type: Boolean,
      default: false
    },
    onlyRefresh: {
      type: Boolean,
      default: false
    },
    source: {
      type: Object,
      default: () => ({
        type: 'default'
      })
    }
  },
  data() {
    return {
      sheetKey: 0,
      statusDictType: void 0,
      imageViewerRef: {
        visible: false,
        props: {
          zIndex: 3000,
          urlList: [],
          onClose: () => {
            this.imageViewerRef.visible = false
            this.imageViewerRef.props.urlList = []
          }
        },
        open: (props) => {
          Object.assign(this.imageViewerRef.props, props)
          this.imageViewerRef.visible = true
        }
      }
    }
  },
  computed: {
    riskBasicType() {
      return this.params.riskBasicType || this.$route.query.riskBasicType
    },
    knowAdapter() {
      const value = {
        101: {
          title: '系统漏洞知识库',
          api: {
            list: async(params) => {
              return request({
                url: '/know/basicSystemLibrary/list',
                method: 'get',
                params: { ...this.params, ...params }
              })
            },
            merge: async(params) => {
              return request({
                url: '/know/basicSystemLibrary/merge',
                method: 'get',
                params: { ...params }
              })
            },
            info: async(id) => {
              return request({
                url: `/know/basicSystemLibrary/${id}`,
                method: 'get'
              })
            },
            remove: async(ids) => {
              return request({
                url: `/know/basicSystemLibrary/${ids}`,
                method: 'delete'
              })
            },
            add: async(data) => {
              return request({
                url: `/know/basicSystemLibrary`,
                method: 'post',
                data
              })
            },
            edit: async(data) => {
              return request({
                url: `/know/basicSystemLibrary`,
                method: 'put',
                data
              })
            },
            stats: async(params = {}) => {
              return request({
                url: `/know/basicSystemLibrary/count`,
                method: 'get',
                params
              })
            },
            // import: '/know/basicSystemLibrary/import',
            template: '/know/basicSystemLibrary/importTemplate',
            export: '/know/basicSystemLibrary/export'
          }
        },
        102: {
          title: '基线知识库',
          api: {
            list: async(params) => {
              return request({
                url: '/know/basicLineLibrary/list',
                method: 'get',
                params: { ...this.params, ...params }
              })
            },
            merge: async(params) => {
              return request({
                url: '/know/basicLineLibrary/merge',
                method: 'get',
                params: { ...params }
              })
            },
            info: async(id) => {
              return request({
                url: `/know/basicLineLibrary/${id}`,
                method: 'get'
              })
            },
            remove: async(ids) => {
              return request({
                url: `/know/basicLineLibrary/${ids}`,
                method: 'delete'
              })
            },
            add: async(data) => {
              return request({
                url: `/know/basicLineLibrary`,
                method: 'post',
                data
              })
            },
            edit: async(data) => {
              return request({
                url: `/know/basicLineLibrary`,
                method: 'put',
                data
              })
            },
            stats: async(params = {}) => {
              return request({
                url: `/know/basicLineLibrary/count`,
                method: 'get',
                params
              })
            },
            // import: '/know/basicLineLibrary/import',
            template: '/know/basicLineLibrary/importTemplate',
            export: '/know/basicLineLibrary/export'
          }
        }
      }[this.riskBasicType]

      return value
    },
    dynamicModel() {
      const fieldList = this.dataMixin.fieldConfigInfo.tableFieldInfoList

      const value = this.getDynamicModel(fieldList)

      return value
    },
    systemModel() {
      const commonPreset = {
        type: 'CommonSystemSelect',
        fieldProps: {
          returnName: true
        }
      }

      return {
        search: {
          ...commonPreset
        },
        form: {
          ...commonPreset
        }
      }
    },

    numberModel() {
      const commonPreset = {
        type: 'input-number',
        fieldProps: {}
      }

      return {
        search: {
          ...commonPreset
        },
        form: {
          ...commonPreset
        }
      }
    },
    departmentModel() {
      const commonPreset = {
        type: 'CommonDepartmentSelect',
        fieldProps: {
          returnName: true
        }
      }

      return {
        search: {
          ...commonPreset
        },
        form: {
          ...commonPreset,
          value: this.$store.getters.userInfo.deptName
        }
      }
    },
    sheetProps() {
      const value = {
        lazy: true,

        api: this.knowAdapter.api,

        title: this.knowAdapter.title,

        layout: this.layout,

        hiddenActions: {
          'add': !this.$checkPermi(['model:know:add']) || this.onlyRefresh,
          'remove': !this.$checkPermi(['model:know:remove']) || this.onlyRefresh,
          'edit': !this.$checkPermi(['model:know:edit']) || this.onlyRefresh,
          'export': !this.$checkPermi(['model:know:export']) || this.onlyRefresh
        },

        flowProps: {
          preset: 'review',
          params: {
            riskBasicType: this.riskBasicType,
            statusDict: this.dict.type[this.statusDictType]
          }
        },

        model: {
          ...this.dynamicModel
        }
      }

      if (this.autoHeight) {
        Object.assign(value, {
          tableProps: {
            height: '100%'
          }
        })
      }

      return value
    }
  },
  methods: {
    datetimeModel(info) {
      const commonPreset = {
        type: 'date-time',
        fieldProps: {}
      }

      return {
        search: {
          type: 'date-time-range',
          parameter: (data) => {
            return {
              [`${info.field}Start`]: data?.[0],
              [`${info.field}End`]: data?.[1]
            }
          }
        },
        form: {
          ...commonPreset
        }
      }
    },
    getDynamicModel(fieldList) {
      const value = fieldList
        .sort((a, b) => a.sortNum - b.sortNum)
        .reduce((obj, item) => {
          const field = camelCase(item.fieldEnglishName)
          const label = item.fieldChineseName

          const sourceModel = this.dataSourceResolve(item)
          const dictModel = this.dictResolve(item)
          const fieldTypeModel = this.fieldTypeResolve({ ...item, field })

          const presetField = presetKnowColumnMap({
            source: this.source,
            params: this.params
          })[field] || {}

          obj[field] = {
            label,
            ...presetField,
            ...fieldTypeModel,
            ...sourceModel,
            ...dictModel,
            info: {
              hidden: ['0'].includes(item.detailShow),
              ...(presetField.info || {}),
              ...(fieldTypeModel.info || {}),
              ...(sourceModel.info || {}),
              ...(dictModel.info || {})
            },
            table: {
              hidden: ['0'].includes(item.listShow),
              ...(item.columnWidth ? { width: item.columnWidth } : {}),
              ...(presetField.table || {}),
              ...(fieldTypeModel.table || {}),
              ...(sourceModel.table || {}),
              ...(dictModel.table || {})
            },
            search: {
              hidden: ['0'].includes(item.queryShow),
              ...(presetField.search || {}),
              ...(fieldTypeModel.search || {}),
              ...(sourceModel.search || {}),
              ...(dictModel.search || {})
            },
            form: {
              rules: item.isBized === '1',
              hidden: ['0'].includes(item.saveShow),
              ...(presetField.form || {}),
              ...(fieldTypeModel.form || {}),
              ...(sourceModel.form || {}),
              ...(dictModel.form || {})
            },
            add: {
              ...(presetField.add || {}),
              ...(fieldTypeModel.add || {}),
              ...(sourceModel.add || {}),
              ...(dictModel.add || {})
            },
            edit: {
              ...(presetField.edit || {}),
              ...(fieldTypeModel.edit || {}),
              ...(sourceModel.edit || {}),
              ...(dictModel.edit || {})
            }
          }

          return obj
        }, {})

      return value
    },
    dictResolve(info) {
      if (!info.dictType) {
        return {}
      }

      return {
        type: 'select',
        options: this.dict.type[info.dictType]
      }
    },
    dataSourceResolve(info) {
      const value = {
        'department': this.departmentModel,
        'system': this.systemModel
      }[info.dataSource]

      return value || {}
    },
    fieldTypeResolve(info) {
      const value = {
        'timestamp': this.datetimeModel(info),
        'int4': this.numberModel,
        'int8': this.numberModel
      }[info.fieldType]

      return value || {}
    },
    onReviewClick(info, ids) {
      this.$refs.reviewDialogRef.open({
        ...info,
        title: info.currentButtonName,
        params: {
          ids: ids.join(','),
          riskBasicType: this.riskBasicType,
          riskAuditStatusPass: info.auditStatusPass,
          riskAuditStatusNotPass: info.auditStatusNotPass,
          doAuditStatus: info.doAuditStatus
        },
        success: () => {
          this.$refs.sheetRef.getTableData()
        }
      })
    },

    async onListSuccess() {
      if (this.$refs.statsOverviewRef) {
        this.$refs.statsOverviewRef.load({
          ...this.params,
          ...this.$refs.sheetRef.searchMixin.parameter()
        })
      }
    },

    onFlowClick() {
      this.$router.push({
        path: '/review/flow',
        query: {
          riskBasicType: this.riskBasicType
        }
      })
    },

    onFieldClick() {
      const row = this.dataMixin.fieldConfigInfo

      this.$refs.fieldDialogRef.open({
        params: {
          tableId: row.id,
          tableEnglishName: row.tableEnglishName
        },
        success: () => {
          this.dataMixin.load(['fieldConfigInfo'])
        }
      })
    },

    async onMergeClick() {
      this.$refs.mergeDialogRef.open({
        mergeBeforeTotal: this.$refs.sheetRef.pagingMixin.total,
        params: {
          riskBasicType: this.riskBasicType
        }
      })
    },

    onFileClick(row) {
      const exts = ['.png', '.jpg', '.jpeg', '.gif', '.bmp']

      if (exts.some(item => row.riskAuditFileName.includes(item))) {
        this.imageViewerRef.open({
          urlList: [row.riskAuditFileUrl]
        })

        return false
      }

      this.download(row.riskAuditFileUrl, {}, row.riskAuditFileName)
    },

    async onBackClick(ids) {
      try {
        await this.$confirm('是否确定退回?', '提示', {
          type: 'warning'
        })
      } catch (error) {
        return error
      }

      const res = await request({
        url: '/risk/audit/goback',
        method: 'post',
        data: {
          ids: ids.join(','),
          riskBasicType: this.riskBasicType
        }
      })

      if (res.code == 200) {
        this.$message.success(res.msg)
        this.$refs.sheetRef.getTableData()
      }
    },
    onAnalyzeClick(row) {
      switch (this.riskBasicType) {
        case '101':
          this.$refs.systemDialogRef.open({
            params: {
              riskKnowLibraryOnlyValue: row.riskKnowLibraryOnlyValue,
              riskRangeInfluence: row.riskRangeInfluence
            }
          })
          break
        case '102':
          this.$refs.lineDialogRef.open({
            params: {
              riskKnowLibraryOnlyValue: row.riskKnowLibraryOnlyValue,
              riskRangeInfluence: row.riskRangeInfluence
            }
          })
          break
      }
    },
    onDisposeClick() {
      this.$refs.disposeDialogRef.open({
        params: {
          riskBasicType: this.riskBasicType
        },
        success: () => {
          this.$refs.sheetRef.getTableData()
        }
      })
    },

    onDelayClick() {
      this.$refs.delayDialogRef.open({
        params: {
          riskBasicType: this.riskBasicType
        }
      })
    },

    infoAction(row) {
      this.$refs.sheetRef.handleInfo(row)
    }
  }
}
</script>

<style></style>
