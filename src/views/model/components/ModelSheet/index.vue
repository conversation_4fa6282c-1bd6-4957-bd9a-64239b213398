<template>
  <EleSheet ref="sheetRef" v-bind="sheetProps" class="page-main page-main--full" @info-success="onInfoSuccess">
    <template #before> </template>

    <template #table:isEnable:simple="{ row }">
      <el-switch v-model="row.isEnable" :disabled="!$checkPermi(['modelDefine:edit:status'])" active-value="1" inactive-value="0" @change="onSwitchChange({ isEnable: $event }, row)" />
    </template>

    <template #table:action:before="{ row }">
      <el-button v-if="$checkPermi(['modelDefine:distribution:show'])" type="text" size="mini" @click="handleDistribution(row)">{{ strategyName || title.slice(0, 2) }}策略</el-button>
    </template>

    <template #after>
      <DistributionDialog ref="distributionDialogRef" v-bind="{ source }" />
    </template>
  </EleSheet>
</template>

<script>

import {
  addModel,
  delModel,
  getModel,
  listModel,
  updateModel
} from '@/api/model/index.js'

// import request from '@/utils/request.js'

import DistributionDialog from '../DistributionDialog/index.vue'

import { dataMixin } from '@/mixins/index.js'

import request from '@/utils/request.js'

export default {
  dicts: ['basic_type', 'risk_basic_type'],
  mixins: [
    dataMixin({
      pointList: {
        default: [],
        async load() {
          const res = await request({
            url: '/rule/point/list',
            method: 'get',
            params: {
              pageNum: 1,
              pageSize: 1000,
              riskBasicType: this.activeRuleType
            }
          })

          const rows = (res.rows || []).map(item => ({ ...item, id: String(item.id) }))

          if (!this.defaultPointList.length) {
            this.defaultPointList = rows
          }

          return rows
        }
      }
    })],
  components: {
    DistributionDialog
  },
  props: {
    title: {
      type: String,
      default: ''
    },
    params: {
      type: Object,
      default: () => ({})
    },
    strategyName: {
      type: String,
      default: ''
    },
    source: {
      type: Object,
      default: () => ({
        type: 'default'
      })
    }
  },
  data() {
    return {
      defaultPointList: [],
      activeRuleType: void 0
    }
  },
  computed: {
    defaultPointTreeList() {
      return this.handleTree(this.defaultPointList, 'id')
    },
    pointTreeList() {
      return this.handleTree(this.dataMixin.pointList, 'id')
    },
    sheetProps() {
      const value = {
        title: this.title,

        lazy: false,

        defaultPageSize: 20,

        api: {
          add: (params) => addModel({ ...params, ...this.params }),
          edit: (params) => updateModel({ ...params }),
          list: async(params) => listModel({ ...params, ...this.params }),
          info: getModel,
          remove: delModel
          // export: '/model/export',
          // import: '',
          // template: ''
        },

        infoProps: {
          title: true
        },

        tableProps: {
          height: '100%'
        },

        hiddenActions: {
          add: !this.$checkPermi(['modelDefine:add:show']),
          edit: !this.$checkPermi(['modelDefine:edit:show']),
          remove: !this.$checkPermi(['modelDefine:remove:show']),
          info: true
        },

        formProps: {
          customClass: 'el-dialog--mini'
        },

        model: {
          modelName: {
            label: '模型名称',
            lg: 24,
            align: 'left',
            rules: true
          },
          // riskType: {
          //   label: '业务类型',
          //   type: 'select',
          //   lg: 24,
          //   rules: true,
          //   options: this.dict.type.basic_type,
          //   table: {
          //     hidden: true
          //   },
          //   search: {
          //     hidden: true
          //   }
          // },
          ruleNum: {
            label: '规则数量',
            hidden: true,
            table: {
              hidden: false
            }
          },
          riskBasicType: {
            label: '安全风险类型',
            type: 'select',
            lg: 24,
            options: this.dict.type.risk_basic_type,
            rules: true,
            fieldProps: {
              on: {
                change: (value) => {
                  this.activeRuleType = value
                  this.dataMixin.load('pointList')
                }
              }
            }
          },
          checkPointPerms: {
            label: '检查点',
            rules: true,
            form: {
              type: 'select-tree',
              fieldProps: {
                value: [],
                props: {
                  value: 'id',
                  label: 'codeEmbeddingPointName',
                  children: 'children'
                },
                multiple: true,
                checkStrictly: true
              },
              options: this.pointTreeList
            },
            table: {
              width: 150,
              type: 'select',
              options: this.defaultPointList.map(item => ({
                label: item.codeEmbeddingPointName,
                value: item.id,
                raw: {
                  listClass: 'default'
                }
              })),
              fieldProps: {
                class: 'whitespace-nowrap truncate',
                separator: ','
              },
              tableColumnProps: {
                showOverflowTooltip: true
              },
              formatter: (row) => row.checkPointPerms || []
            },
            search: {
              hidden: true
            },
            lg: 24
          },
          isEnable: {
            label: '状态',
            type: 'select',
            lg: 24,
            options: [
              {
                label: '启用',
                value: '1'
              },
              {
                label: '禁用',
                value: '0'
              }
            ],
            form: {
              hidden: true
            }
          },
          createTime: {
            type: 'text',
            label: '创建时间',
            search: {
              type: 'date-range',
              parameter: (data) => {
                return {
                  startTime: data?.[0],
                  endTime: data?.[1]
                }
              }
            },
            form: {
              hidden: true
            },
            width: 200
          },
          createBy: {
            label: '创建人',
            hidden: true,
            table: {
              hidden: false
            }
          },
          updateTime: {
            label: '修改时间',
            hidden: true,
            table: {
              hidden: false
            },
            width: 200
          },
          updateBy: {
            label: '修改人',
            hidden: true,
            table: {
              hidden: false
            }
          }
        }
      }

      return value
    }
  },
  methods: {
    onInfoSuccess(data) {
      this.activeRuleType = data.riskBasicType
      this.dataMixin.load('pointList')
    },
    handleDistribution(row) {
      this.$refs.distributionDialogRef.open({ ...row, title: `${this.title.slice(0, 2)}策略` })
    },
    async onSwitchChange(changeParams, row) {
      const params = {
        id: row.id,
        ...changeParams
      }

      const res = await this.sheetProps.api.edit(params)

      if (res.code === 200) {
        this.$message.success(res.msg)
      } else {
        this.$message.warning(res.msg)
      }
    }
  }
}
</script>

<style></style>
