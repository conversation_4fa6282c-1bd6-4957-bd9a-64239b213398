<template>
  <div class="space-x-4 h-full">
    <div class="relative">
      <EleSheet
        :key="[currentAudit.nodeName].join('-')"
        ref="sheetRef"
        class="page-main page-main--flat"
        v-bind="sheetProps"
        @current-change="onCurrentChange"
        @query-success="onQuerySuccess"
      >
        <template #table:ruleLevel:simple="{ row }">
          <el-input-number v-model="row.ruleLevel" :min="0" class="!w-full" :disabled="!$checkPermi(['modelDefine:distribution:show:update'])" @change="(value) => onInputNumberChange(value, row)"></el-input-number>
        </template>

        <template #table:isEnabled:simple="{ row }">
          <el-switch
            v-model="row.isEnabled"
            active-value="true"
            inactive-value="false"
            :disabled="!$checkPermi(['modelDefine:distribution:show:update'])"
            @change="(value) => onSwitchChange(value, row)"
          />
        </template>

        <template v-if="['default'].includes(source.type)" #search:riskOperatorGroupId:simple="{ model }">
          <DisposeGroupSelect
            v-model="model.riskOperatorGroupId"
            placeholder="请选择"
            clearable
            :params="{
              riskBasicType: currentAudit.checkType,
            }"
            @change="(value, label) => (model.riskOperatorGroupName = label)"
          />
        </template>

        <template #table:action:after="{ row, removeHandler }">
          <el-button type="text" size="mini" :disabled="!$checkPermi(['modelDefine:distribution:show:remove'])" @click.stop="removeHandler(row)">
            删除
          </el-button>
        </template>

        <template #table:effectiveTime:simple="{ row }">
          <div class="" :class="isExceeded(row) ? 'text-red-500' : ''">
            <span class="">{{ row.startEffectiveTime }}</span>
            <span class="px-1">至</span>
            <span>{{ row.endEffectiveTime }}</span>
          </div>
        </template>

        <template #after>

          <div class="absolute bottom-0 right-0" @click="fullTable=!fullTable">
            <el-button :icon="!fullTable ? 'el-icon-circle-plus-outline' : 'el-icon-remove-outline'">{{ !fullTable ? '扩展表格' : '收起表格' }}</el-button>
          </div>
        </template>
      </EleSheet>

      <div
        v-if="!currentAudit.nodeName"
        class="absolute inset-0 bg-white/30 z-50"
        title="请先选择左侧审计类"
      />
    </div>
    <div class="overflow-hidden">
      <RuleEditor
        :key="[currentAudit.nodeName].join('-')"
        v-bind="{
          currentRow,
          currentAudit,
          searchRuleExp,
          source
        }"
        @success="onRuleEditorSuccess"
      />
    </div>
  </div>
</template>

<script>
import request from '@/utils/request.js'

import { addRule, delRule, getRule, listRule, updateRule } from '@/api/audit/distribution.js'

import RuleEditor from './RuleEditor/index.vue'

import { checkpointType as checkpointTypeDict } from '@/dicts/rules/checkpoint.js'
import { debounce } from 'lodash-es'
import dayjs from 'dayjs'

export default {
  props: {
    currentAudit: {
      type: Object,
      default: () => ({})
    },
    source: {
      type: Object,
      default: () => ({
        type: 'default'
      })
    }
  },
  components: {
    RuleEditor
  },
  data() {
    return {
      checkpointTypeDict,

      fullTable: false,

      searchInfo: {
        checkpointType: ''
      },

      currentRowKey: void 0,
      currentRow: {},

      searchRuleExp: void 0
    }
  },
  computed: {
    checkType() {
      return this.currentAudit.checkType
    },
    sheetProps() {
      const value = {
        title: '审计分配规则',

        layout: 'search,toolbar,table,paging',

        lazy: true,

        api: {
          add: (params) => {
            return addRule({
              ...params,
              checkPoint: this.currentAudit.nodeName,
              checkType: this.checkType,

              modelType: this.currentAudit.modelType,
              riskBasicType: this.currentAudit.checkType
            })
          },
          edit: (params) => updateRule({ ...params,
            modelType: this.currentAudit.modelType,
            riskBasicType: this.currentAudit.checkType
          }),
          list: async(params) => {
            const res = await listRule({
              ...params,
              checkPoint: this.currentAudit.nodeName,
              checkType: this.checkType
            })

            this.currentRowKey = this.tempRowKey || res.rows?.[0]?.id

            return res
          },
          info: async(id) => {
            const res = await getRule(id)
            const data = res?.data || {}

            return {
              ...res,
              data: {
                ...data,
                effectiveTime: [data.startEffectiveTime, data.endEffectiveTime].filter(
                  (item) => !!item
                )
              }
            }
          },
          remove: async(id) => {
            return delRule(id, {
              modelType: this.currentAudit.modelType,
              riskBasicType: this.currentAudit.checkType
            })
          },
          export: '/risk/rule/export',
          import: '',
          template: ''
        },

        tableProps: {
          height: this.fullTable ? '600px' : '300px',
          selection: 'single',
          selectionRowKeys: [this.currentRowKey]
        },

        infoProps: {
          title: true
        },

        hiddenActions: {
          add: !this.$checkPermi(['modelDefine:distribution:show:add']),
          edit: !this.$checkPermi(['modelDefine:distribution:show:edit']),
          remove: !this.$checkPermi(['modelDefine:distribution:show:remove']),
          export: !this.$checkPermi(['modelDefine:distribution:show:export']),
          info: true
        },

        model: {
          ruleNo: {
            type: 'text',
            label: '规则编号',
            align: 'left',
            rules: true,
            search: {
              hidden: true
            }
          },
          ruleName: {
            type: 'text',
            label: '规则名称',
            rules: true,
            width: 150
          },
          ruleLevel: {
            type: 'text',
            label: '优先级',
            table: {
              width: 150
            },
            form: {
              type: 'input-number',
              fieldProps: {
                max: 10000,
                min: 0
              }
            },
            search: {
              hidden: true
            }
          },
          ruleExp: {
            type: 'text',
            label: '表达式',
            form: {
              hidden: true
            },
            table: {
              sort: 85
            },
            search: {
              fieldProps: {
                class: 'el-input--highlight'
              }
            }
          },
          disposeResult: {
            label: '处置结果',
            type: 'select',
            options: this.source.riskStatusDict,
            form: {
              hidden: true
            },
            search: {},
            hidden: ['default'].includes(this.source.type)
          },
          riskOperatorGroupName: {
            type: 'text',
            label: '处置分组',
            table: {
              width: 200
            },
            form: {
              hidden: true
            },
            search: {
              hidden: true
            },
            hidden: ['know', 'judgment'].includes(this.source.type)
          },
          riskOperatorGroupId: {
            type: 'text',
            label: '处置分组',
            hidden: true,
            search: {
              hidden: false
            }
          },
          isEnabled: {
            label: '是否启用',
            search: {
              hidden: true
            },
            form: {
              type: 'switch',
              options: [{ value: 'true' }, { value: 'false' }]
            },
            formatter: (model) => (model.isEnabled == 'true' ? '已启用' : '未启用')
          },
          effectiveTime: {
            type: 'date-time-range',
            label: '有效时间',
            width: 350,
            form: {
              rules: true,
              fieldProps: {
                on: {
                  change: (value, ctx) => {
                    ctx.model.startEffectiveTime = value?.[0]
                    ctx.model.endEffectiveTime = value?.[1]
                  }
                }
              }
            },
            table: {
              formatter: (row) =>
                row.startEffectiveTime
                  ? `${row.startEffectiveTime} 至 ${row.endEffectiveTime}`
                  : ''
            },
            info: {
              formatter: (row) =>
                row.startEffectiveTime
                  ? `${row.startEffectiveTime} 至 ${row.endEffectiveTime}`
                  : '',
              lg: 24
            }
          },
          startEffectiveTime: {
            type: 'date-time',
            label: '开始有效时间',
            width: 200,
            hidden: true
          },
          endEffectiveTime: {
            type: 'date-time',
            label: '截至有效时间',
            width: 200,
            hidden: true
          },
          tenName: {
            type: 'text',
            label: '租户名称',
            hidden: true,
            value: this.$store.getters.userInfo.tenName
          },
          tenId: {
            type: 'text',
            label: '租户名称',
            form: {
              type: 'TenantSelect',
              fieldProps: {
                on: {
                  change(value, label, ctx) {
                    ctx.model.tenName = label
                  }
                }
              }
            },
            table: {
              formatter: (row) => row.tenName,
              width: 150
            },
            search: {
              hidden: true
            },
            value: this.$store.getters.userInfo.tenId
          },
          remark: {
            type: 'text',
            label: '备注',
            width: 150,
            search: {
              hidden: true
            }
          }
        }
      }

      return value
    }
  },
  created() {
    this.onInputNumberChange = debounce(this.onInputNumberChange, 500)
  },
  async mounted() {
    await this.$nextTick()
    this.$refs.sheetRef.getTableData()
  },
  methods: {
    async onInputNumberChange(ruleLevel, row) {
      const params = {
        id: row.id,
        ruleLevel
      }

      const res = await this.sheetProps.api.edit(params)

      if (res.code === 200) {
        this.$message.success(res.msg)
      }
    },
    onCurrentChange(row) {
      this.currentRow = row
      this.tempRowKey = row.id
    },
    async onRuleEditorSuccess() {
      this.$refs.sheetRef.getTableData()
    },
    async onSwitchChange(isEnabled, row) {
      const params = {
        id: row.id,
        isEnabled,
        modelType: this.currentAudit.modelType,
        riskBasicType: this.currentAudit.checkType
      }

      const res = await this.sheetProps.api.edit(params)

      if (res.code !== 200) {
        this.$message.warning(res.msg)
      }
    },
    onQuerySuccess({ lazyModel } = {}) {
      this.searchRuleExp = lazyModel.ruleExp
    },
    isExceeded(row) {
      return dayjs(row.endEffectiveTime).unix() < dayjs().unix()
    }
  }
}
</script>

<style></style>
