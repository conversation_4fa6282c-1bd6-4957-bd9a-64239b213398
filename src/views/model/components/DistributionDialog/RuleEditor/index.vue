<template>
  <div class="h-full">
    <el-divider direction="horizontal" content-position="left">规则编辑区域</el-divider>

    <el-form
      ref="formRef"
      :model="formInfo"
      :rules="formRules"
      label-width="80px"
      label-position="right"
      class=""
      hide-required-asterisk
      :disabled="!currentRow || !currentRow.id"
    >
      <div class="flex items-center">
        <el-form-item label="规则编号" prop="ruleNo" class="">
          <el-input
            v-model="formInfo.ruleNo"
            clearable
            placeholder="请输入"
          />
        </el-form-item>

        <el-form-item v-if="['default'].includes(source.type)" label="处置分组" prop="riskOperatorGroupId" class="">
          <DisposeGroupSelect
            v-model="formInfo.riskOperatorGroupId"
            placeholder="请选择"
            clearable
            :params="{
              riskBasicType: currentAudit.checkType,
            }"
            @change="(value, label) => (formInfo.riskOperatorGroupName = label)"
          />
        </el-form-item>

        <el-form-item v-if="['know', 'judgment'].includes(source.type)" label="处置结果" prop="disposeResult" class="">
          <EleSelectDict
            v-model="formInfo.disposeResult"
            :options="source.riskStatusDict"
          />
        </el-form-item>

        <el-form-item label-width="0" class="!pl-4">
          <el-button :disabled="!$checkPermi(['modelDefine:distribution:show:saveRule'])" icon="el-icon-check" type="success" v-bind="{ loading }" @click="handleSave">
            保存
          </el-button>
        </el-form-item>

      </div>

      <div class="flex items-center">
        <el-form-item label="字段名称" prop="fieldName" class="">
          <el-select v-model="formInfo.fieldName" placeholder="请选择" clearable filterable @change="onFieldChange">
            <el-option
              v-for="(item, index) in fieldColumnList"
              :key="index"
              :label="item.fieldChineseName"
              :value="item.fieldChineseName"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="运算符" prop="fieldOperator" class="">
          <EleSelectDict
            v-model="formInfo.fieldOperator"
            :options="operatorOptions"
            class="!-w-24"
            @change="onFieldOperator"
          />
        </el-form-item>

        <el-form-item label="操作值" prop="fieldValue" class="">
          <SourceField
            :key="[formInfo.fieldOperator,formInfo.fieldName].join(',')"
            v-model="formInfo.fieldValue"
            :type="formInfo.fieldSource"
            :multiple="['in'].includes(formInfo.fieldOperator)"
            collapse-tags
            :dict-options="currentFieldDict"
            class="el-select--nowrap"
            :params="{
              riskBasicType: currentAudit.riskBasicType
            }"
          />
        </el-form-item>

      </div>

      <div class="flex items-center mt-2 mb-4">
        <div class="">
          <el-button-group>
            <el-button
              v-for="(item, index) of shortcutOptions"
              :key="index"
              type="default"
              @click="handleShortcut(item)"
            >
              {{ item.label }}
            </el-button>
          </el-button-group>
        </div>

        <div class="ml-32">
          <el-button :disabled="!$checkPermi(['modelDefine:distribution:show:generateRule'])" icon="el-icon-bottom" type="primary" size="medium" @click="generateExpression">
            生成表达式
          </el-button>
        </div>

      </div>

      <div class="">
        <el-form-item
          label="规则命中表达式（全表达式解析为整体为 true，则表示命中，执行分组策略）"
          label-position="top"
          label-width="auto"
          prop="ruleExp"
        >
          <div class="overflow-hidden pt-2 w-full relative">
            <div v-if="!htmlRuleExp" class="absolute top-4 left-4 text-sm text-gray-500">请输入规则</div>
            <Contenteditable
              v-model="htmlRuleExp"
              tag="div"
              class="min-h-48 max-h-64 overflow-auto rounded px-3 py-2 border border-gray-300 focus:border-primary-500 text-sm font-mono tracking-wide"
              contenteditable
              v-bind="{ noHTML: false }"
            ></Contenteditable>
          </div>

        </el-form-item>
      </div>
    </el-form>
  </div>
</template>

<script>
import EleSelectDict from '@/plugins/element-extends/components/EleField/SelectDict/index.vue'

import { shortcutOptions, operatorOptions, operatorModel } from './config.js'

import { updateRule } from '@/api/audit/distribution.js'

import { listSecurityAuditColumn } from '@/api/audit/template-column.js'

import DisposeGroupSelect from '@/components/business/DisposeGroupSelect/index.vue'

import request from '@/utils/request.js'

import Contenteditable from 'vue-contenteditable/src/contenteditable.vue'

import { stripHtmlTags } from '@/utils/index.js'

export default {
  dicts: [],
  components: {
    EleSelectDict,
    DisposeGroupSelect,
    Contenteditable
  },
  props: {
    currentRow: {
      type: Object,
      default: () => ({})
    },
    currentAudit: {
      type: Object,
      default: () => ({})
    },
    searchRuleExp: {
      type: String,
      default: void 0
    },
    source: {
      type: Object,
      default: () => ({
        type: 'default'
      })
    }
  },
  data() {
    return {
      shortcutOptions,
      operatorOptions,

      loading: false,

      formInfo: {
        disposeResult: void 0,
        fieldName: void 0,
        fieldOperator: '==',
        fieldValue: void 0,
        riskOperatorGroupId: void 0,
        riskOperatorGroupName: void 0,
        ruleNo: '',
        ruleExp: '',
        fieldSource: 'default',
        fieldDict: void 0
      },

      formRules: {
        ruleNo: [{ required: true, message: '该选项不能为空', trigger: 'blur' }],
        fieldName: [{ required: true, message: '该选项不能为空', trigger: 'blur' }],
        fieldOperator: [{ required: true, message: '该选项不能为空', trigger: 'blur' }],
        fieldValue: [{ required: true, message: '该选项不能为空', trigger: 'blur' }]
        // riskOperatorGroupId: [{ required: true, message: '该选项不能为空', trigger: 'blur' }],
        // ruleExp: [{ required: true, message: '该选项不能为空', trigger: 'blur' }],
      },

      fieldColumnList: [],

      currentField: void 0
    }
  },
  computed: {
    currentFieldDict() {
      return this.dict.type[this.formInfo.fieldDict] || []
    },
    operatorParameter() {
      const value = operatorModel[this.formInfo.fieldOperator] || operatorModel.default
      return value.parameter
    },
    autoSpace() {
      if (!this.formInfo.ruleExp || this.formInfo.ruleExp.slice(-1) === ' ') {
        return ''
      }

      return ' '
    },
    htmlRuleExp: {
      get() {
        const keyword = this.searchRuleExp

        return this.formInfo.ruleExp.replaceAll(keyword, `<span class="text-primary-500 font-bold">${keyword}</span>`)
      },
      set(value) {
        this.formInfo.ruleExp = stripHtmlTags(value)
      }
    }
  },
  watch: {
    'currentRow.id': {
      handler(value) {
        if (!value) {
          this.formInfo = this.$options.data().formInfo
          return false
        }

        this.getFormInfo()
      },
      immediate: true
    }
  },
  methods: {
    onFieldChange(value) {
      const field = this.fieldColumnList.find((item) => item.fieldChineseName === value)

      this.formInfo.fieldSource = field?.dataSource || 'default'

      this.formInfo.fieldValue = void 0

      if (!field) {
        return false
      }

      if (['dict'].includes(field.dataSource)) {
        this.dict.init([field.dictType])
        this.formInfo.fieldDict = field.dictType
      }
    },
    onFieldOperator(value) {
      if (!this.formInfo.fieldValue) {
        return false
      }

      if (['in'].includes(value)) {
        this.formInfo.fieldValue = [this.formInfo.fieldValue]
      } else {
        this.formInfo.fieldValue = String(this.formInfo.fieldValue)
      }
    },
    async getFormInfo() {
      await this.getFieldColumnList()

      const firstField = this.fieldColumnList?.[0]
      const fieldDict = firstField?.dictType

      if (fieldDict) {
        this.dict.init([fieldDict])
      }

      this.formInfo = {
        ...this.$options.data().formInfo,
        fieldName: firstField?.fieldChineseName,
        fieldSource: firstField?.dataSource || 'default',
        fieldDict: firstField?.dictType,
        ruleNo: this.currentRow.ruleNo || '',
        ruleExp: this.currentRow.ruleExp || '',
        riskOperatorGroupId: this.currentRow.riskOperatorGroupId,
        disposeResult: this.currentRow.disposeResult
      }
    },

    async getFieldColumnList() {
      const res = await request({
        url: `common/ruleTableFieldInfo/getRuleTableFields/${this.currentAudit.checkType}`,
        method: 'get'
      })

      const rows = res?.data || []

      this.fieldColumnList = rows
    },

    async generateExpression() {
      const errors = []

      await this.$refs.formRef.validateField(
        ['fieldName', 'fieldValue', 'fieldOperator'],
        (err) => {
          if (err) {
            errors.push(error)
          }
        }
      )

      if (errors.length) {
        return false
      }

      this.formInfo.ruleExp +=
          this.autoSpace +
          this.operatorParameter({
            label: this.formInfo.fieldName,
            value: this.formInfo.fieldValue,
            operator: this.formInfo.fieldOperator
          })
    },
    handleShortcut(info) {
      this.formInfo.ruleExp += `${this.autoSpace}${info.value}`
    },

    async handleSave() {
      const errors = []

      await this.$refs.formRef.validateField(['ruleNo'], (err) => {
        if (err) {
          errors.push(error)
        }
      })

      if (errors.length) {
        return false
      }

      this.loading = true

      const res = await updateRule({
        id: this.currentRow.id,
        ruleNo: this.formInfo.ruleNo,
        ruleExp: this.formInfo.ruleExp,
        riskOperatorGroupId: this.formInfo.riskOperatorGroupId,
        riskOperatorGroupName: this.formInfo.riskOperatorGroupName,
        disposeResult: this.formInfo.disposeResult
      }).finally(() => {
        this.loading = false
      })

      if (res.code === 200) {
        this.$message.success(res.msg)
        this.$emit('success')
      }
    }
  }
}
</script>

<style></style>
